Creating an Order
We can now create orders in the Ctechpay gateway now that the service API key has been provided. An order is always necessary before we can take a payment from a customer so that we have something to work with when interacting with the gateway's API.

HTTP Request Method: POST
Resource (URI): https://api-sandbox.ctechpay.com/
Params:
Add these params to your request to create an Order

Parameter (Header)	Value
endpoint	order
Body:
Include the following mandatory JSON data in your request's form or body content.

Parameter	Description	Example Value
token	YOUR_API_TOKEN	UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==
amount	Order amount	100
Note:These are the mandatory minimum input parameters for creating an order using the Ctech Payment Gateway API - many more values are available.

For instance, you may also wish to add some optional attributes

Parameter	Description	Example Value
merchantAttributes	Merchant attributes enable you to include your custom options when the customer is interacting with the payment page. Set to true to enable the following compulsory and optional parameters: (redirectUrl, cancelUrl, cancelText)	true
redirectUrl	A compulsory URL to redirect the user after a payment has been made.	https://example.com/

Note: Only https requests are supported
cancelUrl	A compulsory URL to redirect the user when they choose to cancel the payment.	https://example.com/

Note: Only https requests are supported
redirectUrl	An optional custom text on the cancelUrl link	“Go back to shop,” “Cancel Payment”
Example requests.
Shell
PHP (Curl)
Python
                              
http --ignore-stdin --form --follow --timeout 3600 POST 'https://api-sandbox.ctechpay.com/?endpoint=order' \
'token'='UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==' \
'amount'='100'
                             
                          
Example Response
                            
{
    "order_reference": "0123erf-834fg-bsh73-dhaydm2aa",
    "payment_page_URL": "https://paypage.sandbox.com/?code=6a6a6a5b8hdgd6"
}
                            
                        
Note: For the subsequent steps, you need the order_paypage_URL (hosted payment page URL) values. You might also find it useful to extract and store the order_reference value.`


Overview
This API allows you to retrieve the status of a transaction using its unique identifier (orderRef).

The API is a RESTful API and utilizes HTTP request methods. The API is designed to have predictable, resource-oriented URLs and to use HTTP response codes to indicate API errors. JSON will be returned in all responses from the API, including errors.

HTTP Request Method: POST
Resource (URI): https://api-sandbox.ctechpay.com/get_order_status/
Request Parameters:
Parameter	Type	Value	Description
token	string	UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==	YOUR API KEY
orderRef	string	26fb1c2b-cfa6-5758-f7bk-hjkfhj77	Your Transaction Reference Number
Example requests.
Shell (Httpie)
PHP (Curl)
Python (Requests)
                              
http --ignore-stdin --form --follow --timeout 3600 POST 'https://api-sandbox.ctechpay.com/get_order_status/' \
 'token'='UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==' \
 'orderRef'='69f6c621-0e29-49c0-a88b-f5ad7b1e55bd'
                             
                          
Example Response
                            
{
    "_id": "69f6c621-0e29-49c0-a88b-f5ad7b1e55bd",
    "orderReference": "e58635b5-c3e4-47dc-a72d-fd30d9925a66",
    "status": "PURCHASED",
    "currencyCode": "MWK",
    "amount": 200,
    "formattedAmount": "MWK 200",
    "cardHolderName": "INNOCENT"
}
                            
                        


prompt: using the info in this file, update the card payments system in the database so that:
- User is redirected to the payment page url once they pay.
- Once the user is redirected back to redirecturl, check the status of the payment, and use that information to update the database accordingly
- update user membership once payment is confirmed
- 

MY environment configuration
CTECHPAY_API_KEY=af63128cf669c71e202a665340916c80
CTECHPAY_REGISTRATION=BICT1121
CTECHPAY_ENVIRONMENT=sandbox