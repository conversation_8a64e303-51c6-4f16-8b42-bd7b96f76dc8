# Payment System Implementation Guide

## Overview

The payment system has been successfully implemented to work with the Ctechpay payment gateway. The system supports both cash payments (handled by clerks) and online card payments through Ctechpay.

## Features Implemented

### 1. Payment Flow
- **User initiates payment**: Users can click "Pay Now" on their membership summary page
- **Redirect to payment gateway**: Users are redirected to Ctechpay's secure payment page
- **Payment processing**: Users complete payment on Ctechpay's platform
- **Return handling**: Users are redirected back to the application with payment status
- **Database updates**: Payment status and membership status are automatically updated

### 2. Database Structure
The `payments` table includes all necessary fields:
- `order_reference`: Ctechpay order reference
- `payment_page_URL`: URL to Ctechpay payment page
- `currency_code`: Currency from Ctechpay response
- `formatted_amount`: Formatted amount from Ctechpay
- `card_holder_name`: Cardholder name from Ctechpay
- `status`: Payment status (pending/fulfilled)
- `payment_method`: Method used (cash/ctechpay)

### 3. Configuration
Environment variables are properly configured:
```env
CTECHPAY_API_KEY=af63128cf669c71e202a665340916c80
CTECHPAY_REGISTRATION=BICT1121
CTECHPAY_ENVIRONMENT=sandbox
```

## How It Works

### Payment Initiation
1. User clicks "Pay Now" on membership summary page
2. Frontend calls `POST /payments/ctechpay/initiate` with:
   - `user_id`
   - `membership_id`
   - `fee_type` (registration/affiliation)
   - `amount`

3. Backend creates pending payment record
4. Backend calls Ctechpay API to create order
5. Backend returns payment page URL
6. Frontend redirects user to Ctechpay payment page

### Payment Return
1. User completes payment on Ctechpay
2. Ctechpay redirects to `/payments/ctechpay/return?ref={order_reference}`
3. Backend calls Ctechpay status API to verify payment
4. Backend updates payment record with:
   - Payment status (fulfilled/pending)
   - Currency code
   - Formatted amount
   - Cardholder name
   - Payment timestamp

5. Backend updates membership status:
   - Registration fee → "Registered"
   - Affiliation fee → "Affiliation fee paid"

6. User sees success/failure page with payment details

### Error Handling
- API failures are logged and user-friendly messages shown
- Missing order references are handled gracefully
- Payment verification failures are logged
- Database transaction failures are caught and reported

## API Endpoints

### POST /payments/ctechpay/initiate
Initiates a new Ctechpay payment.

**Request:**
```json
{
    "user_id": 1,
    "membership_id": 1,
    "fee_type": "registration",
    "amount": 100
}
```

**Response:**
```json
{
    "payment_page_url": "https://paypage.sandbox.com/?code=abc123"
}
```

### GET /payments/ctechpay/return
Handles return from Ctechpay payment page.

**Query Parameters:**
- `ref`: Order reference from Ctechpay

**Response:** Inertia page with payment result

### GET /payments/ctechpay/cancel
Handles payment cancellation.

**Response:** Inertia page with cancellation message

## Frontend Components

### summary-membership.jsx
- Displays membership status and payment options
- Handles Ctechpay payment initiation
- Shows loading states during payment processing

### ctechpay-result.jsx
- Displays payment success/failure status
- Shows payment details
- Provides navigation back to membership page

## Testing

Run the payment system tests:
```bash
php artisan test tests/Feature/CtechpayPaymentTest.php
```

Tests cover:
- Payment initiation
- Successful payment return
- Failed payment return
- Database updates
- Membership status updates

## Security Considerations

1. **API Token**: Stored securely in environment variables
2. **HTTPS**: All Ctechpay URLs use HTTPS
3. **Validation**: All input is validated before processing
4. **Logging**: Errors are logged for debugging without exposing sensitive data
5. **Order Reference**: Used to prevent payment tampering

## Troubleshooting

### Common Issues

1. **Payment not updating**: Check logs for API errors
2. **Redirect not working**: Verify return URL configuration
3. **Status not changing**: Ensure membership status logic is correct

### Logs to Check
- Laravel logs: `storage/logs/laravel.log`
- Look for "Ctechpay" entries for payment-related issues

### Environment Verification
Ensure these are set correctly:
- `CTECHPAY_API_KEY`
- `APP_URL` (for return URLs)
- Database connection

## Production Deployment

Before going live:
1. Change `CTECHPAY_ENVIRONMENT` to `production`
2. Update API URLs in config/services.php
3. Test with small amounts first
4. Monitor logs for any issues
5. Ensure SSL certificate is valid for return URLs

## Support

For Ctechpay API issues, contact their support with:
- API key
- Order reference
- Error messages from logs
- Timestamp of issue
