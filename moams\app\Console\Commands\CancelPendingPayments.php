<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Payment;

class CancelPendingPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:cancel-pending {--user_id= : Cancel pending payments for specific user} {--all : Cancel all pending payments}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel pending payments that are stuck';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('all')) {
            $count = Payment::where('status', 'pending')->count();
            // Delete pending payments instead of updating status
            Payment::where('status', 'pending')->delete();
            $this->info("Deleted {$count} pending payments.");
            return;
        }

        if ($userId = $this->option('user_id')) {
            $count = Payment::where('status', 'pending')
                ->where('user_id', $userId)
                ->count();

            // Delete pending payments instead of updating status
            Payment::where('status', 'pending')
                ->where('user_id', $userId)
                ->delete();

            $this->info("Deleted {$count} pending payments for user {$userId}.");
            return;
        }

        // Show pending payments
        $pendingPayments = Payment::where('status', 'pending')
            ->get(['id', 'user_id', 'membership_id', 'fee_type', 'amount', 'created_at']);

        if ($pendingPayments->isEmpty()) {
            $this->info('No pending payments found.');
            return;
        }

        $this->info('Pending payments:');
        $this->table(
            ['ID', 'User ID', 'Membership ID', 'Fee Type', 'Amount', 'Created At'],
            $pendingPayments->map(function (Payment $payment) {
                return [
                    $payment->id,
                    $payment->user_id,
                    $payment->membership_id,
                    $payment->fee_type,
                    $payment->amount,
                    $payment->created_at->format('Y-m-d H:i:s')
                ];
            })
        );

        $this->info('Use --user_id=X to cancel payments for a specific user, or --all to cancel all pending payments.');
    }
}
