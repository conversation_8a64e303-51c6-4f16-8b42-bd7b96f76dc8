<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestCtechpayApi extends Command
{
    protected $signature = 'ctechpay:test {--order-ref= : Test with specific order reference}';
    protected $description = 'Test Ctechpay API connectivity and response format';

    public function handle()
    {
        $orderRef = $this->option('order-ref') ?? 'test-order-123';
        $apiToken = env('CTECHPAY_API_KEY', 'af63128cf669c71e202a665340916c80');
        $statusApiUrl = 'https://api-sandbox.ctechpay.com/get_order_status/';

        $this->info("Testing Ctechpay API...");
        $this->info("API URL: {$statusApiUrl}");
        $this->info("Order Reference: {$orderRef}");
        $this->info("API Token: " . substr($apiToken, 0, 8) . '...');

        try {
            // Test multiple request formats
            $this->testMultipleFormats($statusApiUrl, $apiToken, $orderRef);

        } catch (\Exception $e) {
            $this->error("Exception occurred: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }

        // Test order creation API as well
        $this->info("\n\n=== Testing Order Creation API ===");
        $newOrderRef = $this->testOrderCreation();

        if ($newOrderRef) {
            $this->info("\n\n=== Testing Status Check with Fresh Order ===");
            $this->testStatusWithFreshOrder($statusApiUrl, $apiToken, $newOrderRef);
        }
    }

    private function testMultipleFormats($statusApiUrl, $apiToken, $orderRef)
    {
        // Format 1: Form data (current approach)
        $this->info("\n--- Test 1: Form Data Request ---");
        $response = Http::timeout(15)->asForm()->post($statusApiUrl, [
            'token' => $apiToken,
            'orderRef' => $orderRef,
        ]);
        $this->logResponse("Form Data", $response);

        // Format 2: JSON request
        $this->info("\n--- Test 2: JSON Request ---");
        $response = Http::timeout(15)->post($statusApiUrl, [
            'token' => $apiToken,
            'orderRef' => $orderRef,
        ]);
        $this->logResponse("JSON", $response);

        // Format 3: Query parameters
        $this->info("\n--- Test 3: Query Parameters ---");
        $response = Http::timeout(15)->get($statusApiUrl, [
            'token' => $apiToken,
            'orderRef' => $orderRef,
        ]);
        $this->logResponse("Query Params", $response);

        // Format 4: Different parameter names
        $this->info("\n--- Test 4: Alternative Parameter Names ---");
        $response = Http::timeout(15)->asForm()->post($statusApiUrl, [
            'token' => $apiToken,
            'orderReference' => $orderRef, // Try orderReference instead of orderRef
        ]);
        $this->logResponse("Alt Params", $response);

        // Format 5: With additional headers
        $this->info("\n--- Test 5: With Headers ---");
        $response = Http::timeout(15)
            ->withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json',
            ])
            ->asForm()
            ->post($statusApiUrl, [
                'token' => $apiToken,
                'orderRef' => $orderRef,
            ]);
        $this->logResponse("With Headers", $response);
    }

    private function logResponse($method, $response)
    {
        $this->info("Method: {$method}");
        $this->info("Status Code: " . $response->status());
        $this->info("Response Body: " . $response->body());

        if ($response->successful() && $response->body() !== 'User authentication failed') {
            try {
                $data = $response->json();
                if ($data) {
                    $this->info("✅ SUCCESS! JSON: " . json_encode($data, JSON_PRETTY_PRINT));
                    return true;
                }
            } catch (\Exception $e) {
                // Not JSON, that's ok
            }
        }
        return false;
    }

    private function testOrderCreation()
    {
        $apiToken = env('CTECHPAY_API_KEY', 'af63128cf669c71e202a665340916c80');
        $apiUrl = 'https://api-sandbox.ctechpay.com';
        $redirectUrl = url('/payments/ctechpay/return');
        $cancelUrl = url('/payments/ctechpay/cancel');

        $this->info("Testing Order Creation API...");
        $this->info("API URL: {$apiUrl}/?endpoint=order");
        $this->info("Redirect URL: {$redirectUrl}");
        $this->info("Cancel URL: {$cancelUrl}");

        try {
            $response = Http::asForm()->post($apiUrl . '/?endpoint=order', [
                'token' => $apiToken,
                'amount' => 100,
                'merchantAttributes' => true,
                'redirectUrl' => $redirectUrl,
                'cancelUrl' => $cancelUrl,
                'cancelText' => 'Cancel Payment',
            ]);

            $this->info("Order Creation Response Status: " . $response->status());
            $this->info("Order Creation Response Body: " . $response->body());

            if ($response->successful()) {
                $data = $response->json();
                $this->info("Order Creation JSON: " . json_encode($data, JSON_PRETTY_PRINT));

                if (isset($data['order_reference'])) {
                    $this->info("✅ Order created successfully with reference: " . $data['order_reference']);
                    $this->info("Payment URL: " . ($data['payment_page_URL'] ?? 'NOT FOUND'));
                    return $data['order_reference']; // Return the order reference
                } else {
                    $this->warn("⚠️  Order creation response missing order_reference");
                }
            } else {
                $this->error("Order creation failed");
            }

        } catch (\Exception $e) {
            $this->error("Order creation exception: " . $e->getMessage());
        }

        return null;
    }

    private function testStatusWithFreshOrder($statusApiUrl, $apiToken, $orderRef)
    {
        $this->info("Testing status check with freshly created order: {$orderRef}");

        // Wait a moment for the order to be processed
        sleep(2);

        $response = Http::timeout(15)->asForm()->post($statusApiUrl, [
            'token' => $apiToken,
            'orderRef' => $orderRef,
        ]);

        $this->info("Fresh Order Status Check:");
        $this->info("Status Code: " . $response->status());
        $this->info("Response Body: " . $response->body());

        if ($response->successful() && $response->body() !== 'User authentication failed') {
            try {
                $data = $response->json();
                if ($data) {
                    $this->info("✅ SUCCESS! Fresh order status: " . json_encode($data, JSON_PRETTY_PRINT));
                    return true;
                }
            } catch (\Exception $e) {
                $this->error("Failed to parse JSON: " . $e->getMessage());
            }
        }

        $this->error("❌ Fresh order status check also failed");
        return false;
    }
}
