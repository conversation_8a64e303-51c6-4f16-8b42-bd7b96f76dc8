<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Membership;
use App\Models\Payment;

class TestPayment extends Command
{
    protected $signature = 'test:payment';
    protected $description = 'Test payment functionality';

    public function handle()
    {
        $this->info('Testing payment functionality...');

        try {
            // Get a user and membership for testing
            $user = User::first();
            if (!$user) {
                $this->error('No users found in database');
                return;
            }

            $membership = Membership::where('user_id', $user->id)->first();
            if (!$membership) {
                $this->warn('No memberships found for this user, trying any membership...');
                $membership = Membership::first();
                if (!$membership) {
                    $this->error('No memberships found in database');
                    return;
                }
                $user = $membership->user; // Use the user associated with this membership
            }

            $this->info("Testing with user: {$user->first_name} {$user->last_name}");
            $this->info("Membership ID: {$membership->id}");

            // Test payment data
            $paymentData = [
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => 'registration',
                'amount' => 5000,
            ];

            $this->info('Payment data: ' . json_encode($paymentData));

            // Test Ctechpay configuration
            $this->info('Testing Ctechpay configuration...');
            $apiToken = config('services.ctechpay.token');
            $apiUrl = config('services.ctechpay.api_url');
            $registration = config('services.ctechpay.registration');
            $environment = config('services.ctechpay.environment');

            $this->info("API Token: " . ($apiToken ? 'Set' : 'Not set'));
            $this->info("API URL: {$apiUrl}");
            $this->info("Registration: {$registration}");
            $this->info("Environment: {$environment}");

            // Check for existing payments
            $existingPayment = Payment::where([
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => 'registration',
                'status' => 'fulfilled'
            ])->first();

            if ($existingPayment) {
                $this->warn('Found existing fulfilled payment - this would block new payment');
                $this->info("Existing payment ID: {$existingPayment->id}");
            } else {
                $this->info('No existing fulfilled payments found - payment would be allowed');
            }

            // Check for recent pending payments
            $recentPending = Payment::where([
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => 'registration',
                'status' => 'pending'
            ])->where('created_at', '>', now()->subMinutes(5))->first();

            if ($recentPending) {
                $this->warn('Found recent pending payment - this would block new payment');
                $this->info("Recent pending payment ID: {$recentPending->id}");
            } else {
                $this->info('No recent pending payments found - payment would be allowed');
            }

            $this->info('Payment test completed successfully!');

        } catch (\Exception $e) {
            $this->error('Payment test failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
