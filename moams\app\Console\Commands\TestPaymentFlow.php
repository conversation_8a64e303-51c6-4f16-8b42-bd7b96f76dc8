<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Membership;
use App\Models\Payment;
use App\Http\Controllers\PaymentController;
use Illuminate\Http\Request;

class TestPaymentFlow extends Command
{
    protected $signature = 'test:payment-flow';
    protected $description = 'Test the complete payment flow';

    public function handle()
    {
        $this->info('Testing complete payment flow...');

        try {
            // Get test data
            $user = User::first();
            $membership = Membership::first();
            
            if (!$user || !$membership) {
                $this->error('No user or membership found for testing');
                return;
            }

            $this->info("Testing with user: {$user->first_name} {$user->last_name}");
            $this->info("Membership ID: {$membership->id}");

            // Test payment data
            $paymentData = [
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => 'registration',
                'amount' => 5000,
            ];

            $this->info('Payment data: ' . json_encode($paymentData));

            // Check mock mode
            $mockMode = env('CTECHPAY_MOCK_MODE', false);
            $this->info('Mock mode: ' . ($mockMode ? 'ENABLED' : 'DISABLED'));

            // Create a mock request
            $request = new Request($paymentData);
            $request->setMethod('POST');

            // Create controller instance
            $controller = new PaymentController();

            $this->info('Calling payment initiation...');
            
            // Call the controller method directly
            $response = $controller->initiateCtechpay($request);
            
            $this->info('Response status: ' . $response->getStatusCode());
            $this->info('Response content: ' . $response->getContent());

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getContent(), true);
                
                if (isset($data['payment_page_url']) && $data['payment_page_url']) {
                    $this->info('✅ SUCCESS: Payment URL received: ' . $data['payment_page_url']);
                    
                    // Test if the URL is accessible
                    $this->info('Testing URL accessibility...');
                    $urlParts = parse_url($data['payment_page_url']);
                    $this->info('URL parts: ' . json_encode($urlParts));
                    
                    // Check if route exists
                    $routeExists = \Route::has('ctechpay.redirect');
                    $this->info('Route exists: ' . ($routeExists ? 'YES' : 'NO'));
                    
                } else {
                    $this->error('❌ FAILED: No payment URL in response');
                    $this->error('Response data: ' . json_encode($data));
                }
            } else {
                $this->error('❌ FAILED: HTTP error ' . $response->getStatusCode());
                $this->error('Error content: ' . $response->getContent());
            }

        } catch (\Exception $e) {
            $this->error('❌ EXCEPTION: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
