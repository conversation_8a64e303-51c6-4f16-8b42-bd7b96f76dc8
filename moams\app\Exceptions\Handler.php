<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Handle unauthenticated users.
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        return $request->expectsJson()
            ? response()->json(['message' => $exception->getMessage()], 401)
            : redirect()->guest(route('login'));
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception): SymfonyResponse
    {
        // If the request expects Inertia (AJAX navigation)
        if ($request->hasHeader('X-Inertia')) {
            $status = method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : 500;
            $title = 'Something went wrong';
            $description = 'An unexpected error occurred. Please try again later or contact support if the problem persists.';

            // Optionally, customize for common HTTP errors
            if ($status === 404) {
                $title = 'Page Not Found';
                $description = 'The page you are looking for does not exist.';
            } elseif ($status === 403) {
                $title = 'Forbidden';
                $description = 'You do not have permission to access this page.';
            } elseif ($status === 401) {
                $title = 'Unauthorized';
                $description = 'You must be logged in to access this page.';
            }

            return Inertia::render('error', [
                'status' => $status,
                'title' => $title,
                'description' => $description,
            ])->toResponse($request)->setStatusCode($status);
        }

        // Default to parent behavior for non-Inertia requests
        return parent::render($request, $exception);
    }
}