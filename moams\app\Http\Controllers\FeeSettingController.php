<?php

namespace App\Http\Controllers;

use App\Models\FeeSetting;
use App\Models\User;
use App\Notifications\FeeChangeNotification;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FeeSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:association manager');
    }
    /**
     * Display the fee management dashboard
     */
    public function index()
    {
        $currentFees = [
            'registration' => FeeSetting::getCurrentFee('registration')?->load('createdBy'),
            'affiliation' => FeeSetting::getCurrentFee('affiliation')?->load('createdBy'),
        ];

        // Get all active fees (including future ones)
        $allActiveFees = [
            'registration' => FeeSetting::where('fee_type', 'registration')
                ->where('is_active', true)
                ->with('createdBy')
                ->orderBy('effective_from', 'desc')
                ->get(),
            'affiliation' => FeeSetting::where('fee_type', 'affiliation')
                ->where('is_active', true)
                ->with('createdBy')
                ->orderBy('effective_from', 'desc')
                ->get(),
        ];

        $feeHistory = [
            'registration' => FeeSetting::getFeeHistory('registration'),
            'affiliation' => FeeSetting::getFeeHistory('affiliation'),
        ];

        return Inertia::render('FeeManagement/index', [
            'currentFees' => $currentFees,
            'allActiveFees' => $allActiveFees,
            'feeHistory' => $feeHistory,
        ]);
    }

    /**
     * Show the form for creating a new fee setting
     */
    public function create()
    {
        return Inertia::render('FeeManagement/create');
    }

    /**
     * Store a newly created fee setting
     */
    public function store(Request $request)
    {
        $request->validate([
            'fee_type' => 'required|in:registration,affiliation',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'effective_from' => 'required|date|after_or_equal:today',
        ]);

        try {
            $feeSetting = null;

            DB::transaction(function () use ($request, &$feeSetting) {
                $feeSetting = FeeSetting::createNewFee([
                    'fee_type' => $request->fee_type,
                    'amount' => $request->amount,
                    'description' => $request->description,
                    'effective_from' => $request->effective_from,
                    'created_by' => auth()->id(),
                ]);
            });

            // Send notifications to association clerks and managers
            if ($feeSetting) {
                $notifyUsers = User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['association clerk', 'association manager']);
                })->where('id', '!=', auth()->id())->get();

                foreach ($notifyUsers as $user) {
                    $user->notify(new FeeChangeNotification($feeSetting, 'created'));
                }
            }

            return redirect()->route('fee-settings.index')
                ->with('success', 'Fee setting created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create fee setting. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the specified fee setting
     */
    public function show(FeeSetting $feeSetting)
    {
        $feeSetting->load('createdBy');

        return Inertia::render('FeeManagement/show', [
            'feeSetting' => $feeSetting,
        ]);
    }

    /**
     * Show the form for editing the specified fee setting
     */
    public function edit(FeeSetting $feeSetting)
    {
        $feeSetting->load('createdBy');

        return Inertia::render('FeeManagement/edit', [
            'feeSetting' => $feeSetting,
        ]);
    }

    /**
     * Update the specified fee setting
     */
    public function update(Request $request, FeeSetting $feeSetting)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'effective_from' => 'required|date',
        ]);

        try {
            $newFeeSetting = null;
            $isNewFee = false;

            DB::transaction(function () use ($request, $feeSetting, &$newFeeSetting, &$isNewFee) {
                // If effective date is changing, create a new fee setting
                $effectiveFrom = $feeSetting->effective_from ? Carbon::parse((string) $feeSetting->effective_from)->toDateString() : null;
                if ($request->effective_from !== $effectiveFrom) {
                    $newFeeSetting = FeeSetting::createNewFee([
                        'fee_type' => $feeSetting->fee_type,
                        'amount' => $request->amount,
                        'description' => $request->description,
                        'effective_from' => $request->effective_from,
                        'created_by' => auth()->id(),
                    ]);
                    $isNewFee = true;
                } else {
                    // Update existing fee setting
                    $feeSetting->update([
                        'amount' => $request->amount,
                        'description' => $request->description,
                    ]);
                    $newFeeSetting = $feeSetting->fresh();
                }
            });

            // Send notifications to association clerks and managers
            if ($newFeeSetting) {
                $notifyUsers = User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['association clerk', 'association manager']);
                })->where('id', '!=', auth()->id())->get();

                foreach ($notifyUsers as $user) {
                    $user->notify(new FeeChangeNotification($newFeeSetting, $isNewFee ? 'created' : 'updated'));
                }
            }

            return redirect()->route('fee-settings.index')
                ->with('success', 'Fee setting updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update fee setting. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified fee setting
     */
    public function destroy(FeeSetting $feeSetting)
    {
        try {
            // Only allow deletion of future or inactive fee settings
            if ($feeSetting->is_active && $feeSetting->effective_from <= now()) {
                return redirect()->back()
                    ->with('error', 'Cannot delete active fee settings.');
            }

            $feeSetting->delete();
            return redirect()->route('fee-settings.index')
                ->with('success', 'Fee setting deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete fee setting. Please try again.');
        }
    }

    /**
     * Get fee history for a specific type
     */
    public function getFeeHistory($feeType)
    {
        $history = FeeSetting::getFeeHistory($feeType)
            ->map(function ($fee) {
                return [
                    'id' => $fee->id,
                    'fee_type' => $fee->fee_type,
                    'amount' => (float) $fee->amount,
                    'description' => $fee->description,
                    'effective_from' => $fee->effective_from,
                    'effective_until' => $fee->effective_until,
                    'is_active' => $fee->is_active,
                    'created_by_user' => $fee->createdBy ? [
                        'id' => $fee->createdBy->id,
                        'first_name' => $fee->createdBy->first_name,
                        'last_name' => $fee->createdBy->last_name,
                        'email' => $fee->createdBy->email,
                    ] : null,
                    'created_at' => $fee->created_at,
                    'updated_at' => $fee->updated_at,
                ];
            });

        return Inertia::render('FeeManagement/history', [
            'feeType' => $feeType,
            'history' => $history,
        ]);
    }
}
