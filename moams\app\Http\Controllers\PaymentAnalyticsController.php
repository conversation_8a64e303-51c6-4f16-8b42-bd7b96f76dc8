<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class PaymentAnalyticsController extends Controller
{
    /**
     * Display payment analytics dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        // Only association clerks and system admins can access analytics
        if (!$user->hasAnyRole(['system admin', 'association clerk'])) {
            abort(403, 'Access denied. Only administrators and association clerks can view payment analytics.');
        }

        $period = $request->get('period', 'monthly');
        $year = $request->get('year', now()->year);
        $month = $request->get('month', now()->month);

        $data = [
            'summary' => $this->getPaymentSummary($period, $year, $month),
            'trends' => $this->getPaymentTrends($period, $year, $month),
            'feeTypeBreakdown' => $this->getFeeTypeBreakdown($period, $year, $month),
            'paymentMethodBreakdown' => $this->getPaymentMethodBreakdown($period, $year, $month),
            'topPayingMembers' => $this->getTopPayingMembers($period, $year, $month),
            'selectedPeriod' => $period,
            'selectedYear' => $year,
            'selectedMonth' => $month,
            'userRoles' => $user->roles->pluck('name')->toArray(),
        ];

        return Inertia::render('paymentManagement/analytics', $data);
    }

    /**
     * Generate financial report PDF
     */
    public function generateReport(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->hasAnyRole(['system admin', 'association clerk'])) {
                abort(403, 'Access denied.');
            }

            $period = $request->get('period', 'monthly');
            $year = $request->get('year', now()->year);
            $month = $request->get('month', now()->month);

            // Validate inputs
            if (!in_array($period, ['daily', 'weekly', 'monthly', 'annually'])) {
                return response()->json(['error' => 'Invalid period specified'], 400);
            }

            if (!is_numeric($year) || $year < 2020 || $year > now()->year + 1) {
                return response()->json(['error' => 'Invalid year specified'], 400);
            }

            if ($period === 'monthly' && (!is_numeric($month) || $month < 1 || $month > 12)) {
                return response()->json(['error' => 'Invalid month specified'], 400);
            }

            $trends = $this->getPaymentTrends($period, $year, $month);

            $data = [
                'title' => 'Financial Report - ' . ucfirst($period) . ' ' . ($period === 'monthly' ? Carbon::create($year, $month)->format('F Y') : $year),
                'company_info' => [
                    'name' => 'Minibus Owners Association of Malawi (MOAM)',
                    'address' => 'Lilongwe, Malawi',
                    'phone' => '+265 1 234 567',
                    'email' => '<EMAIL>'
                ],
                'summary' => $this->getPaymentSummary($period, $year, $month),
                'data' => $trends, // This is what the view expects for the detailed table
                'payment_methods' => $this->getPaymentMethodBreakdown($period, $year, $month),
                'top_members' => $this->getTopPayingMembers($period, $year, $month),
                'period' => $period,
                'year' => $year,
                'month' => $month,
                'monthName' => Carbon::create($year, $month)->format('F'),
                'generated_by' => $user->first_name . ' ' . $user->last_name,
                'generated_at' => now()->format('F j, Y \a\t g:i A'),
                'logoBase64' => null, // Skip logo to avoid GD dependency
            ];

            $filename = "financial-report-{$period}-{$year}";
            if ($period === 'monthly') {
                $filename .= "-{$month}";
            }
            $filename .= '.pdf';

            $pdf = Pdf::loadView('reports.financial-report', $data);
            $pdf->setPaper('A4', 'portrait');

            // Set proper headers for PDF download
            return response($pdf->output(), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            \Log::error('Financial report generation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'request_params' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to generate report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment summary statistics
     */
    private function getPaymentSummary($period, $year, $month)
    {
        $baseQuery = Payment::where('status', 'fulfilled');

        if ($period === 'daily') {
            $date = Carbon::create($year, $month)->startOfMonth();
            $baseQuery->whereDate('paid_at', $date);
        } elseif ($period === 'weekly') {
            $startOfWeek = Carbon::create($year, $month)->startOfMonth()->startOfWeek();
            $endOfWeek = Carbon::create($year, $month)->endOfMonth()->endOfWeek();
            $baseQuery->whereBetween('paid_at', [$startOfWeek, $endOfWeek]);
        } elseif ($period === 'monthly') {
            $baseQuery->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'annually') {
            $baseQuery->whereYear('paid_at', $year);
        }

        return [
            'total_revenue' => (clone $baseQuery)->sum('amount'),
            'total_payments' => (clone $baseQuery)->count(),
            'registration_revenue' => (clone $baseQuery)->where('fee_type', 'registration')->sum('amount'),
            'affiliation_revenue' => (clone $baseQuery)->where('fee_type', 'affiliation')->sum('amount'),
            'average_payment' => (clone $baseQuery)->avg('amount') ?: 0,
        ];
    }

    /**
     * Get payment trends over time
     */
    private function getPaymentTrends($period, $year, $month)
    {
        if ($period === 'daily') {
            return $this->getDailyTrends($year, $month);
        } elseif ($period === 'weekly') {
            return $this->getWeeklyTrends($year, $month);
        } elseif ($period === 'monthly') {
            return $this->getMonthlyTrends($year);
        } elseif ($period === 'annually') {
            return $this->getAnnualTrends();
        }

        return [];
    }

    /**
     * Get daily payment trends for a specific month
     */
    private function getDailyTrends($year, $month)
    {
        $startDate = Carbon::create($year, $month)->startOfMonth();
        $endDate = Carbon::create($year, $month)->endOfMonth();

        return Payment::select(
            DB::raw('DATE(paid_at) as date'),
            DB::raw('COUNT(*) as total_payments'),
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount')
        )
            ->where('status', 'fulfilled')
            ->whereBetween('paid_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(paid_at)'))
            ->orderBy('date')
            ->get();
    }

    /**
     * Get weekly payment trends for a specific month
     */
    private function getWeeklyTrends($year, $month)
    {
        $startDate = Carbon::create($year, $month)->startOfMonth();
        $endDate = Carbon::create($year, $month)->endOfMonth();

        return Payment::select(
            DB::raw('WEEK(paid_at, 1) as week_number'),
            DB::raw('COUNT(*) as total_payments'),
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount')
        )
            ->where('status', 'fulfilled')
            ->whereBetween('paid_at', [$startDate, $endDate])
            ->groupBy(DB::raw('WEEK(paid_at, 1)'))
            ->orderBy('week_number')
            ->get();
    }

    /**
     * Get monthly payment trends for a specific year
     */
    private function getMonthlyTrends($year)
    {
        return Payment::select(
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('COUNT(*) as total_payments'),
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount')
        )
            ->where('status', 'fulfilled')
            ->whereYear('paid_at', $year)
            ->groupBy(DB::raw('MONTH(paid_at)'))
            ->orderBy('month')
            ->get();
    }

    /**
     * Get annual payment trends
     */
    private function getAnnualTrends()
    {
        return Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('COUNT(*) as total_payments'),
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount'),
            DB::raw('SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount')
        )
            ->where('status', 'fulfilled')
            ->groupBy(DB::raw('YEAR(paid_at)'))
            ->orderBy('year')
            ->get();
    }

    /**
     * Get fee type breakdown
     */
    private function getFeeTypeBreakdown($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'annually') {
            $query->whereYear('paid_at', $year);
        }

        return $query->select(
            'fee_type',
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(amount) as total_amount')
        )
            ->groupBy('fee_type')
            ->get();
    }

    /**
     * Get payment method breakdown
     */
    private function getPaymentMethodBreakdown($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'annually') {
            $query->whereYear('paid_at', $year);
        }

        return $query->select(
            'payment_method',
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(amount) as total_amount')
        )
            ->groupBy('payment_method')
            ->get();
    }

    /**
     * Get top paying members
     */
    private function getTopPayingMembers($period, $year, $month)
    {
        $query = Payment::with('user')
            ->where('status', 'fulfilled');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'annually') {
            $query->whereYear('paid_at', $year);
        }

        return $query->select(
            'user_id',
            DB::raw('COUNT(*) as payment_count'),
            DB::raw('SUM(amount) as total_paid')
        )
            ->groupBy('user_id')
            ->orderBy('total_paid', 'desc')
            ->limit(10)
            ->get();
    }
}
