<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Http\Requests\StorePaymentRequest;
use App\Http\Requests\UpdatePaymentRequest;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePaymentRequest $request)
    {
        $payment = Payment::create([
            'user_id' => $request->user_id,
            'membership_id' => $request->membership_id,
            'amount' => $request->amount,
            'fee_type' => $request->fee_type, // 'registration' or 'affiliation'
            'status' => $request->status ?? 'pending',
            'payment_method' => $request->payment_method ?? 'cash',
            'paid_at' => $request->status === 'fulfilled' ? now() : null,
        ]);

        // If payment is fulfilled at creation, update membership status and send notifications
        if ($payment->status === 'fulfilled' && $payment->membership) {
            // Update verification details for clerk payments
            $payment->update([
                'verification_method' => 'clerk_manual',
                'verification_notes' => 'Marked as paid by association clerk: ' . auth()->user()->first_name . ' ' . auth()->user()->last_name,
            ]);

            // Update membership status
            $this->updateMembershipStatus($payment);

            // Send payment notifications
            $this->sendPaymentNotifications($payment);

            return redirect()->back()->with('success', 'Payment saved successfully! Confirmation email sent to member.');
        }

        return redirect()->back()->with('success', 'Payment saved successfully!');
    }

    /**
     * Mark a payment as fulfilled and update membership status accordingly.
     */
    public function fulfill(Payment $payment)
    {
        $payment->update([
            'status' => 'fulfilled',
            'paid_at' => now(),
        ]);
        if ($payment->membership) {
            if ($payment->fee_type === 'registration') {
                $payment->membership->update(['status' => 'Registered']);
            } elseif ($payment->fee_type === 'affiliation') {
                $payment->membership->update(['status' => 'Affiliation fee paid']);
            }
        }
        return redirect()->back()->with('success', 'Payment marked as fulfilled and membership updated.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePaymentRequest $request, Payment $payment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        //
    }

    /**
     * Show all unpaid memberships for a given user (for clerks to select and pay).
     */
    public function unpaidMemberships($userId)
    {
        $user = \App\Models\User::with([
            'memberships' => function ($q) {
                $q->orderBy('start_date');
            }
        ])->findOrFail($userId);
        $unpaidMemberships = $user->memberships->filter(function ($membership) {
            return in_array($membership->status, [
                'Unregistered',
                'Need affiliation fee',
                'Affiliation fee not paid',
            ]);
        });
        return \Inertia\Inertia::render('paymentManagement/unpaid-memberships', [
            'user' => $user,
            'unpaidMemberships' => $unpaidMemberships->values(),
        ]);
    }

    /**
     * Show a summary breakdown of all membership years for a given user, including payment info.
     */
    public function membershipSummary($userId)
    {
        // Clear any model cache and force fresh data
        \Illuminate\Support\Facades\Cache::forget("user_memberships_{$userId}");

        $user = \App\Models\User::with([
            'memberships' => function ($q) {
                $q->orderBy('start_date');
            },
            'memberships.payments'
        ])->findOrFail($userId);

        // Log the current membership statuses for debugging
        \Log::info('Loading membership summary', [
            'user_id' => $userId,
            'memberships' => $user->memberships->map(function ($m) {
                return [
                    'id' => $m->id,
                    'type' => $m->type,
                    'status' => $m->status,
                    'updated_at' => $m->updated_at
                ];
            })
        ]);

        return \Inertia\Inertia::render('paymentManagement/membership-summary', [
            'user' => $user,
            'memberships' => $user->memberships,
            'timestamp' => now()->timestamp, // Force cache busting
        ]);
    }

    /**
     * Mark a membership as paid (fulfilled) by a clerk.
     */
    public function markPaid($membershipId)
    {
        $membership = \App\Models\Membership::findOrFail($membershipId);
        $user = $membership->user;
        $feeType = strtolower($membership->type); // e.g., 'registration' or 'affiliation'
        $amount = \App\Models\FeeSetting::getCurrentFeeAmount($feeType);

        // Create payment record
        $payment = \App\Models\Payment::create([
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'amount' => $amount,
            'fee_type' => $feeType,
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
            'verification_method' => 'clerk_manual',
            'verification_notes' => 'Marked as paid by association clerk: ' . auth()->user()->first_name . ' ' . auth()->user()->last_name,
        ]);

        // Update membership status
        $this->updateMembershipStatus($payment);

        // Send payment notifications
        $this->sendPaymentNotifications($payment);

        return redirect()->back()->with('success', 'Membership marked as paid and payment recorded. Confirmation email sent to member.');
    }

    /**
     * Initiate a Ctechpay payment and return the payment page URL.
     */
    public function initiateCtechpay(\Illuminate\Http\Request $request)
    {
        try {
            \Log::info('Ctechpay payment initiation started', ['request_data' => $request->all()]);

            $validated = $request->validate([
                'user_id' => 'required|exists:users,id',
                'membership_id' => 'required|exists:memberships,id',
                'fee_type' => 'required|string',
                'amount' => 'required|numeric|min:1',
            ]);

            \Log::info('Validation passed', ['validated_data' => $validated]);

            // Check for existing fulfilled payments (these should be blocked)
            $fulfilledPayment = Payment::where([
                'user_id' => $validated['user_id'],
                'membership_id' => $validated['membership_id'],
                'fee_type' => $validated['fee_type'],
                'status' => 'fulfilled'
            ])->first();

            if ($fulfilledPayment) {
                return response()->json(['error' => 'This membership fee has already been paid.'], 422);
            }

            // Check for recent pending payments (within last 5 minutes)
            $recentPending = Payment::where([
                'user_id' => $validated['user_id'],
                'membership_id' => $validated['membership_id'],
                'fee_type' => $validated['fee_type'],
                'status' => 'pending'
            ])->where('created_at', '>', now()->subMinutes(5))->first();

            if ($recentPending) {
                return response()->json([
                    'error' => 'A payment for this membership is already in progress. Please wait 5 minutes before trying again, or contact support if you need assistance.',
                    'payment_id' => $recentPending->id,
                    'created_at' => $recentPending->created_at
                ], 422);
            }

            // Delete any old pending payments (older than 5 minutes) for this membership/fee_type
            $deletedCount = Payment::where([
                'user_id' => $validated['user_id'],
                'membership_id' => $validated['membership_id'],
                'fee_type' => $validated['fee_type'],
                'status' => 'pending'
            ])->where('created_at', '<=', now()->subMinutes(5))
                ->delete();

            if ($deletedCount > 0) {
                \Log::info('Old pending payments deleted for retry', [
                    'user_id' => $validated['user_id'],
                    'membership_id' => $validated['membership_id'],
                    'fee_type' => $validated['fee_type'],
                    'deleted_count' => $deletedCount
                ]);
            }

            // Generate order reference
            $orderReference = (string) \Illuminate\Support\Str::uuid();

            // Create payment record (pending)
            $payment = Payment::create([
                'user_id' => $validated['user_id'],
                'membership_id' => $validated['membership_id'],
                'amount' => $validated['amount'],
                'fee_type' => $validated['fee_type'],
                'status' => 'pending',
                'payment_method' => 'ctechpay',
                'order_reference' => $orderReference,
            ]);

            // Call Ctechpay API
            $apiToken = config('services.ctechpay.token');
            $apiUrl = config('services.ctechpay.api_url');

            // Force URLs to use 127.0.0.1 instead of localhost for Ctechpay compatibility
            $redirectUrl = str_replace('localhost', '127.0.0.1', route('payments.ctechpay.return', [], true));
            $cancelUrl = str_replace('localhost', '127.0.0.1', route('payments.ctechpay.cancel', [], true));

            \Log::info('Sending request to Ctechpay', [
                'redirectUrl' => $redirectUrl,
                'cancelUrl' => $cancelUrl,
                'amount' => $validated['amount']
            ]);

            // Check if we should use mock mode for testing
            $useMockMode = env('CTECHPAY_MOCK_MODE', false); // Use real Ctechpay by default

            \Log::info('Mock mode check', [
                'CTECHPAY_MOCK_MODE_env' => env('CTECHPAY_MOCK_MODE'),
                'useMockMode' => $useMockMode,
                'config_app_env' => config('app.env')
            ]);

            if ($useMockMode) {
                // Use mock Ctechpay interface for testing
                $mockPaymentUrl = url('/ctechpay/redirect/' . $payment->order_reference);

                $payment->update([
                    'payment_page_URL' => $mockPaymentUrl,
                ]);

                \Log::info('Using mock Ctechpay interface', [
                    'payment_id' => $payment->id,
                    'order_reference' => $payment->order_reference,
                    'mock_url' => $mockPaymentUrl,
                    'url_parts' => parse_url($mockPaymentUrl),
                    'route_exists' => \Route::has('ctechpay.redirect')
                ]);

                return response()->json([
                    'success' => true,
                    'payment_page_url' => $mockPaymentUrl,
                    'order_reference' => $payment->order_reference,
                ]);
            }

            \Log::info('Making Ctechpay API request', [
                'url' => $apiUrl . '/?endpoint=order',
                'token' => substr($apiToken, 0, 8) . '...',
                'amount' => $validated['amount'],
                'redirectUrl' => $redirectUrl,
                'cancelUrl' => $cancelUrl
            ]);

            $response = Http::timeout(30)->asForm()->post($apiUrl . '/?endpoint=order', [
                'token' => $apiToken,
                'amount' => $validated['amount'],
                'merchantAttributes' => true,
                'redirectUrl' => $redirectUrl,
                'cancelUrl' => $cancelUrl,
                'cancelText' => 'Cancel Payment',
            ]);

            \Log::info('Ctechpay API raw response', [
                'status' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->body(),
                'successful' => $response->successful()
            ]);

            if (!$response->ok()) {
                \Log::error('Ctechpay API error during order creation', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'payment_id' => $payment->id
                ]);
                return response()->json(['error' => 'Failed to create Ctechpay order. Please try again.'], 500);
            }

            $data = $response->json();
            \Log::info('Ctechpay API response', ['response_data' => $data]);

            // Check if we got the expected fields
            if (!isset($data['payment_page_URL']) || !$data['payment_page_URL']) {
                \Log::error('Ctechpay API returned no payment URL', [
                    'response_data' => $data,
                    'payment_id' => $payment->id
                ]);
            }

            if (!isset($data['order_reference']) || !$data['order_reference']) {
                \Log::error('Ctechpay API returned no order reference', [
                    'response_data' => $data,
                    'payment_id' => $payment->id
                ]);
            }

            $payment->update([
                'order_reference' => $data['order_reference'] ?? null,
                'payment_page_URL' => $data['payment_page_URL'] ?? null,
            ]);

            \Log::info('Payment record updated', ['payment_id' => $payment->id, 'order_reference' => $data['order_reference'] ?? null]);

            // Check if we have the required data before returning success
            if (empty($data['payment_page_URL'])) {
                \Log::error('Ctechpay API returned empty payment URL', [
                    'payment_id' => $payment->id,
                    'response_data' => $data
                ]);
                return response()->json([
                    'error' => 'Payment gateway did not provide a payment URL. Please try again or contact support.',
                    'debug_info' => $data
                ], 500);
            }

            // Always return JSON response with payment page URL
            return response()->json([
                'success' => true,
                'payment_page_url' => $data['payment_page_URL'] ?? null,
                'order_reference' => $data['order_reference'] ?? null,
            ]);
        } catch (\Exception $e) {
            \Log::error('Ctechpay error: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function ctechpayReturn(\Illuminate\Http\Request $request)
    {
        \Log::info('NEW VERSION: Ctechpay return handler called', [
            'url' => $request->fullUrl(),
            'query_params' => $request->query(),
            'all_params' => $request->all(),
            'timestamp' => now(),
            'version' => '2.0'
        ]);

        try {
            // Get order reference from query parameters (could be 'ref', 'order_reference', or 'orderRef')
            $orderRef = $request->query('ref') ?? $request->query('order_reference') ?? $request->query('orderRef');

            // Check for explicit failure indicators from Ctechpay
            $status = $request->query('status');
            $success = $request->query('success');
            $error = $request->query('error');
            $cancelled = $request->query('cancelled');

            // Additional parameters that real Ctechpay might send
            $result = $request->query('result');
            $response = $request->query('response');
            $code = $request->query('code');
            $message = $request->query('message');

            // Check for explicit failure indicators (expanded for real Ctechpay)
            $isFailure = $status === 'failed' ||
                $status === 'cancelled' ||
                $success === 'false' ||
                $cancelled === 'true' ||
                $error ||
                $result === 'failed' ||
                $result === 'cancelled' ||
                $response === 'failed' ||
                $code === 'failed' ||
                $message;

            \Log::info('Ctechpay return parameters', [
                'orderRef' => $orderRef,
                'status' => $status,
                'success' => $success,
                'error' => $error,
                'cancelled' => $cancelled,
                'result' => $result,
                'response' => $response,
                'code' => $code,
                'message' => $message,
                'all_params' => $request->all(),
                'query_params' => $request->query(),
                'url' => $request->fullUrl()
            ]);

            // Enhanced failure detection for debugging
            \Log::info('Failure detection check', [
                'status_failed' => $status === 'failed',
                'status_cancelled' => $status === 'cancelled',
                'success_false' => $success === 'false',
                'cancelled_true' => $cancelled === 'true',
                'has_error' => !empty($error),
                'result_failed' => $result === 'failed',
                'response_failed' => $response === 'failed',
                'code_failed' => $code === 'failed',
                'has_message' => !empty($message),
                'will_show_failure' => $isFailure
            ]);

            if (!$orderRef) {
                \Log::warning('Ctechpay return: Missing order reference', ['query_params' => $request->query()]);
                return Inertia::render('paymentManagement/ctechpay-result', [
                    'success' => false,
                    'message' => 'Missing order reference in return URL.'
                ]);
            }

            if ($isFailure) {
                \Log::info('Ctechpay payment explicitly failed', [
                    'orderRef' => $orderRef,
                    'status' => $status,
                    'error' => $error,
                    'result' => $result,
                    'response' => $response,
                    'code' => $code,
                    'message' => $message
                ]);

                $failurePageData = [
                    'success' => false,
                    'message' => $error ?: 'Payment was not successful. Please try again or contact support.',
                    'order_reference' => $orderRef,
                    'error_type' => $status === 'cancelled' ? 'cancelled' : 'failed'
                ];

                \Log::info('Rendering failure page', [
                    'page_data' => $failurePageData,
                    'template' => 'paymentManagement/ctechpay-result'
                ]);

                return Inertia::render('paymentManagement/ctechpay-result', $failurePageData);
            }

            // Find the payment record
            $payment = Payment::where('order_reference', $orderRef)->first();

            if (!$payment) {
                \Log::error('Payment record not found', ['orderRef' => $orderRef]);
                return Inertia::render('paymentManagement/ctechpay-result', [
                    'success' => false,
                    'message' => 'Payment record not found in our system.'
                ]);
            }

            // Automatic verification with multiple strategies (pass failure indicators)
            $failureIndicators = [
                'status' => $status,
                'success' => $success,
                'error' => $error,
                'cancelled' => $cancelled,
                'result' => $result,
                'response' => $response,
                'code' => $code,
                'message' => $message
            ];
            $verificationResult = $this->automaticPaymentVerification($payment, $orderRef, $failureIndicators);

            if ($verificationResult['success']) {
                \Log::info('Automatic verification successful', [
                    'orderRef' => $orderRef,
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                    'verification_method' => $verificationResult['method']
                ]);

                // Update payment as successful
                $payment->update([
                    'status' => 'fulfilled',
                    'paid_at' => now(),
                    'verification_method' => $verificationResult['method'],
                    'verification_notes' => $verificationResult['notes'] ?? null,
                ]);

                // Update membership status
                $this->updateMembershipStatus($payment);



                \Log::info('Payment successful notification', [
                    'user_id' => $payment->user_id,
                    'payment_id' => $payment->id,
                    'amount' => $payment->amount,
                    'fee_type' => $payment->fee_type,
                    'membership_id' => $payment->membership_id
                ]);

                // Store success message in session for display on membership page
                session()->flash('payment_success', [
                    'message' => 'Payment successful! Your membership has been updated. Confirmation emails have been sent.',
                    'amount' => $payment->amount,
                    'timestamp' => now()->toDateTimeString()
                ]);

                return Inertia::render('paymentManagement/ctechpay-result', [
                    'success' => true,
                    'message' => 'Payment successful! Your membership has been updated.',
                    'payment' => $payment->load('membership', 'user'),
                    'order_reference' => $orderRef,
                ]);
            } else {
                // Verification failed - implement retry logic
                \Log::warning('Automatic verification failed, scheduling retry', [
                    'orderRef' => $orderRef,
                    'payment_id' => $payment->id,
                    'verification_error' => $verificationResult['error'] ?? 'Unknown error'
                ]);

                // Temporarily disable background verification job due to API authentication issues
                \Log::info('Background verification disabled due to API authentication issues', [
                    'payment_id' => $payment->id,
                    'order_ref' => $orderRef,
                    'note' => 'Manual verification may be required'
                ]);

                // TODO: Re-enable when API authentication is fixed
                // \App\Jobs\RetryPaymentVerification::dispatch($payment)->delay(now()->addMinutes(5));

                return Inertia::render('paymentManagement/ctechpay-result', [
                    'success' => null,
                    'verification_needed' => true,
                    'message' => 'Payment verification in progress. We are confirming your payment with the payment gateway. You will receive an email confirmation once verified.',
                    'order_reference' => $orderRef,
                    'payment' => $payment->load('membership', 'user'),
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Ctechpay return error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return Inertia::render('paymentManagement/ctechpay-result', [
                'success' => false,
                'message' => 'An error occurred while processing your payment. Please contact support.'
            ]);
        }
    }

    /**
     * Manually verify a payment (for admin use when automatic verification fails)
     */
    public function verifyManually(Payment $payment, \Illuminate\Http\Request $request)
    {
        $validated = $request->validate([
            'status' => 'required|in:fulfilled,pending',
            'notes' => 'nullable|string|max:500'
        ]);

        if ($validated['status'] === 'fulfilled') {
            $payment->update([
                'status' => 'fulfilled',
                'paid_at' => now(),
            ]);

            // Update membership status
            $this->updateMembershipStatus($payment);



            \Log::info('Payment manually verified as successful', [
                'payment_id' => $payment->id,
                'verified_by' => auth()->id(),
                'notes' => $validated['notes'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment has been manually verified as successful.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Payment status updated.'
        ]);
    }

    /**
     * Automatic payment verification with multiple strategies
     */
    private function automaticPaymentVerification($payment, $orderRef, $failureIndicators = [])
    {
        // Strategy 1: Check for explicit failure indicators first
        if ($this->hasFailureIndicators($failureIndicators)) {
            return [
                'success' => false,
                'method' => 'failure_detected',
                'error' => 'Payment failed based on return parameters: ' . json_encode($failureIndicators)
            ];
        }

        // Strategy 2: Try API verification FIRST (most reliable)
        \Log::info('Starting API verification strategy', ['order_ref' => $orderRef]);
        $apiResult = $this->verifyViaAPI($payment, $orderRef);
        \Log::info('API verification result', ['api_result' => $apiResult]);

        if (isset($apiResult['success'])) {
            \Log::info('API verification completed', [
                'success' => $apiResult['success'],
                'verification_needed' => $apiResult['verification_needed'] ?? false,
                'auth_failed' => $apiResult['auth_failed'] ?? false
            ]);

            // If API verification succeeded, return it
            if ($apiResult['success']) {
                return [
                    'success' => $apiResult['success'],
                    'method' => 'api_verification',
                    'notes' => $apiResult['notes'] ?? 'API verification completed',
                    'error' => $apiResult['error'] ?? null,
                    'api_status' => $apiResult['api_status'] ?? null,
                    'response_code' => $apiResult['response_code'] ?? null,
                    'response_message' => $apiResult['response_message'] ?? null,
                    'verification_needed' => $apiResult['verification_needed'] ?? false
                ];
            }

            // If API failed due to authentication, DO NOT use fallback strategies
            if ($apiResult['auth_failed'] ?? false) {
                \Log::error('API verification failed due to authentication - cannot verify payment reliably', [
                    'order_ref' => $orderRef,
                    'api_error' => $apiResult['notes'] ?? 'Unknown error',
                    'action' => 'Marking as verification needed instead of using unreliable fallbacks'
                ]);

                // Return verification needed instead of using unreliable fallback methods
                return [
                    'success' => false,
                    'error' => 'Payment verification failed due to API authentication issues',
                    'verification_needed' => true,
                    'auth_failed' => true
                ];
            }
        }

        // Strategy 3: Conservative fallback - DO NOT assume success without proper verification
        \Log::warning('API verification failed - cannot verify payment status reliably', [
            'order_ref' => $orderRef,
            'payment_id' => $payment->id,
            'note' => 'User returned from payment gateway but API verification failed'
        ]);

        // DO NOT use unreliable fallback strategies that assume success
        // Return verification needed instead of false positive success

        // All strategies failed
        return [
            'success' => false,
            'error' => 'All verification strategies failed',
            'api_error' => $apiResult['error'] ?? null
        ];
    }

    /**
     * Check if failure indicators are present in return parameters
     */
    private function hasFailureIndicators($failureIndicators)
    {
        $status = $failureIndicators['status'] ?? null;
        $success = $failureIndicators['success'] ?? null;
        $error = $failureIndicators['error'] ?? null;
        $cancelled = $failureIndicators['cancelled'] ?? null;
        $result = $failureIndicators['result'] ?? null;
        $response = $failureIndicators['response'] ?? null;
        $code = $failureIndicators['code'] ?? null;
        $message = $failureIndicators['message'] ?? null;

        return $status === 'failed' ||
            $status === 'cancelled' ||
            $success === 'false' ||
            $cancelled === 'true' ||
            !empty($error) ||
            $result === 'failed' ||
            $result === 'cancelled' ||
            $response === 'failed' ||
            $code === 'failed' ||
            !empty($message);
    }

    /**
     * Verify user return from payment gateway (improved logic)
     */
    private function verifyUserReturn($payment, $orderRef)
    {
        // Basic checks
        if (empty($orderRef) || !$payment || $payment->status !== 'pending') {
            return false;
        }

        // Check if user returned within reasonable time (not too quick = likely cancelled)
        $timeSinceCreated = $payment->created_at->diffInSeconds(now());
        $minimumTime = 30; // At least 30 seconds (time to actually make payment)
        $maximumTime = 1800; // Maximum 30 minutes

        \Log::info('User return verification details', [
            'order_ref' => $orderRef,
            'time_since_created' => $timeSinceCreated,
            'minimum_time' => $minimumTime,
            'maximum_time' => $maximumTime,
            'within_time_window' => $timeSinceCreated >= $minimumTime && $timeSinceCreated <= $maximumTime
        ]);

        // IMPORTANT: User return does not guarantee payment success
        // Users can return after cancelling or failing payments too
        \Log::warning('User return verification should not assume success', [
            'order_ref' => $orderRef,
            'time_since_created' => $timeSinceCreated,
            'note' => 'User return alone is insufficient proof of payment success'
        ]);

        // Always return false - user return alone is not sufficient proof of payment success
        return false;
    }

    /**
     * Verify payment via Ctechpay API (using correct documentation endpoint)
     */
    private function verifyViaAPI($payment, $orderRef)
    {
        try {
            // Use the correct Ctechpay status API endpoint from documentation
            $statusApiUrl = 'https://api-sandbox.ctechpay.com/get_order_status/';
            $apiToken = env('CTECHPAY_API_KEY', 'af63128cf669c71e202a665340916c80');

            \Log::info('Querying Ctechpay status API', [
                'order_ref' => $orderRef,
                'api_url' => $statusApiUrl,
                'token' => substr($apiToken, 0, 8) . '...' // Log partial token for debugging
            ]);

            // Query Ctechpay status API using correct format from documentation
            $response = Http::timeout(15)->asForm()->post($statusApiUrl, [
                'token' => $apiToken,
                'orderRef' => $orderRef, // Note: orderRef not orderReference
            ]);

            \Log::info('Ctechpay status API raw response', [
                'order_ref' => $orderRef,
                'status_code' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->body(),
                'successful' => $response->successful()
            ]);

            if ($response->successful()) {
                // Check if response body indicates authentication failure
                $responseBody = $response->body();
                if ($responseBody === 'User authentication failed') {
                    \Log::error('Ctechpay API authentication failed', [
                        'order_ref' => $orderRef,
                        'response_body' => $responseBody,
                        'api_token_prefix' => substr($apiToken, 0, 8) . '...'
                    ]);

                    return [
                        'success' => false,
                        'notes' => 'Ctechpay API authentication failed - cannot verify payment status',
                        'verification_needed' => true,
                        'auth_failed' => true
                    ];
                }

                $data = $response->json();
                \Log::info('Ctechpay status API response', [
                    'order_ref' => $orderRef,
                    'response_data' => $data
                ]);

                // Check payment status from API response (based on documentation)
                $status = $data['status'] ?? null;
                $orderReference = $data['orderReference'] ?? null;
                $amount = $data['amount'] ?? null;
                $cardHolderName = $data['cardHolderName'] ?? null;

                \Log::info('Parsed Ctechpay status response', [
                    'status' => $status,
                    'order_reference' => $orderReference,
                    'amount' => $amount,
                    'card_holder' => $cardHolderName
                ]);

                // Success conditions (based on documentation example: "PURCHASED")
                if ($status === 'PURCHASED' || $status === 'COMPLETED' || $status === 'SUCCESS') {
                    return [
                        'success' => true,
                        'notes' => 'Payment verified as successful via Ctechpay status API',
                        'api_status' => $status,
                        'order_reference' => $orderReference,
                        'amount' => $amount
                    ];
                }

                // Failure conditions
                if ($status === 'FAILED' || $status === 'DECLINED' || $status === 'CANCELLED' || $status === 'REJECTED') {
                    return [
                        'success' => false,
                        'notes' => 'Payment failed according to Ctechpay status API',
                        'api_status' => $status,
                        'order_reference' => $orderReference,
                        'error' => "Payment status: {$status}"
                    ];
                }

                // Unknown status - treat as verification needed
                return [
                    'success' => false,
                    'notes' => "Unknown payment status from API: {$status}",
                    'api_status' => $status,
                    'verification_needed' => true
                ];

            } else {
                \Log::warning('Ctechpay API status query failed', [
                    'order_ref' => $orderRef,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                // Check if it's an authentication issue
                if ($response->body() === 'User authentication failed') {
                    \Log::error('Ctechpay API authentication failed - possible API key issue', [
                        'order_ref' => $orderRef,
                        'api_token_prefix' => substr(env('CTECHPAY_API_KEY', ''), 0, 8) . '...',
                        'response' => $response->body()
                    ]);

                    return [
                        'success' => false,
                        'notes' => 'Ctechpay API authentication failed - API key may be invalid or expired',
                        'verification_needed' => true,
                        'auth_failed' => true
                    ];
                }

                return [
                    'success' => false,
                    'notes' => 'Failed to query Ctechpay API for payment status',
                    'verification_needed' => true
                ];
            }

        } catch (\Exception $e) {
            \Log::error('Ctechpay status API exception', [
                'error' => $e->getMessage(),
                'order_ref' => $orderRef,
                'api_url' => 'https://api-sandbox.ctechpay.com/get_order_status/',
                'exception_type' => get_class($e)
            ]);

            return [
                'success' => false,
                'notes' => 'Exception occurred while querying Ctechpay status API: ' . $e->getMessage(),
                'verification_needed' => true
            ];
        }
    }

    /**
     * Verify payment based on time window (CONSERVATIVE - DO NOT ASSUME SUCCESS)
     */
    private function verifyByTimeWindow($payment)
    {
        // IMPORTANT: Time window alone does not guarantee payment success
        // Payments can fail or be cancelled within any time window

        \Log::warning('verifyByTimeWindow called - this method should not assume payment success', [
            'payment_id' => $payment->id,
            'created_at' => $payment->created_at,
            'note' => 'Time window alone is insufficient proof of payment success'
        ]);

        // Always return false - time window alone is not sufficient proof of payment success
        return false;
    }



    /**
     * Update membership status based on payment type and status
     */
    private function updateMembershipStatus(Payment $payment)
    {
        \Log::info('Updating membership status', [
            'payment_id' => $payment->id,
            'membership_id' => $payment->membership_id,
            'fee_type' => $payment->fee_type,
            'has_membership' => $payment->membership ? 'yes' : 'no'
        ]);

        if (!$payment->membership) {
            \Log::warning('No membership found for payment', ['payment_id' => $payment->id]);
            return;
        }

        $oldStatus = $payment->membership->status;

        if ($payment->fee_type === 'registration') {
            $payment->membership->update(['status' => 'Registered']);
            \Log::info('Updated membership status for registration', [
                'membership_id' => $payment->membership->id,
                'old_status' => $oldStatus,
                'new_status' => 'Registered'
            ]);
        } elseif ($payment->fee_type === 'affiliation') {
            $payment->membership->update(['status' => 'Affiliation fee paid']);
            \Log::info('Updated membership status for affiliation', [
                'membership_id' => $payment->membership->id,
                'old_status' => $oldStatus,
                'new_status' => 'Affiliation fee paid'
            ]);
        } else {
            \Log::warning('Unknown fee type', [
                'payment_id' => $payment->id,
                'fee_type' => $payment->fee_type
            ]);
        }
    }

    /**
     * Send payment success notifications to user and clerks
     */
    private function sendPaymentNotifications(Payment $payment)
    {
        try {
            \Log::info('Sending payment notifications', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'amount' => $payment->amount,
                'fee_type' => $payment->fee_type
            ]);

            // Send notification to the user who made the payment
            $payment->user->notify(new \App\Notifications\PaymentSuccessNotification($payment, false));

            // Send notification to association clerks
            $clerks = \App\Models\User::whereHas('roles', function ($query) {
                $query->where('name', 'association clerk');
            })->get();

            foreach ($clerks as $clerk) {
                $clerk->notify(new \App\Notifications\PaymentSuccessNotification($payment, true));
            }

            \Log::info('Payment notifications sent successfully', [
                'payment_id' => $payment->id,
                'user_notified' => true,
                'clerks_notified' => $clerks->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send payment notifications', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't throw the exception to avoid breaking the payment flow
            // The payment is still successful even if notifications fail
        }
    }
}
