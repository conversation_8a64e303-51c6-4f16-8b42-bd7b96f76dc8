<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Payment;
use App\Http\Controllers\PaymentController;
use App\Notifications\PaymentSuccessNotification;

class RetryPaymentVerification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $payment;
    public $tries = 3; // Retry up to 3 times
    public $backoff = [300, 900, 1800]; // 5 min, 15 min, 30 min delays

    /**
     * Create a new job instance.
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Skip if payment is already fulfilled
        if ($this->payment->status === 'fulfilled') {
            \Log::info('Payment already fulfilled, skipping verification', [
                'payment_id' => $this->payment->id
            ]);
            return;
        }

        \Log::info('Retrying payment verification', [
            'payment_id' => $this->payment->id,
            'order_reference' => $this->payment->order_reference,
            'attempt' => $this->attempts()
        ]);

        // Try API verification
        $result = $this->verifyPaymentViaAPI();

        if ($result['success']) {
            // Payment verified successfully
            $this->payment->update([
                'status' => 'fulfilled',
                'paid_at' => now(),
                'verification_method' => 'background_api',
                'verification_notes' => 'Verified via background job: ' . $result['notes'],
            ]);

            // Update membership status
            $controller = new PaymentController();
            $controller->updateMembershipStatus($this->payment);

            // Send notifications
            $controller->sendPaymentNotifications($this->payment);

            \Log::info('Background payment verification successful', [
                'payment_id' => $this->payment->id,
                'verification_method' => 'background_api'
            ]);

        } else {
            \Log::warning('Background payment verification failed', [
                'payment_id' => $this->payment->id,
                'error' => $result['error'],
                'attempt' => $this->attempts()
            ]);

            // If this is the last attempt, mark as needs manual review
            if ($this->attempts() >= $this->tries) {
                $this->payment->update([
                    'verification_notes' => 'Automatic verification failed after ' . $this->tries . ' attempts. May need manual review.',
                ]);

                \Log::error('Payment verification failed after all retries', [
                    'payment_id' => $this->payment->id,
                    'order_reference' => $this->payment->order_reference
                ]);

                // Optionally notify admins about failed verification
                // You could send an email to clerks here if needed
            }

            // Re-throw exception to trigger retry
            throw new \Exception('Payment verification failed: ' . $result['error']);
        }
    }

    /**
     * Verify payment via API
     */
    private function verifyPaymentViaAPI()
    {
        try {
            // Use the correct Ctechpay status API endpoint (same as PaymentController)
            $statusApiUrl = 'https://api-sandbox.ctechpay.com/get_order_status/';
            $apiToken = config('services.ctechpay.token'); // Use correct config key

            \Log::info('Background job: Querying Ctechpay status API', [
                'order_ref' => $this->payment->order_reference,
                'api_url' => $statusApiUrl,
                'token' => substr($apiToken, 0, 8) . '...'
            ]);

            $response = \Illuminate\Support\Facades\Http::timeout(15)->asForm()->post($statusApiUrl, [
                'token' => $apiToken,
                'orderRef' => $this->payment->order_reference,
            ]);

            \Log::info('Background job: Ctechpay status API raw response', [
                'order_ref' => $this->payment->order_reference,
                'status_code' => $response->status(),
                'body' => $response->body(),
                'successful' => $response->successful()
            ]);

            if ($response->successful()) {
                // Check if response body indicates authentication failure
                $responseBody = $response->body();
                if ($responseBody === 'User authentication failed') {
                    \Log::error('Background job: Ctechpay API authentication failed', [
                        'order_ref' => $this->payment->order_reference,
                        'response_body' => $responseBody
                    ]);

                    return [
                        'success' => false,
                        'error' => 'Ctechpay API authentication failed',
                        'auth_failed' => true
                    ];
                }

                $data = $response->json();
                \Log::info('Background job: Ctechpay status API response', [
                    'order_ref' => $this->payment->order_reference,
                    'response_data' => $data
                ]);

                // Check payment status from API response (based on documentation)
                $status = $data['status'] ?? null;

                // Success conditions (based on documentation example: "PURCHASED")
                if ($status === 'PURCHASED' || $status === 'COMPLETED' || $status === 'SUCCESS') {
                    return [
                        'success' => true,
                        'status' => $status,
                        'notes' => 'API confirmed payment success on retry'
                    ];
                }

                // Failure conditions
                if ($status === 'FAILED' || $status === 'DECLINED' || $status === 'CANCELLED' || $status === 'REJECTED') {
                    return [
                        'success' => false,
                        'error' => "Payment failed according to API: {$status}",
                        'status' => $status
                    ];
                }

                // Unknown status
                return [
                    'success' => false,
                    'error' => "Unknown payment status from API: {$status}",
                    'status' => $status,
                    'verification_needed' => true
                ];

            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to query Ctechpay API for payment status',
                    'http_status' => $response->status()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'API call failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        \Log::error('Payment verification job failed permanently', [
            'payment_id' => $this->payment->id,
            'order_reference' => $this->payment->order_reference,
            'error' => $exception->getMessage()
        ]);
    }
}
