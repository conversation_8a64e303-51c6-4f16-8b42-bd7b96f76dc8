<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int|null $membership_id
 * @property float $amount
 * @property string $fee_type
 * @property string $status
 * @property string $payment_method
 * @property \Carbon\Carbon|null $paid_at
 * @property string|null $order_reference
 * @property string|null $payment_page_URL
 * @property string|null $currency_code
 * @property string|null $formatted_amount
 * @property string|null $card_holder_name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \App\Models\Membership|null $membership
 * @property-read \App\Models\User $user
 */
class Payment extends Model
{
    /** @use HasFactory<\Database\Factories\PaymentFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'membership_id',
        'amount',
        'fee_type',
        'status',
        'payment_method',
        'paid_at',
        'order_reference',
        'payment_page_URL',
        'currency_code',
        'formatted_amount',
        'card_holder_name',
    ];

    protected $casts = [
        'payment_date' => 'datetime',
    ];

    public function membership(): BelongsTo
    {
        return $this->belongsTo(Membership::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
