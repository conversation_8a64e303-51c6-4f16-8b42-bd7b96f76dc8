<?php

namespace App\Notifications;

use App\Models\DriverClearanceRequest;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;

class DriverClearanceSuccessNotification extends Notification implements ShouldQueue
{
    // Made queueable for better reliability and performance

    public $clearanceRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct(DriverClearanceRequest $clearanceRequest)
    {
        $this->clearanceRequest = $clearanceRequest;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $driver = $this->clearanceRequest->driver;
        $owner = $this->clearanceRequest->owner;
        $processedBy = $this->clearanceRequest->processedBy;

        $notificationData = [
            'badge' => ['type' => 'success', 'text' => 'Request Processed'],
            'greeting' => "Hello {$owner->first_name},",
            'message' => 'Your driver clearance request has been successfully processed by our association team. The driver has been cleared and is no longer associated with your account.',
            'details' => [
                [
                    'title' => 'Driver Information',
                    'icon' => '🚗',
                    'items' => [
                        ['label' => 'Driver Name', 'value' => $driver->first_name . ' ' . $driver->last_name],
                        ['label' => 'License Number', 'value' => $driver->license_number ?? 'Not provided'],
                        ['label' => 'Phone', 'value' => $driver->phone_number ?? 'Not provided']
                    ]
                ],
                [
                    'title' => 'Processing Details',
                    'icon' => '📋',
                    'items' => [
                        ['label' => 'Processed By', 'value' => $processedBy->first_name . ' ' . $processedBy->last_name],
                        ['label' => 'Processed On', 'value' => $this->clearanceRequest->processed_at->format('F j, Y \a\t g:i A')],
                        ['label' => 'Original Reason', 'value' => $this->clearanceRequest->reason]
                    ]
                ]
            ],
            'additionalMessages' => [
                ['type' => 'divider'],
                ['title' => 'What\'s Next?', 'content' => 'The driver clearance has been completed successfully. You can now add new drivers to replace the cleared one if needed. The driver\'s employment history has been updated in our system.'],
                ['content' => 'If you have any questions about this clearance or need to add new drivers, please contact our support team or log into your MOAMS account.'],
                ['content' => 'Thank you for using MOAMS!']
            ]
        ];

        return (new MailMessage)
            ->subject('Driver Clearance Completed Successfully')
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => 'Driver Clearance Completed Successfully',
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        $driver = $this->clearanceRequest->driver;
        $processedBy = $this->clearanceRequest->processedBy;

        return [
            'clearance_request_id' => $this->clearanceRequest->id,
            'driver_id' => $driver->id,
            'driver_name' => "{$driver->first_name} {$driver->last_name}",
            'processed_by' => "{$processedBy->first_name} {$processedBy->last_name}",
            'processed_at' => $this->clearanceRequest->processed_at,
        ];
    }
}
