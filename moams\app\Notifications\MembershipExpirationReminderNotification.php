<?php

namespace App\Notifications;

use App\Models\Membership;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;

class MembershipExpirationReminderNotification extends Notification implements ShouldQueue
{
    public $membership;
    public $daysUntilExpiration;
    public $reminderType;

    /**
     * Create a new notification instance.
     */
    public function __construct(Membership $membership, int $daysUntilExpiration, string $reminderType = 'standard')
    {
        $this->membership = $membership;
        $this->daysUntilExpiration = $daysUntilExpiration;
        $this->reminderType = $reminderType; // 'early', 'standard', 'urgent', 'final'
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $user = $this->membership->user;
        $subject = $this->getSubject();
        $notificationData = $this->buildNotificationData();

        return (new MailMessage)
            ->subject($subject)
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => $subject,
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    /**
     * Get the subject line based on reminder type
     */
    private function getSubject(): string
    {
        $membershipType = ucfirst($this->membership->type);

        switch ($this->reminderType) {
            case 'early':
                return "Early Reminder: {$membershipType} Membership Expires in {$this->daysUntilExpiration} Days";
            case 'urgent':
                return "Urgent: {$membershipType} Membership Expires in {$this->daysUntilExpiration} Days";
            case 'final':
                return "Final Notice: {$membershipType} Membership Expires Tomorrow";
            default:
                return "{$membershipType} Membership Expiration Reminder";
        }
    }

    /**
     * Build notification data for email template
     */
    private function buildNotificationData(): array
    {
        $user = $this->membership->user;
        $membershipType = ucfirst($this->membership->type);

        $urgencyLevel = $this->getUrgencyLevel();
        $message = $this->getMessage();
        $actionText = $this->getActionText();

        return [
            'greeting' => "Hello {$user->first_name},",
            'message' => $message,
            'badge' => [
                'type' => $urgencyLevel['badge_type'],
                'text' => $urgencyLevel['badge_text']
            ],
            'details' => [
                [
                    'title' => 'Membership Information',
                    'icon' => '📋',
                    'items' => [
                        ['label' => 'Membership Type', 'value' => $membershipType],
                        ['label' => 'Current Status', 'value' => $this->membership->status],
                        ['label' => 'Expiration Date', 'value' => $this->membership->end_date->format('F j, Y')],
                        ['label' => 'Days Until Expiration', 'value' => $this->daysUntilExpiration . ' days'],
                    ]
                ],
                [
                    'title' => 'Renewal Information',
                    'icon' => '💳',
                    'items' => [
                        ['label' => 'Renewal Fee', 'value' => $this->getRenewalFeeAmount()],
                        ['label' => 'Payment Methods', 'value' => 'Cash, Mobile Money, Online Payment'],
                        ['label' => 'Grace Period', 'value' => '30 days after expiration'],
                    ]
                ]
            ],
            'action' => [
                'url' => route('membership.summary'),
                'text' => $actionText
            ],
            'additionalMessages' => $this->getAdditionalMessages()
        ];
    }

    /**
     * Get urgency level configuration
     */
    private function getUrgencyLevel(): array
    {
        switch ($this->reminderType) {
            case 'early':
                return [
                    'badge_type' => 'info',
                    'badge_text' => 'Early Reminder'
                ];
            case 'urgent':
                return [
                    'badge_type' => 'warning',
                    'badge_text' => 'Urgent Action Required'
                ];
            case 'final':
                return [
                    'badge_type' => 'error',
                    'badge_text' => 'Final Notice'
                ];
            default:
                return [
                    'badge_type' => 'info',
                    'badge_text' => 'Membership Reminder'
                ];
        }
    }

    /**
     * Get main message based on reminder type
     */
    private function getMessage(): string
    {
        $membershipType = strtolower($this->membership->type);

        switch ($this->reminderType) {
            case 'early':
                return "This is an early reminder that your {$membershipType} membership will expire in {$this->daysUntilExpiration} days. We recommend renewing early to avoid any service interruptions.";
            case 'urgent':
                return "Your {$membershipType} membership will expire in {$this->daysUntilExpiration} days. Please renew as soon as possible to maintain your membership benefits and avoid late fees.";
            case 'final':
                return "This is your final notice! Your {$membershipType} membership expires tomorrow. Renew immediately to avoid suspension of your membership and associated services.";
            default:
                return "Your {$membershipType} membership will expire in {$this->daysUntilExpiration} days. Please renew your membership to continue enjoying all member benefits.";
        }
    }

    /**
     * Get action button text
     */
    private function getActionText(): string
    {
        switch ($this->reminderType) {
            case 'urgent':
            case 'final':
                return 'Renew Now';
            default:
                return 'View Membership Details';
        }
    }

    /**
     * Get additional messages based on reminder type
     */
    private function getAdditionalMessages(): array
    {
        $baseMessages = [
            ['type' => 'divider'],
            ['title' => 'Why Renew?', 'content' => 'Maintaining your membership ensures continued access to MOAM services, support, and benefits. It also helps us continue providing valuable services to the minibus transport community.']
        ];

        switch ($this->reminderType) {
            case 'urgent':
                return array_merge($baseMessages, [
                    ['content' => 'Late renewal may result in additional fees and temporary suspension of services.'],
                    ['content' => 'If you have any questions or need assistance with renewal, please contact our support team immediately.']
                ]);
            case 'final':
                return array_merge($baseMessages, [
                    ['content' => '⚠️ WARNING: Failure to renew by the expiration date will result in immediate suspension of your membership and all associated services.'],
                    ['content' => 'Contact our support team immediately if you need assistance or have payment difficulties.']
                ]);
            default:
                return array_merge($baseMessages, [
                    ['content' => 'You can renew online through your MOAMS dashboard or visit our offices for in-person payment.'],
                    ['content' => 'Thank you for being a valued member of MOAM!']
                ]);
        }
    }

    /**
     * Get renewal fee amount
     */
    private function getRenewalFeeAmount(): string
    {
        $feeType = strtolower($this->membership->type);
        $feeSetting = \App\Models\FeeSetting::getCurrentFee($feeType);

        if ($feeSetting) {
            return 'MWK ' . number_format($feeSetting->amount, 2);
        }

        return 'Contact office for current rates';
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'membership_id' => $this->membership->id,
            'membership_type' => $this->membership->type,
            'days_until_expiration' => $this->daysUntilExpiration,
            'reminder_type' => $this->reminderType,
            'expiration_date' => $this->membership->end_date->toDateString(),
            'type' => 'membership_expiration_reminder',
            'title' => $this->getSubject(),
            'message' => "Your {$this->membership->type} membership expires in {$this->daysUntilExpiration} days.",
        ];
    }
}
