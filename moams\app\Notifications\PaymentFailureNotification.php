<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;

class PaymentFailureNotification extends Notification implements ShouldQueue
{
    public $payment;
    public $isForClerk;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, bool $isForClerk = false)
    {
        $this->payment = $payment;
        $this->isForClerk = $isForClerk;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $payment = $this->payment;
        $user = $payment->user;

        $subject = $this->isForClerk
            ? 'Payment Failure Alert - ' . $user->first_name . ' ' . $user->last_name
            : 'Payment Failed - Please Try Again';

        $notificationData = $this->buildPaymentFailureNotificationData($payment, $user);

        return (new MailMessage)
            ->subject($subject)
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => $subject,
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    /**
     * Build notification data for payment failure
     */
    private function buildPaymentFailureNotificationData(Payment $payment, $user): array
    {
        if ($this->isForClerk) {
            return $this->buildClerkNotificationData($payment, $user);
        } else {
            return $this->buildUserNotificationData($payment, $user);
        }
    }

    /**
     * Build notification data for user
     */
    private function buildUserNotificationData(Payment $payment, $user): array
    {
        return [
            'greeting' => "Hello {$user->first_name},",
            'message' => 'We encountered an issue processing your payment. Don\'t worry - no charges have been made to your account. Please try again or contact our support team for assistance.',
            'badge' => [
                'type' => 'error',
                'text' => 'Payment Failed'
            ],
            'details' => [
                [
                    'title' => 'Payment Information',
                    'icon' => '💳',
                    'items' => [
                        ['label' => 'Amount', 'value' => 'MWK ' . number_format($payment->amount, 2)],
                        ['label' => 'Fee Type', 'value' => ucfirst($payment->fee_type)],
                        ['label' => 'Payment Method', 'value' => ucfirst(str_replace('_', ' ', $payment->payment_method))],
                        ['label' => 'Attempt Date', 'value' => $payment->created_at->format('F j, Y \a\t g:i A')],
                        ['label' => 'Order Reference', 'value' => $payment->order_reference ?? 'N/A'],
                    ]
                ],
                [
                    'title' => 'Failure Details',
                    'icon' => '⚠️',
                    'items' => [
                        ['label' => 'Reason', 'value' => $this->getFailureReason($payment)],
                        ['label' => 'Retry Count', 'value' => $payment->retry_count],
                        ['label' => 'Status', 'value' => 'Failed'],
                    ]
                ]
            ],
            'action' => [
                'url' => route('membership.summary'),
                'text' => 'Try Payment Again'
            ],
            'additionalMessages' => [
                ['type' => 'divider'],
                ['title' => 'What to do next?', 'content' => 'You can try making the payment again using the same or a different payment method. If the problem persists, please contact our support team.'],
                ['content' => 'Common solutions: Check your internet connection, verify your payment details, or try a different payment method.'],
                ['content' => 'If you need immediate assistance, please contact <NAME_EMAIL> or call +265 1 234 567.']
            ]
        ];
    }

    /**
     * Build notification data for clerk
     */
    private function buildClerkNotificationData(Payment $payment, $user): array
    {
        return [
            'greeting' => 'Hello,',
            'message' => 'A payment has failed after multiple retry attempts and requires manual attention. Please review the details below and follow up with the member if necessary.',
            'badge' => [
                'type' => 'error',
                'text' => 'Payment Failure Alert'
            ],
            'details' => [
                [
                    'title' => 'Member Information',
                    'icon' => '👤',
                    'items' => [
                        ['label' => 'Name', 'value' => $user->first_name . ' ' . $user->last_name],
                        ['label' => 'Email', 'value' => $user->email],
                        ['label' => 'Phone', 'value' => $user->phone ?? 'Not provided'],
                        ['label' => 'Member ID', 'value' => $user->id],
                    ]
                ],
                [
                    'title' => 'Payment Details',
                    'icon' => '💳',
                    'items' => [
                        ['label' => 'Payment ID', 'value' => $payment->id],
                        ['label' => 'Amount', 'value' => 'MWK ' . number_format($payment->amount, 2)],
                        ['label' => 'Fee Type', 'value' => ucfirst($payment->fee_type)],
                        ['label' => 'Payment Method', 'value' => ucfirst(str_replace('_', ' ', $payment->payment_method))],
                        ['label' => 'Order Reference', 'value' => $payment->order_reference ?? 'N/A'],
                    ]
                ],
                [
                    'title' => 'Failure Information',
                    'icon' => '⚠️',
                    'items' => [
                        ['label' => 'Failure Reason', 'value' => $this->getFailureReason($payment)],
                        ['label' => 'Retry Count', 'value' => $payment->retry_count],
                        ['label' => 'First Attempt', 'value' => $payment->created_at->format('F j, Y \a\t g:i A')],
                        ['label' => 'Last Retry', 'value' => $payment->last_retry_at ? $payment->last_retry_at->format('F j, Y \a\t g:i A') : 'N/A'],
                    ]
                ]
            ],
            'action' => [
                'url' => route('payment-management.show', $payment->id),
                'text' => 'Review Payment Details'
            ],
            'additionalMessages' => [
                ['type' => 'divider'],
                ['title' => 'Recommended Actions', 'content' => '1. Contact the member to verify their payment details and assist with the payment process.'],
                ['content' => '2. Check if there are any technical issues with the payment gateway.'],
                ['content' => '3. Consider offering alternative payment methods if the issue persists.'],
                ['content' => '4. Update the payment status manually if the payment was completed through other means.']
            ]
        ];
    }

    /**
     * Get user-friendly failure reason
     */
    private function getFailureReason(Payment $payment): string
    {
        if ($payment->failure_reason) {
            // Map technical errors to user-friendly messages
            $reason = strtolower($payment->failure_reason);

            if (str_contains($reason, 'timeout')) {
                return 'Payment gateway timeout - please try again';
            } elseif (str_contains($reason, 'insufficient')) {
                return 'Insufficient funds';
            } elseif (str_contains($reason, 'declined')) {
                return 'Payment declined by bank';
            } elseif (str_contains($reason, 'network')) {
                return 'Network connection issue';
            } elseif (str_contains($reason, 'api')) {
                return 'Payment gateway error';
            } else {
                return $payment->failure_reason;
            }
        }

        return 'Unknown error - please contact support';
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'fee_type' => $this->payment->fee_type,
            'user_name' => $this->payment->user->first_name . ' ' . $this->payment->user->last_name,
            'failure_reason' => $this->payment->failure_reason,
            'retry_count' => $this->payment->retry_count,
            'type' => 'payment_failure',
            'title' => $this->isForClerk ? 'Payment Failure Alert' : 'Payment Failed',
            'message' => $this->isForClerk
                ? 'Payment failed after multiple attempts - manual review required'
                : 'Your payment could not be processed - please try again',
        ];
    }
}
