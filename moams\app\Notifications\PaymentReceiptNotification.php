<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;

class PaymentReceiptNotification extends Notification implements ShouldQueue
{
    public $payment;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $payment = $this->payment;
        $user = $payment->user;

        $notificationData = [
            'greeting' => "Hello {$user->first_name},",
            'message' => 'Thank you for your payment! Please find your official receipt attached to this email. This receipt serves as proof of payment and should be kept for your records.',
            'details' => [
                [
                    'title' => 'Payment Receipt Details',
                    'icon' => '🧾',
                    'items' => [
                        ['label' => 'Receipt Number', 'value' => $payment->receipt_number],
                        ['label' => 'Amount Paid', 'value' => 'MWK ' . number_format($payment->amount, 2)],
                        ['label' => 'Fee Type', 'value' => ucfirst($payment->fee_type)],
                        ['label' => 'Payment Method', 'value' => ucfirst(str_replace('_', ' ', $payment->payment_method))],
                        ['label' => 'Payment Date', 'value' => $payment->paid_at->format('F j, Y \a\t g:i A')],
                    ]
                ],
                [
                    'title' => 'Member Information',
                    'icon' => '👤',
                    'items' => [
                        ['label' => 'Name', 'value' => $user->first_name . ' ' . $user->last_name],
                        ['label' => 'Email', 'value' => $user->email],
                        ['label' => 'Membership Status', 'value' => $payment->membership ? $payment->membership->status : 'N/A']
                    ]
                ]
            ],
            'action' => [
                'url' => route('payments.receipt.download', $payment->id),
                'text' => 'Download Receipt'
            ],
            'additionalMessages' => [
                ['type' => 'divider'],
                ['title' => 'Important Notes', 'content' => 'Please keep this receipt for your records. You can also download it anytime from your MOAMS dashboard.'],
                ['content' => 'If you have any questions about this payment or need assistance, please contact our support team.'],
                ['content' => 'Thank you for being a valued member of MOAM!']
            ]
        ];

        $mail = (new MailMessage)
            ->subject('Payment Receipt - ' . $payment->receipt_number)
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => 'Payment Receipt - ' . $payment->receipt_number,
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);

        // Attach receipt PDF if it exists
        if ($payment->receipt_file_path && Storage::disk('local')->exists($payment->receipt_file_path)) {
            $mail->attach(Storage::disk('local')->path($payment->receipt_file_path), [
                'as' => 'Receipt_' . $payment->receipt_number . '.pdf',
                'mime' => 'application/pdf',
            ]);
        }

        return $mail;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'receipt_number' => $this->payment->receipt_number,
            'amount' => $this->payment->amount,
            'fee_type' => $this->payment->fee_type,
            'type' => 'payment_receipt',
            'title' => 'Payment Receipt Available',
            'message' => 'Your payment receipt for ' . ucfirst($this->payment->fee_type) . ' fee is now available for download.',
        ];
    }
}
