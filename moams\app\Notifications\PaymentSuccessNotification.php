<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Payment;

class PaymentSuccessNotification extends Notification implements ShouldQueue
{
    // Queueable notification for better reliability

    public $connection = 'sync'; // Use sync connection for immediate sending
    public $queue = 'default';
    public $tries = 3;
    public $timeout = 60;
    public $delay = 0; // No delay for immediate sending

    protected $payment;
    protected $isForClerk;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, bool $isForClerk = false)
    {
        $this->payment = $payment;
        $this->isForClerk = $isForClerk;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $payment = $this->payment;
        $user = $payment->user;
        $membership = $payment->membership;

        $subject = $this->isForClerk
            ? 'New Payment Received - ' . $user->first_name . ' ' . $user->last_name
            : 'Payment Confirmation - ' . ucfirst($payment->fee_type) . ' Fee';

        $notificationData = $this->buildPaymentNotificationData($payment, $user, $membership);

        return (new MailMessage)
            ->subject($subject)
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => $subject,
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    /**
     * Build notification data for payment emails
     */
    private function buildPaymentNotificationData($payment, $user, $membership)
    {
        $data = [
            'badge' => [
                'type' => $this->isForClerk ? 'warning' : 'success',
                'text' => $this->isForClerk ? 'New Payment Alert' : 'Payment Successful'
            ],
            'greeting' => $this->isForClerk
                ? 'Hello,'
                : "Hello {$user->first_name},",
            'message' => $this->isForClerk
                ? 'A new payment has been successfully processed in the MOAMS system. Please review the member details below and take any necessary follow-up actions.'
                : 'Congratulations! Your payment has been successfully processed. Your membership status has been updated and you can now enjoy all the benefits of your membership.',
            'details' => [
                [
                    'title' => 'Payment Details',
                    'icon' => '💳',
                    'items' => [
                        ['label' => 'Amount', 'value' => 'MWK ' . number_format($payment->amount, 2)],
                        ['label' => 'Fee Type', 'value' => ucfirst($payment->fee_type)],
                        ['label' => 'Order Reference', 'value' => $payment->order_reference],
                        ['label' => 'Payment Date', 'value' => $payment->paid_at ? \Carbon\Carbon::parse($payment->paid_at)->format('F j, Y \a\t g:i A') : 'Not yet paid']
                    ]
                ],
                [
                    'title' => 'Membership Information',
                    'icon' => '📋',
                    'items' => [
                        ['label' => 'Year', 'value' => $membership->start_date->format('Y')],
                        ['label' => 'Type', 'value' => ucfirst($membership->type)],
                        ['label' => 'Status', 'value' => $membership->status]
                    ]
                ]
            ]
        ];

        // Add member details for clerk emails
        if ($this->isForClerk) {
            array_splice($data['details'], 1, 0, [
                [
                    'title' => 'Member Information',
                    'icon' => '👤',
                    'items' => [
                        ['label' => 'Name', 'value' => $user->first_name . ' ' . $user->last_name],
                        ['label' => 'Email', 'value' => $user->email],
                        ['label' => 'Phone', 'value' => $user->phone ?? 'Not provided']
                    ]
                ]
            ]);

            $data['action'] = [
                'url' => route('notification.redirect', ['type' => 'member-details', 'id' => $user->id]),
                'text' => 'View Member Details'
            ];

            $data['additionalMessages'] = [
                ['content' => 'The member\'s status has been automatically updated in the system. Please verify the payment details and contact the member if any follow-up is needed.']
            ];
        } else {
            $data['additionalMessages'] = [
                ['type' => 'divider'],
                ['title' => 'What\'s Next?', 'content' => 'Your membership is now active and you can access all member benefits. If you have any questions or need assistance, please don\'t hesitate to contact our support team.'],
                ['content' => 'Thank you for choosing MOAMS!']
            ];
        }

        return $data;
    }



    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'fee_type' => $this->payment->fee_type,
            'user_name' => $this->payment->user->first_name . ' ' . $this->payment->user->last_name,
        ];
    }
}
