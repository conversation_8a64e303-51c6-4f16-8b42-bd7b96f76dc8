<?php

namespace App\Notifications;

use App\Models\MinibusTransferRequest;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;

class TransferSuccessNotification extends Notification implements ShouldQueue
{
    // Made queueable for better reliability and performance

    public $transferRequest;

    public function __construct(MinibusTransferRequest $transferRequest)
    {
        $this->transferRequest = $transferRequest;
    }

    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable): MailMessage
    {
        $minibus = $this->transferRequest->minibus;
        $owner = $this->transferRequest->owner;

        $details = [
            [
                'title' => 'Minibus Information',
                'icon' => '🚐',
                'items' => [
                    ['label' => 'Number Plate', 'value' => $minibus->number_plate],
                    ['label' => 'Make & Model', 'value' => $minibus->make . ' ' . $minibus->model],
                    ['label' => 'Year', 'value' => $minibus->year ?? 'Not specified'],
                    ['label' => 'Route', 'value' => $minibus->route ?? 'Not specified']
                ]
            ],
            [
                'title' => 'Transfer Details',
                'icon' => '📋',
                'items' => [
                    ['label' => 'Transfer Type', 'value' => ucfirst($this->transferRequest->transfer_type)],
                    ['label' => 'Processed On', 'value' => $this->transferRequest->processed_at->format('F j, Y \a\t g:i A')],
                    ['label' => 'Original Request', 'value' => $this->transferRequest->created_at->format('F j, Y \a\t g:i A')]
                ]
            ]
        ];

        // Add new owner info for internal transfers
        if ($this->transferRequest->transfer_type === 'internal' && $this->transferRequest->new_owner_id) {
            $details[1]['items'][] = [
                'label' => 'New Owner',
                'value' => ($this->transferRequest->newOwner->first_name ?? 'TBD') . ' ' . ($this->transferRequest->newOwner->last_name ?? '')
            ];
        }

        $nextStepsMessage = $this->transferRequest->transfer_type === 'external'
            ? 'Your minibus has been successfully transferred externally and is no longer associated with your MOAMS account. The minibus record has been archived in our system.'
            : 'Your minibus has been successfully transferred to the new owner within the MOAMS system. The ownership records have been updated accordingly.';

        $notificationData = [
            'badge' => ['type' => 'success', 'text' => 'Transfer Completed'],
            'greeting' => "Hello {$owner->first_name},",
            'message' => 'Your minibus transfer request has been successfully processed by our association team. The transfer has been completed as requested.',
            'details' => $details,
            'additionalMessages' => [
                ['type' => 'divider'],
                ['title' => 'What\'s Next?', 'content' => $nextStepsMessage],
                ['content' => 'If you have any questions about this transfer or need assistance with other minibuses, please contact our support team or log into your MOAMS account.'],
                ['content' => 'Thank you for using MOAMS!']
            ]
        ];

        return (new MailMessage)
            ->subject('Minibus Transfer Successful')
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => 'Minibus Transfer Successful',
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    public function toArray($notifiable): array
    {
        $minibus = $this->transferRequest->minibus;
        return [
            'minibus_id' => $minibus->id,
            'minibus_number_plate' => $minibus->number_plate,
            'transfer_type' => $this->transferRequest->transfer_type,
            'status' => $this->transferRequest->status,
        ];
    }
}