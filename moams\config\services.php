<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],
    'ctechpay' => [
        'token' => env('CTECHPAY_API_KEY'),
        'registration' => env('CTECHPAY_REGISTRATION'),
        'environment' => env('CTECHPAY_ENVIRONMENT', 'sandbox'),
        'api_url' => env('CTECHPAY_ENVIRONMENT', 'sandbox') === 'sandbox'
            ? 'https://api-sandbox.ctechpay.com'
            : 'https://api.ctechpay.com',
    ],

    /* 'paychangu' => [
         'public_key' => env('PAYCHANGU_PUBLIC_KEY'),
         'private_key' => env('PAYCHANGU_PRIVATE_KEY'),
     ],*/


];
