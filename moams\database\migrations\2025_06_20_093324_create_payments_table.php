<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('membership_id')->nullable()->constrained('memberships')->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->string('currency_code')->nullable();
            $table->string('formatted_amount')->nullable();
            $table->string('card_holder_name')->nullable();
            $table->enum('fee_type', ['registration', 'affiliation']);
            $table->enum('status', ['pending', 'fulfilled'])->default('pending');
            $table->enum('payment_method', ['cash', 'ctechpay'])->default('cash');
            $table->string('order_reference')->nullable();
            $table->string('payment_page_URL')->nullable();
            $table->date('paid_at')->nullable();
            $table->string('verification_method')->nullable();
            $table->text('verification_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
