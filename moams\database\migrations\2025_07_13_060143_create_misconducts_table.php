<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('misconducts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Overloading", "Driving under influence"
            $table->text('description')->nullable();
            $table->date('offense_date');
            $table->enum('severity', ['low', 'medium', 'high'])->default('medium');
            $table->string('original_severity')->nullable();

            // Add minibus association
            $table->foreignId('minibus_id')->nullable()->constrained('minibuses')->onDelete('set null');

            // Add reporting owner (who reported the misconduct)
            $table->foreignId('reported_by')->nullable()->constrained('users')->onDelete('set null');

            // Add evidence file path
            $table->string('evidence_file')->nullable();

            // Add points deducted for trust score calculation
            $table->integer('points_deducted')->default(0);
            $table->enum('resolution_status', ['unresolved', 'resolved'])->default('unresolved');

            // Add resolution details
            $table->text('resolution_notes')->nullable();

            // Add who resolved it and when
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('resolved_at')->nullable();

            // Severity review fields
            $table->unsignedBigInteger('severity_reviewed_by')->nullable();
            $table->timestamp('severity_reviewed_at')->nullable();
            $table->text('severity_review_notes')->nullable();

            $table->foreign('severity_reviewed_by')->references('id')->on('users')->onDelete('set null');

            // Polymorphic relation: can be user or driver
            $table->morphs('offender'); // creates offender_id, offender_type

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('misconducts');
    }
};
