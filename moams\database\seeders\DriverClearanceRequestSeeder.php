<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DriverClearanceRequest;
use App\Models\Driver;
use App\Models\User;

class DriverClearanceRequestSeeder extends Seeder
{
    public function run(): void
    {
        $drivers = Driver::all();
        $users = User::all();
        if ($drivers->count() === 0 || $users->count() === 0) {
            return;
        }
        DriverClearanceRequest::factory()
            ->count(10)
            ->make()
            ->each(function ($request) use ($drivers, $users) {
                $request->driver_id = $drivers->random()->id;
                $request->owner_id = $users->random()->id;
                $request->save();
            });
    }
}