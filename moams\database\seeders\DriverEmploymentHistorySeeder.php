<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DriverEmploymentHistory;
use App\Models\Driver;
use App\Models\User;

class DriverEmploymentHistorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some drivers and users for seeding
        $drivers = Driver::take(5)->get();
        $users = User::role('minibus owner')->take(3)->get();

        if ($drivers->isEmpty() || $users->isEmpty()) {
            $this->command->info('No drivers or minibus owners found. Skipping employment history seeding.');
            return;
        }

        foreach ($drivers as $driver) {
            // Create initial hiring record
            DriverEmploymentHistory::create([
                'driver_id' => $driver->id,
                'new_owner_id' => $driver->owner_id,
                'employment_change_type' => 'hired',
                'status' => 'completed',
                'employment_start_date' => now()->subDays(rand(30, 365)),
                'reason' => 'Initial driver registration',
            ]);

            // Randomly create some additional employment changes
            if (rand(0, 1)) {
                $previousOwner = $users->random();
                $newOwner = $users->where('id', '!=', $previousOwner->id)->first() ?? $users->first();

                DriverEmploymentHistory::create([
                    'driver_id' => $driver->id,
                    'previous_owner_id' => $previousOwner->id,
                    'new_owner_id' => $newOwner->id,
                    'employment_change_type' => 'rehired',
                    'status' => 'completed',
                    'employment_start_date' => now()->subDays(rand(10, 100)),
                    'reason' => 'Driver transferred to new owner',
                ]);
            }
        }

        $this->command->info('Driver employment history seeded successfully.');
    }
}
