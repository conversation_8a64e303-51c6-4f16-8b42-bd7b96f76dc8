<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $minibusOwners = \App\Models\User::role('minibus owner')->get();
        if ($minibusOwners->count() === 0) {
            return;
        }
        $minibuses = \App\Models\Minibus::all();
        \App\Models\Driver::factory()
            ->count(20)
            ->make()
            ->each(function ($driver) use ($minibusOwners, $minibuses) {
                $driver->owner_id = $minibusOwners->random()->id;
                if ($minibuses->count() > 0) {
                    $driver->minibus_id = $minibuses->random()->id;
                }
                $driver->save();
            });
    }
}
