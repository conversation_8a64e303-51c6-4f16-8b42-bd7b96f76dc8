<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FeeSetting;
use App\Models\User;

class FeeSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first association manager or create one if none exists
        $manager = User::role('association manager')->first();
        if (!$manager) {
            $manager = User::factory()->create([
                'email' => '<EMAIL>',
                'first_name' => 'Association',
                'last_name' => 'Manager',
            ]);
            $manager->assignRole('association manager');
        }

        // Create initial fee settings
        FeeSetting::create([
            'fee_type' => 'registration',
            'amount' => 2500.00,
            'description' => 'Initial registration fee for new minibus owners',
            'effective_from' => now()->subYear(),
            'is_active' => true,
            'created_by' => $manager->id,
        ]);

        FeeSetting::create([
            'fee_type' => 'affiliation',
            'amount' => 2500.00,
            'description' => 'Annual affiliation fee for existing members',
            'effective_from' => now()->subYear(),
            'is_active' => true,
            'created_by' => $manager->id,
        ]);
    }
}
