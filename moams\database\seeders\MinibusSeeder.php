<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Minibus;
use App\Models\User;
use App\Models\Member;
use App\Models\VehicleRoute;

class MinibusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $owners = User::role('minibus owner')->get();
        if ($owners->count() === 0) {
            return; // No minibus owners to assign minibuses to
        }
        Minibus::factory()
            ->count(10)
            ->make()
            ->each(function ($minibus) use ($owners) {
                $minibus->owner_id = $owners->random()->id;
                // The factory now sets all required attributes (make, model, year_of_make, main_colour, proof_of_ownership)
                $minibus->save();
            });

        // Ensure there are routes to assign
        if (VehicleRoute::count() === 0) {
            VehicleRoute::insert([
                ['name' => 'Area 25 - City Centre', 'status' => 'active'],
                ['name' => 'Chilinde - City Centre', 'status' => 'active'],
                ['name' => 'Area 18 - City Centre', 'status' => 'inactive'],
            ]);
        }
    }
}
