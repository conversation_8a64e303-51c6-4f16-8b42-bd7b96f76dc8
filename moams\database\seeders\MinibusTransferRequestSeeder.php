<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MinibusTransferRequest;
use App\Models\Minibus;
use App\Models\User;

class MinibusTransferRequestSeeder extends Seeder
{
    public function run(): void
    {
        $minibuses = Minibus::all();
        $users = User::all();
        if ($minibuses->count() === 0 || $users->count() === 0) {
            return;
        }
        MinibusTransferRequest::factory()
            ->count(10)
            ->make()
            ->each(function ($request) use ($minibuses, $users) {
                $request->minibus_id = $minibuses->random()->id;
                $request->owner_id = $users->random()->id;
                $request->save();
            });
    }
}