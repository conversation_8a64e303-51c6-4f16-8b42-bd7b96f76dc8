<?php

namespace Database\Seeders;

use App\Models\Misconduct;
use App\Models\User;
use App\Models\Driver;
use Illuminate\Database\Seeder;

class MisconductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get drivers to assign misconducts to (only drivers now, not minibus owners)
        $drivers = Driver::active()->take(10)->get();

        if ($drivers->isEmpty()) {
            echo "No active drivers found. Please seed drivers first.\n";
            return;
        }

        // Create misconducts for drivers with recent dates
        foreach ($drivers as $driver) {
            // Create 1-3 misconducts per driver
            $misconductCount = rand(1, 3);

            for ($i = 0; $i < $misconductCount; $i++) {
                // Create misconducts spread across the last 3 months
                $offenseDate = now()->subDays(rand(0, 90));

                Misconduct::factory()
                    ->forDriver($driver)
                    ->create([
                        'offense_date' => $offenseDate,
                        'reported_by' => $driver->owner_id, // Reported by the driver's owner
                        'points_deducted' => match (fake()->randomElement(['low', 'medium', 'high'])) {
                            'low' => 5,
                            'medium' => 10,
                            'high' => 20,
                        }
                    ]);
            }
        }

        // Update driver trust scores based on their misconducts
        foreach ($drivers as $driver) {
            $totalPoints = $driver->misconducts()->sum('points_deducted');
            $newTrustScore = max(0, 100 - $totalPoints);
            $driver->update(['trust_score' => $newTrustScore]);
        }

        echo "Created " . Misconduct::count() . " misconduct records.\n";
        echo "Current month misconducts: " . Misconduct::whereMonth('offense_date', now()->month)->whereYear('offense_date', now()->year)->count() . "\n";
    }
}
