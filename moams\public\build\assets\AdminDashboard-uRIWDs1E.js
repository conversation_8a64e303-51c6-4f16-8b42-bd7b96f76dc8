import{j as s}from"./app-BbBkOpss.js";import{C as l,a as n,b as t,c as d}from"./card-CeuYtmvf.js";import{U as o}from"./user-BYQKZGaY.js";import{U as x}from"./users-vqmOp4p6.js";import"./utils-BB2gXWs2.js";function b({user:a,users:c=[]}){const m=c.reduce((e,r)=>{const i=r.roles&&r.roles.length>0?r.roles[0].name:"Unknown";return e[i]=(e[i]||0)+1,e},{});return s.jsxs("div",{className:"space-y-8 p-4",children:[s.jsxs(l,{className:"shadow-md",children:[s.jsx(n,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(o,{className:"h-5 w-5 text-blue-600"})," Personal Details"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Your basic profile information."}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"Name:"})," ",a.first_name," ",a.last_name]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"Email:"})," ",a.email]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"Phone:"})," ",a.phone_number]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"District:"})," ",a.district]})]})]})]}),s.jsxs(l,{className:"shadow-md",children:[s.jsx(n,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(x,{className:"h-5 w-5 text-green-600"})," User Statistics"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Number of users by role in the system."}),s.jsx("div",{className:"flex flex-wrap gap-8",children:Object.entries(m).map(([e,r])=>s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-700",children:r}),s.jsx("div",{className:"text-gray-600 capitalize",children:e})]},e))})]})]})]})}export{b as default};
