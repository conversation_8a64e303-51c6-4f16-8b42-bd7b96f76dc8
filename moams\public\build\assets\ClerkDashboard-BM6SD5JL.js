import{j as e}from"./app-BbBkOpss.js";import{C as a,a as l,b as i,c as t}from"./card-CeuYtmvf.js";import{U as p}from"./user-BYQKZGaY.js";import{B as b}from"./bus-Bw3CllrX.js";import{U as g}from"./users-vqmOp4p6.js";import{B as f}from"./badge-check-D1iz0v56.js";import{T as y}from"./triangle-alert-Dh6aTEch.js";import{a as u}from"./utils-BB2gXWs2.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]],C=u("ClipboardList",w);function _({user:c={},stats:s={},misconducts:o=0,clearanceRequests:N=0,transferRequests:v=0,currentFees:M={}}){var r,d,x,n,m,h,j;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 p-4",children:[e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5 text-blue-600"})," Personal Details"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Your basic profile information."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"First Name:"})," ",c.first_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Last Name:"})," ",c.last_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Gender:"})," ",c.gender||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"District:"})," ",c.district||"N/A"]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5 text-blue-600"})," Minibus Statistics"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Overview of all registered minibuses."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((r=s.minibuses)==null?void 0:r.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((d=s.minibuses)==null?void 0:d.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((x=s.minibuses)==null?void 0:x.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5 text-blue-600"})," Driver Statistics"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Summary of all drivers in the system."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((n=s.drivers)==null?void 0:n.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((m=s.drivers)==null?void 0:m.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((h=s.drivers)==null?void 0:h.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5 text-blue-600"})," Members & Memberships"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Membership status and unpaid memberships."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:s.members??0}),e.jsx("div",{className:"text-gray-600",children:"All Members"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-yellow-600",children:((j=s.memberships)==null?void 0:j.unpaid)??0}),e.jsx("div",{className:"text-gray-600",children:"Unpaid memberships"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5 text-yellow-600"})," Misconducts"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Reported misconducts in the association."}),e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xl font-bold text-yellow-600",children:o??0})})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-5 w-5 text-blue-600"})," Pending Requests"]})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Requests awaiting action."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-blue-600",children:N??0}),e.jsx("div",{className:"text-gray-600",children:"Clearance"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-blue-600",children:v??0}),e.jsx("div",{className:"text-gray-600",children:"Transfer"})]})]})]})]})]})}export{_ as default};
