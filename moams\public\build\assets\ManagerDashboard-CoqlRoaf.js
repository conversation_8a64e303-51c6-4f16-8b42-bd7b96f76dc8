import{j as e}from"./app-BbBkOpss.js";import{C as a,a as i,b as l,c as r}from"./card-CeuYtmvf.js";import{U as N}from"./user-BYQKZGaY.js";import{B as v}from"./bus-Bw3CllrX.js";import{U as p}from"./users-vqmOp4p6.js";import{B as b}from"./badge-check-D1iz0v56.js";import{T as g}from"./triangle-alert-Dh6aTEch.js";import"./utils-BB2gXWs2.js";function S({user:c={},stats:s={},misconducts:o=0,unpaidMemberships:f=0,currentFees:u={}}){var d,t,n,x,m,h,j;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 p-4",children:[e.jsxs(a,{className:"shadow-md",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-5 w-5 text-blue-600"})," Personal Details"]})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Your basic profile information."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"First Name:"})," ",c.first_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Last Name:"})," ",c.last_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Gender:"})," ",c.gender||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"District:"})," ",c.district||"N/A"]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5 text-blue-600"})," Minibus Statistics"]})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Overview of all registered minibuses."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((d=s.minibuses)==null?void 0:d.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((t=s.minibuses)==null?void 0:t.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((n=s.minibuses)==null?void 0:n.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5 text-blue-600"})," Driver Statistics"]})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Summary of all drivers in the system."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((x=s.drivers)==null?void 0:x.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((m=s.drivers)==null?void 0:m.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((h=s.drivers)==null?void 0:h.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5 text-blue-600"})," Members & Memberships"]})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Membership status and unpaid memberships. (Members = minibus owners)"}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:s.members??0}),e.jsx("div",{className:"text-gray-600",children:"All Members"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-yellow-600",children:((j=s.memberships)==null?void 0:j.unpaid)??0}),e.jsx("div",{className:"text-gray-600",children:"Unpaid memberships"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5 text-yellow-600"})," Misconducts"]})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Reported misconducts in the association."}),e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xl font-bold text-yellow-600",children:o??0})})]})]})]})}export{S as default};
