import{j as s}from"./app-BbBkOpss.js";import{C as l,a as i,b as t,c as d}from"./card-CeuYtmvf.js";import{U as n}from"./user-BYQKZGaY.js";import{D as x}from"./dollar-sign-Cgj5GuZe.js";import{B as m}from"./bus-Bw3CllrX.js";import{U as o}from"./users-vqmOp4p6.js";import"./utils-BB2gXWs2.js";function y({user:a,minibuses:r=[],drivers:c=[],unpaidMemberships:h=[],currentFees:e}){return s.jsxs(s.Fragment,{children:[s.jsxs(l,{className:"shadow-md mb-6",children:[s.jsx(i,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(n,{className:"h-5 w-5 text-blue-600"})," Personal Details"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Your basic profile information."}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"First Name:"})," ",a.first_name||"N/A"]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"Last Name:"})," ",a.last_name||"N/A"]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"Gender:"})," ",a.gender||"N/A"]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:"District:"})," ",a.district||"N/A"]})]})]})]}),s.jsxs(l,{className:"shadow-md mb-6",children:[s.jsx(i,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(x,{className:"h-5 w-5 text-green-600"})," Current Fees"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Below are the current registration and affiliation fees set by the association."}),s.jsxs("div",{className:"flex gap-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-xl font-bold text-green-700",children:e!=null&&e.registration?`MK ${e.registration}`:"N/A"}),s.jsx("div",{className:"text-gray-600",children:"Registration Fee"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-xl font-bold text-green-700",children:e!=null&&e.affiliation?`MK ${e.affiliation}`:"N/A"}),s.jsx("div",{className:"text-gray-600",children:"Affiliation Fee"})]})]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[s.jsxs(l,{className:"shadow-md",children:[s.jsx(i,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(m,{className:"h-5 w-5 text-blue-600"})," Minibus Statistics"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Overview of your registered minibuses."}),s.jsx("div",{className:"flex gap-8",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-xl font-bold",children:(r==null?void 0:r.length)??0}),s.jsx("div",{className:"text-gray-600",children:"All"})]})})]})]}),s.jsxs(l,{className:"shadow-md",children:[s.jsx(i,{children:s.jsxs(t,{className:"flex items-center gap-2",children:[s.jsx(o,{className:"h-5 w-5 text-blue-600"})," Driver Statistics"]})}),s.jsxs(d,{children:[s.jsx("p",{className:"text-gray-500 mb-2",children:"Summary of your drivers."}),s.jsx("div",{className:"flex gap-8",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-xl font-bold",children:(c==null?void 0:c.length)??0}),s.jsx("div",{className:"text-gray-600",children:"All"})]})})]})]})]})]})}export{y as default};
