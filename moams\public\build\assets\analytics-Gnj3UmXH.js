import{r as m,j as e,b as j}from"./app-BbBkOpss.js";import{A as B}from"./app-layout-DI72WroM.js";import{C as t,a as x,b as h,c as n}from"./card-CeuYtmvf.js";import{B as E}from"./app-logo-icon-EJK9zfCM.js";import{S as p,a as u,b as f,c as y,d as c}from"./select-B1BNOBy4.js";import{n as V}from"./navigation-DXenxO2h.js";import{C as Y}from"./chevron-left-BQ99U5mw.js";import{C as I}from"./chart-column-BRNjmg3d.js";import{D as J}from"./download-u6EDakOu.js";import{C as z}from"./calendar-HpFaW1Ru.js";import{D as W}from"./dollar-sign-Cgj5GuZe.js";import{C as G}from"./credit-card-KPJaArOK.js";import{U as H}from"./users-vqmOp4p6.js";import{T as K}from"./trending-up-N_Z4Gbll.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./triangle-alert-Dh6aTEch.js";import"./index-CHr_Yg1t.js";function fe({summary:o,trends:O,feeTypeBreakdown:v,paymentMethodBreakdown:w,topPayingMembers:C,selectedPeriod:S,selectedYear:P,selectedMonth:_,userRoles:q}){const[l,D]=m.useState(S),[i,A]=m.useState(P),[d,$]=m.useState(_),[g,N]=m.useState(!1),b=[{title:"Dashboard",href:"/dashboard"},{title:"Payment Analytics",href:"/payment-analytics"}],k=s=>{D(s);const a=new URLSearchParams({period:s,year:i,...s==="monthly"?{month:d}:{}});j.get(`/payment-analytics?${a.toString()}`)},L=s=>{A(s);const a=new URLSearchParams({period:l,year:s,...l==="monthly"?{month:d}:{}});j.get(`/payment-analytics?${a.toString()}`)},R=s=>{$(s);const a=new URLSearchParams({period:l,year:i,month:s});j.get(`/payment-analytics?${a.toString()}`)},T=async()=>{try{N(!0);const s=document.createElement("a"),a=new URLSearchParams({period:l,year:i,...l==="monthly"?{month:d}:{}});s.href=`/payment-analytics/report?${a.toString()}`,s.download=`financial-report-${l}-${i}${l==="monthly"?`-${d}`:""}.pdf`,document.body.appendChild(s),s.click(),document.body.removeChild(s)}catch(s){console.error("Error downloading PDF:",s)}finally{N(!1)}},r=s=>`MWK ${Number(s).toLocaleString()}`,M=new Date().getFullYear(),U=Array.from({length:5},(s,a)=>M-a),F=[{value:1,label:"January"},{value:2,label:"February"},{value:3,label:"March"},{value:4,label:"April"},{value:5,label:"May"},{value:6,label:"June"},{value:7,label:"July"},{value:8,label:"August"},{value:9,label:"September"},{value:10,label:"October"},{value:11,label:"November"},{value:12,label:"December"}];return e.jsx(B,{breadcrumbs:b,children:e.jsxs("div",{className:"max-w-7xl mx-auto p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",onClick:()=>V(b),children:[e.jsx(Y,{className:"h-4 w-4 mr-1"})," Back"]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[e.jsx(I,{className:"h-6 w-6 text-blue-600"}),"Payment Analytics"]}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Financial insights and payment trends"})]}),e.jsxs(E,{onClick:T,disabled:g,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2",children:[e.jsx(J,{className:"h-4 w-4"}),g?"Generating...":"Download Report"]})]})]}),e.jsxs(t,{className:"mb-6",children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5"}),"Report Filters"]})}),e.jsx(n,{children:e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Period"}),e.jsxs(p,{value:l,onValueChange:k,children:[e.jsx(u,{className:"w-40",children:e.jsx(f,{})}),e.jsxs(y,{children:[e.jsx(c,{value:"daily",children:"Daily"}),e.jsx(c,{value:"weekly",children:"Weekly"}),e.jsx(c,{value:"monthly",children:"Monthly"}),e.jsx(c,{value:"annually",children:"Annually"})]})]})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Year"}),e.jsxs(p,{value:i.toString(),onValueChange:s=>L(parseInt(s)),children:[e.jsx(u,{className:"w-32",children:e.jsx(f,{})}),e.jsx(y,{children:U.map(s=>e.jsx(c,{value:s.toString(),children:s},s))})]})]}),l==="monthly"&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Month"}),e.jsxs(p,{value:d.toString(),onValueChange:s=>R(parseInt(s)),children:[e.jsx(u,{className:"w-40",children:e.jsx(f,{})}),e.jsx(y,{children:F.map(s=>e.jsx(c,{value:s.value.toString(),children:s.label},s.value))})]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[e.jsx(t,{children:e.jsx(n,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:r(o.total_revenue)})]}),e.jsx(W,{className:"h-8 w-8 text-green-600"})]})})}),e.jsx(t,{children:e.jsx(n,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Payments"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:o.total_payments})]}),e.jsx(G,{className:"h-8 w-8 text-blue-600"})]})})}),e.jsx(t,{children:e.jsx(n,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Registration Fees"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:r(o.registration_revenue)})]}),e.jsx(H,{className:"h-8 w-8 text-purple-600"})]})})}),e.jsx(t,{children:e.jsx(n,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Affiliation Fees"}),e.jsx("p",{className:"text-2xl font-bold text-orange-600",children:r(o.affiliation_revenue)})]}),e.jsx(K,{className:"h-8 w-8 text-orange-600"})]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsxs(t,{children:[e.jsx(x,{children:e.jsx(h,{children:"Fee Type Breakdown"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:v.map((s,a)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"capitalize font-medium",children:s.fee_type}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:r(s.total_amount)}),e.jsxs("div",{className:"text-sm text-gray-500",children:[s.count," payments"]})]})]},a))})})]}),e.jsxs(t,{children:[e.jsx(x,{children:e.jsx(h,{children:"Payment Method Breakdown"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:w.map((s,a)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"capitalize font-medium",children:s.payment_method}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:r(s.total_amount)}),e.jsxs("div",{className:"text-sm text-gray-500",children:[s.count," payments"]})]})]},a))})})]})]}),e.jsxs(t,{children:[e.jsx(x,{children:e.jsx(h,{children:"Top Paying Members"})}),e.jsx(n,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left py-2",children:"Member"}),e.jsx("th",{className:"text-left py-2",children:"Payments"}),e.jsx("th",{className:"text-left py-2",children:"Total Amount"})]})}),e.jsx("tbody",{children:C.map((s,a)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"py-2",children:s.user?`${s.user.first_name} ${s.user.last_name}`:"Unknown User"}),e.jsx("td",{className:"py-2",children:s.payment_count}),e.jsx("td",{className:"py-2 font-medium",children:r(s.total_amount)})]},a))})]})})})]})]})})}export{fe as default};
