import{r as l,t as Na,j as s,a as Me,L as de,R as ge,e as ja,b as Fe}from"./app-BbBkOpss.js";import{u as W,c as lt,b as Ta,S as vt,d as mr,B as Be,a as hr,A as Da}from"./app-logo-icon-EJK9zfCM.js";import{a as q,c as N}from"./utils-BB2gXWs2.js";import{d as xe,a as A,u as xt,c as me,P as le,e as Oa,b as Ia,C as ka}from"./index-ChOOE5JL.js";import{P as I,d as gr,R as La,r as Fa}from"./index-CAhsadr8.js";import{B as $a}from"./bus-Bw3CllrX.js";import{D as Ba}from"./dollar-sign-Cgj5GuZe.js";import{T as Wa}from"./triangle-alert-Dh6aTEch.js";import{U as Ha}from"./users-vqmOp4p6.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ua=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],$n=q("Bell",Ua);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ga=[["path",{d:"M15 13a3 3 0 1 0-6 0",key:"10j68g"}],["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}]],Bn=q("BookUser",Ga);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ka=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],za=q("ChevronRight",Ka);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Va=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Ya=q("ChevronsUpDown",Va);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qa=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],Xa=q("Ellipsis",qa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Za=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Qa=q("House",Za);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ja=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],es=q("Key",Ja);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],ns=q("LogOut",ts);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]],os=q("Map",rs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],ss=q("PanelLeft",as);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]],cs=q("UserCheck",is);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],vr=q("X",ls),Dt=768;function xr(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Dt-1}px)`),r=()=>{t(window.innerWidth<Dt)};return n.addEventListener("change",r),t(window.innerWidth<Dt),()=>n.removeEventListener("change",r)},[]),!!e}var us=Na[" useId ".trim().toString()]||(()=>{}),ds=0;function ve(e){const[t,n]=l.useState(us());return xe(()=>{n(r=>r??String(ds++))},[e]),t?`radix-${t}`:""}function re(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function fs(e,t=globalThis==null?void 0:globalThis.document){const n=re(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var ps="DismissableLayer",Yt="dismissableLayer.update",ms="dismissableLayer.pointerDownOutside",hs="dismissableLayer.focusOutside",Wn,wr=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...f}=e,p=l.useContext(wr),[d,u]=l.useState(null),m=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=l.useState({}),w=W(t,S=>u(S)),g=Array.from(p.layers),[h]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),x=g.indexOf(h),b=d?g.indexOf(d):-1,y=p.layersWithOutsidePointerEventsDisabled.size>0,C=b>=x,E=xs(S=>{const _=S.target,D=[...p.branches].some(j=>j.contains(_));!C||D||(o==null||o(S),i==null||i(S),S.defaultPrevented||c==null||c())},m),M=ws(S=>{const _=S.target;[...p.branches].some(j=>j.contains(_))||(a==null||a(S),i==null||i(S),S.defaultPrevented||c==null||c())},m);return fs(S=>{b===p.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},m),l.useEffect(()=>{if(d)return n&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(Wn=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(d)),p.layers.add(d),Hn(),()=>{n&&p.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Wn)}},[d,m,n,p]),l.useEffect(()=>()=>{d&&(p.layers.delete(d),p.layersWithOutsidePointerEventsDisabled.delete(d),Hn())},[d,p]),l.useEffect(()=>{const S=()=>v({});return document.addEventListener(Yt,S),()=>document.removeEventListener(Yt,S)},[]),s.jsx(I.div,{...f,ref:w,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:A(e.onFocusCapture,M.onFocusCapture),onBlurCapture:A(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:A(e.onPointerDownCapture,E.onPointerDownCapture)})});wt.displayName=ps;var gs="DismissableLayerBranch",vs=l.forwardRef((e,t)=>{const n=l.useContext(wr),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),s.jsx(I.div,{...e,ref:o})});vs.displayName=gs;function xs(e,t=globalThis==null?void 0:globalThis.document){const n=re(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let f=function(){br(ms,n,p,{discrete:!0})};const p={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=f,t.addEventListener("click",o.current,{once:!0})):f()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function ws(e,t=globalThis==null?void 0:globalThis.document){const n=re(e),r=l.useRef(!1);return l.useEffect(()=>{const o=a=>{a.target&&!r.current&&br(hs,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Hn(){const e=new CustomEvent(Yt);document.dispatchEvent(e)}function br(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gr(o,a):o.dispatchEvent(a)}var Ot="focusScope.autoFocusOnMount",It="focusScope.autoFocusOnUnmount",Un={bubbles:!1,cancelable:!0},bs="FocusScope",an=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,f]=l.useState(null),p=re(o),d=re(a),u=l.useRef(null),m=W(t,g=>f(g)),v=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let g=function(y){if(v.paused||!c)return;const C=y.target;c.contains(C)?u.current=C:ue(u.current,{select:!0})},h=function(y){if(v.paused||!c)return;const C=y.relatedTarget;C!==null&&(c.contains(C)||ue(u.current,{select:!0}))},x=function(y){if(document.activeElement===document.body)for(const E of y)E.removedNodes.length>0&&ue(c)};document.addEventListener("focusin",g),document.addEventListener("focusout",h);const b=new MutationObserver(x);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",h),b.disconnect()}}},[r,c,v.paused]),l.useEffect(()=>{if(c){Kn.add(v);const g=document.activeElement;if(!c.contains(g)){const x=new CustomEvent(Ot,Un);c.addEventListener(Ot,p),c.dispatchEvent(x),x.defaultPrevented||(ys(Ms(yr(c)),{select:!0}),document.activeElement===g&&ue(c))}return()=>{c.removeEventListener(Ot,p),setTimeout(()=>{const x=new CustomEvent(It,Un);c.addEventListener(It,d),c.dispatchEvent(x),x.defaultPrevented||ue(g??document.body,{select:!0}),c.removeEventListener(It,d),Kn.remove(v)},0)}}},[c,p,d,v]);const w=l.useCallback(g=>{if(!n&&!r||v.paused)return;const h=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,x=document.activeElement;if(h&&x){const b=g.currentTarget,[y,C]=Cs(b);y&&C?!g.shiftKey&&x===C?(g.preventDefault(),n&&ue(y,{select:!0})):g.shiftKey&&x===y&&(g.preventDefault(),n&&ue(C,{select:!0})):x===b&&g.preventDefault()}},[n,r,v.paused]);return s.jsx(I.div,{tabIndex:-1,...i,ref:m,onKeyDown:w})});an.displayName=bs;function ys(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ue(r,{select:t}),document.activeElement!==n)return}function Cs(e){const t=yr(e),n=Gn(t,e),r=Gn(t.reverse(),e);return[n,r]}function yr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Gn(e,t){for(const n of e)if(!Es(n,{upTo:t}))return n}function Es(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ss(e){return e instanceof HTMLInputElement&&"select"in e}function ue(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ss(e)&&t&&e.select()}}var Kn=Rs();function Rs(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=zn(e,t),e.unshift(t)},remove(t){var n;e=zn(e,t),(n=e[0])==null||n.resume()}}}function zn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Ms(e){return e.filter(t=>t.tagName!=="A")}var As="Portal",bt=l.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,a]=l.useState(!1);xe(()=>a(!0),[]);const i=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?La.createPortal(s.jsx(I.div,{...r,ref:t}),i):null});bt.displayName=As;var kt=0;function Cr(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Vn()),document.body.insertAdjacentElement("beforeend",e[1]??Vn()),kt++,()=>{kt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),kt--}},[])}function Vn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ee=function(){return ee=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},ee.apply(this,arguments)};function Er(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function _s(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var st="right-scroll-bar-position",it="width-before-scroll-bar",Ps="with-scroll-bars-hidden",Ns="--removed-body-scroll-bar-size";function Lt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function js(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Ts=typeof window<"u"?l.useLayoutEffect:l.useEffect,Yn=new WeakMap;function Ds(e,t){var n=js(null,function(r){return e.forEach(function(o){return Lt(o,r)})});return Ts(function(){var r=Yn.get(n);if(r){var o=new Set(r),a=new Set(e),i=n.current;o.forEach(function(c){a.has(c)||Lt(c,null)}),a.forEach(function(c){o.has(c)||Lt(c,i)})}Yn.set(n,e)},[e]),n}function Os(e){return e}function Is(e,t){t===void 0&&(t=Os);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(r=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(c){return a(c)},filter:function(){return n}}},assignMedium:function(a){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(a),i=n}var f=function(){var d=i;i=[],d.forEach(a)},p=function(){return Promise.resolve().then(f)};p(),n={push:function(d){i.push(d),p()},filter:function(d){return i=i.filter(d),n}}}};return o}function ks(e){e===void 0&&(e={});var t=Is(null);return t.options=ee({async:!0,ssr:!1},e),t}var Sr=function(e){var t=e.sideCar,n=Er(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,ee({},n))};Sr.isSideCarExport=!0;function Ls(e,t){return e.useMedium(t),Sr}var Rr=ks(),Ft=function(){},yt=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:Ft,onWheelCapture:Ft,onTouchMoveCapture:Ft}),o=r[0],a=r[1],i=e.forwardProps,c=e.children,f=e.className,p=e.removeScrollBar,d=e.enabled,u=e.shards,m=e.sideCar,v=e.noRelative,w=e.noIsolation,g=e.inert,h=e.allowPinchZoom,x=e.as,b=x===void 0?"div":x,y=e.gapMode,C=Er(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=m,M=Ds([n,t]),S=ee(ee({},C),o);return l.createElement(l.Fragment,null,d&&l.createElement(E,{sideCar:Rr,removeScrollBar:p,shards:u,noRelative:v,noIsolation:w,inert:g,setCallbacks:a,allowPinchZoom:!!h,lockRef:n,gapMode:y}),i?l.cloneElement(l.Children.only(c),ee(ee({},S),{ref:M})):l.createElement(b,ee({},S,{className:f,ref:M}),c))});yt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};yt.classNames={fullWidth:it,zeroRight:st};var Fs=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function $s(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Fs();return t&&e.setAttribute("nonce",t),e}function Bs(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ws(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Hs=function(){var e=0,t=null;return{add:function(n){e==0&&(t=$s())&&(Bs(t,n),Ws(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Us=function(){var e=Hs();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Mr=function(){var e=Us(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Gs={left:0,top:0,right:0,gap:0},$t=function(e){return parseInt(e||"",10)||0},Ks=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[$t(n),$t(r),$t(o)]},zs=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Gs;var t=Ks(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Vs=Mr(),Ae="data-scroll-locked",Ys=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ps,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(Ae,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(st,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(it,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(st," .").concat(st,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(it," .").concat(it,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ae,`] {
    `).concat(Ns,": ").concat(c,`px;
  }
`)},qn=function(){var e=parseInt(document.body.getAttribute(Ae)||"0",10);return isFinite(e)?e:0},qs=function(){l.useEffect(function(){return document.body.setAttribute(Ae,(qn()+1).toString()),function(){var e=qn()-1;e<=0?document.body.removeAttribute(Ae):document.body.setAttribute(Ae,e.toString())}},[])},Xs=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;qs();var a=l.useMemo(function(){return zs(o)},[o]);return l.createElement(Vs,{styles:Ys(a,!t,o,n?"":"!important")})},qt=!1;if(typeof window<"u")try{var et=Object.defineProperty({},"passive",{get:function(){return qt=!0,!0}});window.addEventListener("test",et,et),window.removeEventListener("test",et,et)}catch{qt=!1}var Ee=qt?{passive:!1}:!1,Zs=function(e){return e.tagName==="TEXTAREA"},Ar=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Zs(e)&&n[t]==="visible")},Qs=function(e){return Ar(e,"overflowY")},Js=function(e){return Ar(e,"overflowX")},Xn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=_r(e,r);if(o){var a=Pr(e,r),i=a[1],c=a[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ei=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},ti=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},_r=function(e,t){return e==="v"?Qs(t):Js(t)},Pr=function(e,t){return e==="v"?ei(t):ti(t)},ni=function(e,t){return e==="h"&&t==="rtl"?-1:1},ri=function(e,t,n,r,o){var a=ni(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,f=t.contains(c),p=!1,d=i>0,u=0,m=0;do{if(!c)break;var v=Pr(e,c),w=v[0],g=v[1],h=v[2],x=g-h-a*w;(w||x)&&_r(e,c)&&(u+=x,m+=w);var b=c.parentNode;c=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!f&&c!==document.body||f&&(t.contains(c)||t===c));return(d&&Math.abs(u)<1||!d&&Math.abs(m)<1)&&(p=!0),p},tt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Zn=function(e){return[e.deltaX,e.deltaY]},Qn=function(e){return e&&"current"in e?e.current:e},oi=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ai=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},si=0,Se=[];function ii(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(si++)[0],a=l.useState(Mr)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=_s([e.lockRef.current],(e.shards||[]).map(Qn),!0).filter(Boolean);return g.forEach(function(h){return h.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(h){return h.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(g,h){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var x=tt(g),b=n.current,y="deltaX"in g?g.deltaX:b[0]-x[0],C="deltaY"in g?g.deltaY:b[1]-x[1],E,M=g.target,S=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in g&&S==="h"&&M.type==="range")return!1;var _=Xn(S,M);if(!_)return!0;if(_?E=S:(E=S==="v"?"h":"v",_=Xn(S,M)),!_)return!1;if(!r.current&&"changedTouches"in g&&(y||C)&&(r.current=E),!E)return!0;var D=r.current||E;return ri(D,h,g,D==="h"?y:C)},[]),f=l.useCallback(function(g){var h=g;if(!(!Se.length||Se[Se.length-1]!==a)){var x="deltaY"in h?Zn(h):tt(h),b=t.current.filter(function(E){return E.name===h.type&&(E.target===h.target||h.target===E.shadowParent)&&oi(E.delta,x)})[0];if(b&&b.should){h.cancelable&&h.preventDefault();return}if(!b){var y=(i.current.shards||[]).map(Qn).filter(Boolean).filter(function(E){return E.contains(h.target)}),C=y.length>0?c(h,y[0]):!i.current.noIsolation;C&&h.cancelable&&h.preventDefault()}}},[]),p=l.useCallback(function(g,h,x,b){var y={name:g,delta:h,target:x,should:b,shadowParent:ci(x)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),d=l.useCallback(function(g){n.current=tt(g),r.current=void 0},[]),u=l.useCallback(function(g){p(g.type,Zn(g),g.target,c(g,e.lockRef.current))},[]),m=l.useCallback(function(g){p(g.type,tt(g),g.target,c(g,e.lockRef.current))},[]);l.useEffect(function(){return Se.push(a),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",f,Ee),document.addEventListener("touchmove",f,Ee),document.addEventListener("touchstart",d,Ee),function(){Se=Se.filter(function(g){return g!==a}),document.removeEventListener("wheel",f,Ee),document.removeEventListener("touchmove",f,Ee),document.removeEventListener("touchstart",d,Ee)}},[]);var v=e.removeScrollBar,w=e.inert;return l.createElement(l.Fragment,null,w?l.createElement(a,{styles:ai(o)}):null,v?l.createElement(Xs,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function ci(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const li=Ls(Rr,ii);var sn=l.forwardRef(function(e,t){return l.createElement(yt,ee({},e,{ref:t,sideCar:li}))});sn.classNames=yt.classNames;var ui=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Re=new WeakMap,nt=new WeakMap,rt={},Bt=0,Nr=function(e){return e&&(e.host||Nr(e.parentNode))},di=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Nr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},fi=function(e,t,n,r){var o=di(t,Array.isArray(e)?e:[e]);rt[n]||(rt[n]=new WeakMap);var a=rt[n],i=[],c=new Set,f=new Set(o),p=function(u){!u||c.has(u)||(c.add(u),p(u.parentNode))};o.forEach(p);var d=function(u){!u||f.has(u)||Array.prototype.forEach.call(u.children,function(m){if(c.has(m))d(m);else try{var v=m.getAttribute(r),w=v!==null&&v!=="false",g=(Re.get(m)||0)+1,h=(a.get(m)||0)+1;Re.set(m,g),a.set(m,h),i.push(m),g===1&&w&&nt.set(m,!0),h===1&&m.setAttribute(n,"true"),w||m.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",m,x)}})};return d(t),c.clear(),Bt++,function(){i.forEach(function(u){var m=Re.get(u)-1,v=a.get(u)-1;Re.set(u,m),a.set(u,v),m||(nt.has(u)||u.removeAttribute(r),nt.delete(u)),v||u.removeAttribute(n)}),Bt--,Bt||(Re=new WeakMap,Re=new WeakMap,nt=new WeakMap,rt={})}},jr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=ui(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),fi(r,o,n,"aria-hidden")):function(){return null}},Ct="Dialog",[Tr,ff]=me(Ct),[pi,Q]=Tr(Ct),Dr=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,c=l.useRef(null),f=l.useRef(null),[p,d]=xt({prop:r,defaultProp:o??!1,onChange:a,caller:Ct});return s.jsx(pi,{scope:t,triggerRef:c,contentRef:f,contentId:ve(),titleId:ve(),descriptionId:ve(),open:p,onOpenChange:d,onOpenToggle:l.useCallback(()=>d(u=>!u),[d]),modal:i,children:n})};Dr.displayName=Ct;var Or="DialogTrigger",mi=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Or,n),a=W(t,o.triggerRef);return s.jsx(I.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":un(o.open),...r,ref:a,onClick:A(e.onClick,o.onOpenToggle)})});mi.displayName=Or;var cn="DialogPortal",[hi,Ir]=Tr(cn,{forceMount:void 0}),kr=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=Q(cn,t);return s.jsx(hi,{scope:t,forceMount:n,children:l.Children.map(r,i=>s.jsx(le,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:i})}))})};kr.displayName=cn;var ut="DialogOverlay",Lr=l.forwardRef((e,t)=>{const n=Ir(ut,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Q(ut,e.__scopeDialog);return a.modal?s.jsx(le,{present:r||a.open,children:s.jsx(vi,{...o,ref:t})}):null});Lr.displayName=ut;var gi=lt("DialogOverlay.RemoveScroll"),vi=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(ut,n);return s.jsx(sn,{as:gi,allowPinchZoom:!0,shards:[o.contentRef],children:s.jsx(I.div,{"data-state":un(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),we="DialogContent",Fr=l.forwardRef((e,t)=>{const n=Ir(we,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Q(we,e.__scopeDialog);return s.jsx(le,{present:r||a.open,children:a.modal?s.jsx(xi,{...o,ref:t}):s.jsx(wi,{...o,ref:t})})});Fr.displayName=we;var xi=l.forwardRef((e,t)=>{const n=Q(we,e.__scopeDialog),r=l.useRef(null),o=W(t,n.contentRef,r);return l.useEffect(()=>{const a=r.current;if(a)return jr(a)},[]),s.jsx($r,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:A(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:A(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault())})}),wi=l.forwardRef((e,t)=>{const n=Q(we,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return s.jsx($r,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i,c;(i=e.onCloseAutoFocus)==null||i.call(e,a),a.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var f,p;(f=e.onInteractOutside)==null||f.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=a.target;((p=n.triggerRef.current)==null?void 0:p.contains(i))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),$r=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,c=Q(we,n),f=l.useRef(null),p=W(t,f);return Cr(),s.jsxs(s.Fragment,{children:[s.jsx(an,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:s.jsx(wt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":un(c.open),...i,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(bi,{titleId:c.titleId}),s.jsx(Ci,{contentRef:f,descriptionId:c.descriptionId})]})]})}),ln="DialogTitle",Br=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(ln,n);return s.jsx(I.h2,{id:o.titleId,...r,ref:t})});Br.displayName=ln;var Wr="DialogDescription",Hr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Wr,n);return s.jsx(I.p,{id:o.descriptionId,...r,ref:t})});Hr.displayName=Wr;var Ur="DialogClose",Gr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Ur,n);return s.jsx(I.button,{type:"button",...r,ref:t,onClick:A(e.onClick,()=>o.onOpenChange(!1))})});Gr.displayName=Ur;function un(e){return e?"open":"closed"}var Kr="DialogTitleWarning",[pf,zr]=Oa(Kr,{contentName:we,titleName:ln,docsSlug:"dialog"}),bi=({titleId:e})=>{const t=zr(Kr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},yi="DialogDescriptionWarning",Ci=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${zr(yi).contentName}}.`;return l.useEffect(()=>{var a;const o=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Ei=Dr,Si=kr,Ri=Lr,Mi=Fr,Ai=Br,_i=Hr,Pi=Gr;function Ni({...e}){return s.jsx(Ei,{"data-slot":"sheet",...e})}function ji({...e}){return s.jsx(Si,{"data-slot":"sheet-portal",...e})}function Ti({className:e,...t}){return s.jsx(Ri,{"data-slot":"sheet-overlay",className:N("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Di({className:e,children:t,side:n="right",...r}){return s.jsxs(ji,{children:[s.jsx(Ti,{}),s.jsxs(Mi,{"data-slot":"sheet-content",className:N("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,s.jsxs(Pi,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[s.jsx(vr,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Oi({className:e,...t}){return s.jsx("div",{"data-slot":"sheet-header",className:N("flex flex-col gap-1.5 p-4",e),...t})}function Ii({className:e,...t}){return s.jsx(Ai,{"data-slot":"sheet-title",className:N("text-foreground font-semibold",e),...t})}function ki({className:e,...t}){return s.jsx(_i,{"data-slot":"sheet-description",className:N("text-muted-foreground text-sm",e),...t})}const Li=["top","right","bottom","left"],fe=Math.min,K=Math.max,dt=Math.round,ot=Math.floor,ne=e=>({x:e,y:e}),Fi={left:"right",right:"left",bottom:"top",top:"bottom"},$i={start:"end",end:"start"};function Xt(e,t,n){return K(e,fe(t,n))}function ie(e,t){return typeof e=="function"?e(t):e}function ce(e){return e.split("-")[0]}function je(e){return e.split("-")[1]}function dn(e){return e==="x"?"y":"x"}function fn(e){return e==="y"?"height":"width"}const Bi=new Set(["top","bottom"]);function te(e){return Bi.has(ce(e))?"y":"x"}function pn(e){return dn(te(e))}function Wi(e,t,n){n===void 0&&(n=!1);const r=je(e),o=pn(e),a=fn(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=ft(i)),[i,ft(i)]}function Hi(e){const t=ft(e);return[Zt(e),t,Zt(t)]}function Zt(e){return e.replace(/start|end/g,t=>$i[t])}const Jn=["left","right"],er=["right","left"],Ui=["top","bottom"],Gi=["bottom","top"];function Ki(e,t,n){switch(e){case"top":case"bottom":return n?t?er:Jn:t?Jn:er;case"left":case"right":return t?Ui:Gi;default:return[]}}function zi(e,t,n,r){const o=je(e);let a=Ki(ce(e),n==="start",r);return o&&(a=a.map(i=>i+"-"+o),t&&(a=a.concat(a.map(Zt)))),a}function ft(e){return e.replace(/left|right|bottom|top/g,t=>Fi[t])}function Vi(e){return{top:0,right:0,bottom:0,left:0,...e}}function Vr(e){return typeof e!="number"?Vi(e):{top:e,right:e,bottom:e,left:e}}function pt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function tr(e,t,n){let{reference:r,floating:o}=e;const a=te(t),i=pn(t),c=fn(i),f=ce(t),p=a==="y",d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,m=r[c]/2-o[c]/2;let v;switch(f){case"top":v={x:d,y:r.y-o.height};break;case"bottom":v={x:d,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:u};break;case"left":v={x:r.x-o.width,y:u};break;default:v={x:r.x,y:r.y}}switch(je(t)){case"start":v[i]-=m*(n&&p?-1:1);break;case"end":v[i]+=m*(n&&p?-1:1);break}return v}const Yi=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,c=a.filter(Boolean),f=await(i.isRTL==null?void 0:i.isRTL(t));let p=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=tr(p,r,f),m=r,v={},w=0;for(let g=0;g<c.length;g++){const{name:h,fn:x}=c[g],{x:b,y,data:C,reset:E}=await x({x:d,y:u,initialPlacement:r,placement:m,strategy:o,middlewareData:v,rects:p,platform:i,elements:{reference:e,floating:t}});d=b??d,u=y??u,v={...v,[h]:{...v[h],...C}},E&&w<=50&&(w++,typeof E=="object"&&(E.placement&&(m=E.placement),E.rects&&(p=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:u}=tr(p,m,f)),g=-1)}return{x:d,y:u,placement:m,strategy:o,middlewareData:v}};async function He(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:i,elements:c,strategy:f}=e,{boundary:p="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:m=!1,padding:v=0}=ie(t,e),w=Vr(v),h=c[m?u==="floating"?"reference":"floating":u],x=pt(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(h)))==null||n?h:h.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:p,rootBoundary:d,strategy:f})),b=u==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),C=await(a.isElement==null?void 0:a.isElement(y))?await(a.getScale==null?void 0:a.getScale(y))||{x:1,y:1}:{x:1,y:1},E=pt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:y,strategy:f}):b);return{top:(x.top-E.top+w.top)/C.y,bottom:(E.bottom-x.bottom+w.bottom)/C.y,left:(x.left-E.left+w.left)/C.x,right:(E.right-x.right+w.right)/C.x}}const qi=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:c,middlewareData:f}=t,{element:p,padding:d=0}=ie(e,t)||{};if(p==null)return{};const u=Vr(d),m={x:n,y:r},v=pn(o),w=fn(v),g=await i.getDimensions(p),h=v==="y",x=h?"top":"left",b=h?"bottom":"right",y=h?"clientHeight":"clientWidth",C=a.reference[w]+a.reference[v]-m[v]-a.floating[w],E=m[v]-a.reference[v],M=await(i.getOffsetParent==null?void 0:i.getOffsetParent(p));let S=M?M[y]:0;(!S||!await(i.isElement==null?void 0:i.isElement(M)))&&(S=c.floating[y]||a.floating[w]);const _=C/2-E/2,D=S/2-g[w]/2-1,j=fe(u[x],D),F=fe(u[b],D),$=j,k=S-g[w]-F,O=S/2-g[w]/2+_,H=Xt($,O,k),T=!f.arrow&&je(o)!=null&&O!==H&&a.reference[w]/2-(O<$?j:F)-g[w]/2<0,B=T?O<$?O-$:O-k:0;return{[v]:m[v]+B,data:{[v]:H,centerOffset:O-H-B,...T&&{alignmentOffset:B}},reset:T}}}),Xi=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:c,platform:f,elements:p}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:g=!0,...h}=ie(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const x=ce(o),b=te(c),y=ce(c)===c,C=await(f.isRTL==null?void 0:f.isRTL(p.floating)),E=m||(y||!g?[ft(c)]:Hi(c)),M=w!=="none";!m&&M&&E.push(...zi(c,g,w,C));const S=[c,...E],_=await He(t,h),D=[];let j=((r=a.flip)==null?void 0:r.overflows)||[];if(d&&D.push(_[x]),u){const O=Wi(o,i,C);D.push(_[O[0]],_[O[1]])}if(j=[...j,{placement:o,overflows:D}],!D.every(O=>O<=0)){var F,$;const O=(((F=a.flip)==null?void 0:F.index)||0)+1,H=S[O];if(H&&(!(u==="alignment"?b!==te(H):!1)||j.every(P=>P.overflows[0]>0&&te(P.placement)===b)))return{data:{index:O,overflows:j},reset:{placement:H}};let T=($=j.filter(B=>B.overflows[0]<=0).sort((B,P)=>B.overflows[1]-P.overflows[1])[0])==null?void 0:$.placement;if(!T)switch(v){case"bestFit":{var k;const B=(k=j.filter(P=>{if(M){const R=te(P.placement);return R===b||R==="y"}return!0}).map(P=>[P.placement,P.overflows.filter(R=>R>0).reduce((R,L)=>R+L,0)]).sort((P,R)=>P[1]-R[1])[0])==null?void 0:k[0];B&&(T=B);break}case"initialPlacement":T=c;break}if(o!==T)return{reset:{placement:T}}}return{}}}};function nr(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function rr(e){return Li.some(t=>e[t]>=0)}const Zi=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ie(e,t);switch(r){case"referenceHidden":{const a=await He(t,{...o,elementContext:"reference"}),i=nr(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:rr(i)}}}case"escaped":{const a=await He(t,{...o,altBoundary:!0}),i=nr(a,n.floating);return{data:{escapedOffsets:i,escaped:rr(i)}}}default:return{}}}}},Yr=new Set(["left","top"]);async function Qi(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=ce(n),c=je(n),f=te(n)==="y",p=Yr.has(i)?-1:1,d=a&&f?-1:1,u=ie(t,e);let{mainAxis:m,crossAxis:v,alignmentAxis:w}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof w=="number"&&(v=c==="end"?w*-1:w),f?{x:v*d,y:m*p}:{x:m*p,y:v*d}}const Ji=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:c}=t,f=await Qi(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+f.x,y:a+f.y,data:{...f,placement:i}}}}},ec=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:h=>{let{x,y:b}=h;return{x,y:b}}},...f}=ie(e,t),p={x:n,y:r},d=await He(t,f),u=te(ce(o)),m=dn(u);let v=p[m],w=p[u];if(a){const h=m==="y"?"top":"left",x=m==="y"?"bottom":"right",b=v+d[h],y=v-d[x];v=Xt(b,v,y)}if(i){const h=u==="y"?"top":"left",x=u==="y"?"bottom":"right",b=w+d[h],y=w-d[x];w=Xt(b,w,y)}const g=c.fn({...t,[m]:v,[u]:w});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[m]:a,[u]:i}}}}}},tc=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:f=!0,crossAxis:p=!0}=ie(e,t),d={x:n,y:r},u=te(o),m=dn(u);let v=d[m],w=d[u];const g=ie(c,t),h=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(f){const y=m==="y"?"height":"width",C=a.reference[m]-a.floating[y]+h.mainAxis,E=a.reference[m]+a.reference[y]-h.mainAxis;v<C?v=C:v>E&&(v=E)}if(p){var x,b;const y=m==="y"?"width":"height",C=Yr.has(ce(o)),E=a.reference[u]-a.floating[y]+(C&&((x=i.offset)==null?void 0:x[u])||0)+(C?0:h.crossAxis),M=a.reference[u]+a.reference[y]+(C?0:((b=i.offset)==null?void 0:b[u])||0)-(C?h.crossAxis:0);w<E?w=E:w>M&&(w=M)}return{[m]:v,[u]:w}}}},nc=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:c}=t,{apply:f=()=>{},...p}=ie(e,t),d=await He(t,p),u=ce(o),m=je(o),v=te(o)==="y",{width:w,height:g}=a.floating;let h,x;u==="top"||u==="bottom"?(h=u,x=m===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(x=u,h=m==="end"?"top":"bottom");const b=g-d.top-d.bottom,y=w-d.left-d.right,C=fe(g-d[h],b),E=fe(w-d[x],y),M=!t.middlewareData.shift;let S=C,_=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(_=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=b),M&&!m){const j=K(d.left,0),F=K(d.right,0),$=K(d.top,0),k=K(d.bottom,0);v?_=w-2*(j!==0||F!==0?j+F:K(d.left,d.right)):S=g-2*($!==0||k!==0?$+k:K(d.top,d.bottom))}await f({...t,availableWidth:_,availableHeight:S});const D=await i.getDimensions(c.floating);return w!==D.width||g!==D.height?{reset:{rects:!0}}:{}}}};function Et(){return typeof window<"u"}function Te(e){return qr(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ae(e){var t;return(t=(qr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function qr(e){return Et()?e instanceof Node||e instanceof z(e).Node:!1}function X(e){return Et()?e instanceof Element||e instanceof z(e).Element:!1}function oe(e){return Et()?e instanceof HTMLElement||e instanceof z(e).HTMLElement:!1}function or(e){return!Et()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof z(e).ShadowRoot}const rc=new Set(["inline","contents"]);function Ve(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!rc.has(o)}const oc=new Set(["table","td","th"]);function ac(e){return oc.has(Te(e))}const sc=[":popover-open",":modal"];function St(e){return sc.some(t=>{try{return e.matches(t)}catch{return!1}})}const ic=["transform","translate","scale","rotate","perspective"],cc=["transform","translate","scale","rotate","perspective","filter"],lc=["paint","layout","strict","content"];function mn(e){const t=hn(),n=X(e)?Z(e):e;return ic.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||cc.some(r=>(n.willChange||"").includes(r))||lc.some(r=>(n.contain||"").includes(r))}function uc(e){let t=pe(e);for(;oe(t)&&!Pe(t);){if(mn(t))return t;if(St(t))return null;t=pe(t)}return null}function hn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const dc=new Set(["html","body","#document"]);function Pe(e){return dc.has(Te(e))}function Z(e){return z(e).getComputedStyle(e)}function Rt(e){return X(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function pe(e){if(Te(e)==="html")return e;const t=e.assignedSlot||e.parentNode||or(e)&&e.host||ae(e);return or(t)?t.host:t}function Xr(e){const t=pe(e);return Pe(t)?e.ownerDocument?e.ownerDocument.body:e.body:oe(t)&&Ve(t)?t:Xr(t)}function Ue(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Xr(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),i=z(o);if(a){const c=Qt(i);return t.concat(i,i.visualViewport||[],Ve(o)?o:[],c&&n?Ue(c):[])}return t.concat(o,Ue(o,[],n))}function Qt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Zr(e){const t=Z(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=oe(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=dt(n)!==a||dt(r)!==i;return c&&(n=a,r=i),{width:n,height:r,$:c}}function gn(e){return X(e)?e:e.contextElement}function _e(e){const t=gn(e);if(!oe(t))return ne(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Zr(t);let i=(a?dt(n.width):n.width)/r,c=(a?dt(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const fc=ne(0);function Qr(e){const t=z(e);return!hn()||!t.visualViewport?fc:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function pc(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==z(e)?!1:t}function be(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=gn(e);let i=ne(1);t&&(r?X(r)&&(i=_e(r)):i=_e(e));const c=pc(a,n,r)?Qr(a):ne(0);let f=(o.left+c.x)/i.x,p=(o.top+c.y)/i.y,d=o.width/i.x,u=o.height/i.y;if(a){const m=z(a),v=r&&X(r)?z(r):r;let w=m,g=Qt(w);for(;g&&r&&v!==w;){const h=_e(g),x=g.getBoundingClientRect(),b=Z(g),y=x.left+(g.clientLeft+parseFloat(b.paddingLeft))*h.x,C=x.top+(g.clientTop+parseFloat(b.paddingTop))*h.y;f*=h.x,p*=h.y,d*=h.x,u*=h.y,f+=y,p+=C,w=z(g),g=Qt(w)}}return pt({width:d,height:u,x:f,y:p})}function vn(e,t){const n=Rt(e).scrollLeft;return t?t.left+n:be(ae(e)).left+n}function Jr(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:vn(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function mc(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",i=ae(r),c=t?St(t.floating):!1;if(r===i||c&&a)return n;let f={scrollLeft:0,scrollTop:0},p=ne(1);const d=ne(0),u=oe(r);if((u||!u&&!a)&&((Te(r)!=="body"||Ve(i))&&(f=Rt(r)),oe(r))){const v=be(r);p=_e(r),d.x=v.x+r.clientLeft,d.y=v.y+r.clientTop}const m=i&&!u&&!a?Jr(i,f,!0):ne(0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-f.scrollLeft*p.x+d.x+m.x,y:n.y*p.y-f.scrollTop*p.y+d.y+m.y}}function hc(e){return Array.from(e.getClientRects())}function gc(e){const t=ae(e),n=Rt(e),r=e.ownerDocument.body,o=K(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=K(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+vn(e);const c=-n.scrollTop;return Z(r).direction==="rtl"&&(i+=K(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:c}}function vc(e,t){const n=z(e),r=ae(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,c=0,f=0;if(o){a=o.width,i=o.height;const p=hn();(!p||p&&t==="fixed")&&(c=o.offsetLeft,f=o.offsetTop)}return{width:a,height:i,x:c,y:f}}const xc=new Set(["absolute","fixed"]);function wc(e,t){const n=be(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=oe(e)?_e(e):ne(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,f=o*a.x,p=r*a.y;return{width:i,height:c,x:f,y:p}}function ar(e,t,n){let r;if(t==="viewport")r=vc(e,n);else if(t==="document")r=gc(ae(e));else if(X(t))r=wc(t,n);else{const o=Qr(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return pt(r)}function eo(e,t){const n=pe(e);return n===t||!X(n)||Pe(n)?!1:Z(n).position==="fixed"||eo(n,t)}function bc(e,t){const n=t.get(e);if(n)return n;let r=Ue(e,[],!1).filter(c=>X(c)&&Te(c)!=="body"),o=null;const a=Z(e).position==="fixed";let i=a?pe(e):e;for(;X(i)&&!Pe(i);){const c=Z(i),f=mn(i);!f&&c.position==="fixed"&&(o=null),(a?!f&&!o:!f&&c.position==="static"&&!!o&&xc.has(o.position)||Ve(i)&&!f&&eo(e,i))?r=r.filter(d=>d!==i):o=c,i=pe(i)}return t.set(e,r),r}function yc(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?St(t)?[]:bc(t,this._c):[].concat(n),r],c=i[0],f=i.reduce((p,d)=>{const u=ar(t,d,o);return p.top=K(u.top,p.top),p.right=fe(u.right,p.right),p.bottom=fe(u.bottom,p.bottom),p.left=K(u.left,p.left),p},ar(t,c,o));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Cc(e){const{width:t,height:n}=Zr(e);return{width:t,height:n}}function Ec(e,t,n){const r=oe(t),o=ae(t),a=n==="fixed",i=be(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const f=ne(0);function p(){f.x=vn(o)}if(r||!r&&!a)if((Te(t)!=="body"||Ve(o))&&(c=Rt(t)),r){const v=be(t,!0,a,t);f.x=v.x+t.clientLeft,f.y=v.y+t.clientTop}else o&&p();a&&!r&&o&&p();const d=o&&!r&&!a?Jr(o,c):ne(0),u=i.left+c.scrollLeft-f.x-d.x,m=i.top+c.scrollTop-f.y-d.y;return{x:u,y:m,width:i.width,height:i.height}}function Wt(e){return Z(e).position==="static"}function sr(e,t){if(!oe(e)||Z(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ae(e)===n&&(n=n.ownerDocument.body),n}function to(e,t){const n=z(e);if(St(e))return n;if(!oe(e)){let o=pe(e);for(;o&&!Pe(o);){if(X(o)&&!Wt(o))return o;o=pe(o)}return n}let r=sr(e,t);for(;r&&ac(r)&&Wt(r);)r=sr(r,t);return r&&Pe(r)&&Wt(r)&&!mn(r)?n:r||uc(e)||n}const Sc=async function(e){const t=this.getOffsetParent||to,n=this.getDimensions,r=await n(e.floating);return{reference:Ec(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Rc(e){return Z(e).direction==="rtl"}const Mc={convertOffsetParentRelativeRectToViewportRelativeRect:mc,getDocumentElement:ae,getClippingRect:yc,getOffsetParent:to,getElementRects:Sc,getClientRects:hc,getDimensions:Cc,getScale:_e,isElement:X,isRTL:Rc};function no(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ac(e,t){let n=null,r;const o=ae(e);function a(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,f){c===void 0&&(c=!1),f===void 0&&(f=1),a();const p=e.getBoundingClientRect(),{left:d,top:u,width:m,height:v}=p;if(c||t(),!m||!v)return;const w=ot(u),g=ot(o.clientWidth-(d+m)),h=ot(o.clientHeight-(u+v)),x=ot(d),y={rootMargin:-w+"px "+-g+"px "+-h+"px "+-x+"px",threshold:K(0,fe(1,f))||1};let C=!0;function E(M){const S=M[0].intersectionRatio;if(S!==f){if(!C)return i();S?i(!1,S):r=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!no(p,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(E,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,y)}n.observe(e)}return i(!0),a}function _c(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:f=!1}=r,p=gn(e),d=o||a?[...p?Ue(p):[],...Ue(t)]:[];d.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),a&&x.addEventListener("resize",n)});const u=p&&c?Ac(p,n):null;let m=-1,v=null;i&&(v=new ResizeObserver(x=>{let[b]=x;b&&b.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var y;(y=v)==null||y.observe(t)})),n()}),p&&!f&&v.observe(p),v.observe(t));let w,g=f?be(e):null;f&&h();function h(){const x=be(e);g&&!no(g,x)&&n(),g=x,w=requestAnimationFrame(h)}return n(),()=>{var x;d.forEach(b=>{o&&b.removeEventListener("scroll",n),a&&b.removeEventListener("resize",n)}),u==null||u(),(x=v)==null||x.disconnect(),v=null,f&&cancelAnimationFrame(w)}}const Pc=Ji,Nc=ec,jc=Xi,Tc=nc,Dc=Zi,ir=qi,Oc=tc,Ic=(e,t,n)=>{const r=new Map,o={platform:Mc,...n},a={...o.platform,_c:r};return Yi(e,t,{...o,platform:a})};var kc=typeof document<"u",Lc=function(){},ct=kc?l.useLayoutEffect:Lc;function mt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!mt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!mt(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function ro(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function cr(e,t){const n=ro(e);return Math.round(t*n)/n}function Ht(e){const t=l.useRef(e);return ct(()=>{t.current=e}),t}function Fc(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:f,open:p}=e,[d,u]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,v]=l.useState(r);mt(m,r)||v(r);const[w,g]=l.useState(null),[h,x]=l.useState(null),b=l.useCallback(P=>{P!==M.current&&(M.current=P,g(P))},[]),y=l.useCallback(P=>{P!==S.current&&(S.current=P,x(P))},[]),C=a||w,E=i||h,M=l.useRef(null),S=l.useRef(null),_=l.useRef(d),D=f!=null,j=Ht(f),F=Ht(o),$=Ht(p),k=l.useCallback(()=>{if(!M.current||!S.current)return;const P={placement:t,strategy:n,middleware:m};F.current&&(P.platform=F.current),Ic(M.current,S.current,P).then(R=>{const L={...R,isPositioned:$.current!==!1};O.current&&!mt(_.current,L)&&(_.current=L,Fa.flushSync(()=>{u(L)}))})},[m,t,n,F,$]);ct(()=>{p===!1&&_.current.isPositioned&&(_.current.isPositioned=!1,u(P=>({...P,isPositioned:!1})))},[p]);const O=l.useRef(!1);ct(()=>(O.current=!0,()=>{O.current=!1}),[]),ct(()=>{if(C&&(M.current=C),E&&(S.current=E),C&&E){if(j.current)return j.current(C,E,k);k()}},[C,E,k,j,D]);const H=l.useMemo(()=>({reference:M,floating:S,setReference:b,setFloating:y}),[b,y]),T=l.useMemo(()=>({reference:C,floating:E}),[C,E]),B=l.useMemo(()=>{const P={position:n,left:0,top:0};if(!T.floating)return P;const R=cr(T.floating,d.x),L=cr(T.floating,d.y);return c?{...P,transform:"translate("+R+"px, "+L+"px)",...ro(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:L}},[n,c,T.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:k,refs:H,elements:T,floatingStyles:B}),[d,k,H,T,B])}const $c=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ir({element:r.current,padding:o}).fn(n):{}:r?ir({element:r,padding:o}).fn(n):{}}}},Bc=(e,t)=>({...Pc(e),options:[e,t]}),Wc=(e,t)=>({...Nc(e),options:[e,t]}),Hc=(e,t)=>({...Oc(e),options:[e,t]}),Uc=(e,t)=>({...jc(e),options:[e,t]}),Gc=(e,t)=>({...Tc(e),options:[e,t]}),Kc=(e,t)=>({...Dc(e),options:[e,t]}),zc=(e,t)=>({...$c(e),options:[e,t]});var Vc="Arrow",oo=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return s.jsx(I.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});oo.displayName=Vc;var Yc=oo,xn="Popper",[ao,Mt]=me(xn),[qc,so]=ao(xn),io=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return s.jsx(qc,{scope:t,anchor:r,onAnchorChange:o,children:n})};io.displayName=xn;var co="PopperAnchor",lo=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=so(co,n),i=l.useRef(null),c=W(t,i);return l.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:s.jsx(I.div,{...o,ref:c})});lo.displayName=co;var wn="PopperContent",[Xc,Zc]=ao(wn),uo=l.forwardRef((e,t)=>{var se,Ie,V,ke,kn,Ln;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:d=0,sticky:u="partial",hideWhenDetached:m=!1,updatePositionStrategy:v="optimized",onPlaced:w,...g}=e,h=so(wn,n),[x,b]=l.useState(null),y=W(t,Le=>b(Le)),[C,E]=l.useState(null),M=Ia(C),S=(M==null?void 0:M.width)??0,_=(M==null?void 0:M.height)??0,D=r+(a!=="center"?"-"+a:""),j=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},F=Array.isArray(p)?p:[p],$=F.length>0,k={padding:j,boundary:F.filter(Jc),altBoundary:$},{refs:O,floatingStyles:H,placement:T,isPositioned:B,middlewareData:P}=Fc({strategy:"fixed",placement:D,whileElementsMounted:(...Le)=>_c(...Le,{animationFrame:v==="always"}),elements:{reference:h.anchor},middleware:[Bc({mainAxis:o+_,alignmentAxis:i}),f&&Wc({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?Hc():void 0,...k}),f&&Uc({...k}),Gc({...k,apply:({elements:Le,rects:Fn,availableWidth:Ma,availableHeight:Aa})=>{const{width:_a,height:Pa}=Fn.reference,Je=Le.floating.style;Je.setProperty("--radix-popper-available-width",`${Ma}px`),Je.setProperty("--radix-popper-available-height",`${Aa}px`),Je.setProperty("--radix-popper-anchor-width",`${_a}px`),Je.setProperty("--radix-popper-anchor-height",`${Pa}px`)}}),C&&zc({element:C,padding:c}),el({arrowWidth:S,arrowHeight:_}),m&&Kc({strategy:"referenceHidden",...k})]}),[R,L]=mo(T),U=re(w);xe(()=>{B&&(U==null||U())},[B,U]);const J=(se=P.arrow)==null?void 0:se.x,De=(Ie=P.arrow)==null?void 0:Ie.y,Oe=((V=P.arrow)==null?void 0:V.centerOffset)!==0,[Qe,he]=l.useState();return xe(()=>{x&&he(window.getComputedStyle(x).zIndex)},[x]),s.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:B?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Qe,"--radix-popper-transform-origin":[(ke=P.transformOrigin)==null?void 0:ke.x,(kn=P.transformOrigin)==null?void 0:kn.y].join(" "),...((Ln=P.hide)==null?void 0:Ln.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(Xc,{scope:n,placedSide:R,onArrowChange:E,arrowX:J,arrowY:De,shouldHideArrow:Oe,children:s.jsx(I.div,{"data-side":R,"data-align":L,...g,ref:y,style:{...g.style,animation:B?void 0:"none"}})})})});uo.displayName=wn;var fo="PopperArrow",Qc={top:"bottom",right:"left",bottom:"top",left:"right"},po=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=Zc(fo,r),i=Qc[a.placedSide];return s.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:s.jsx(Yc,{...o,ref:n,style:{...o.style,display:"block"}})})});po.displayName=fo;function Jc(e){return e!==null}var el=e=>({name:"transformOrigin",options:e,fn(t){var h,x,b;const{placement:n,rects:r,middlewareData:o}=t,i=((h=o.arrow)==null?void 0:h.centerOffset)!==0,c=i?0:e.arrowWidth,f=i?0:e.arrowHeight,[p,d]=mo(n),u={start:"0%",center:"50%",end:"100%"}[d],m=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,v=(((b=o.arrow)==null?void 0:b.y)??0)+f/2;let w="",g="";return p==="bottom"?(w=i?u:`${m}px`,g=`${-f}px`):p==="top"?(w=i?u:`${m}px`,g=`${r.floating.height+f}px`):p==="right"?(w=`${-f}px`,g=i?u:`${v}px`):p==="left"&&(w=`${r.floating.width+f}px`,g=i?u:`${v}px`),{data:{x:w,y:g}}}});function mo(e){const[t,n="center"]=e.split("-");return[t,n]}var ho=io,go=lo,vo=uo,xo=po,tl=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),nl="VisuallyHidden",wo=l.forwardRef((e,t)=>s.jsx(I.span,{...e,ref:t,style:{...tl,...e.style}}));wo.displayName=nl;var rl=wo,[At,mf]=me("Tooltip",[Mt]),_t=Mt(),bo="TooltipProvider",ol=700,Jt="tooltip.open",[al,bn]=At(bo),yo=e=>{const{__scopeTooltip:t,delayDuration:n=ol,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,i=l.useRef(!0),c=l.useRef(!1),f=l.useRef(0);return l.useEffect(()=>{const p=f.current;return()=>window.clearTimeout(p)},[]),s.jsx(al,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(f.current),i.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:l.useCallback(p=>{c.current=p},[]),disableHoverableContent:o,children:a})};yo.displayName=bo;var Ge="Tooltip",[sl,Ye]=At(Ge),Co=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,f=bn(Ge,e.__scopeTooltip),p=_t(t),[d,u]=l.useState(null),m=ve(),v=l.useRef(0),w=i??f.disableHoverableContent,g=c??f.delayDuration,h=l.useRef(!1),[x,b]=xt({prop:r,defaultProp:o??!1,onChange:S=>{S?(f.onOpen(),document.dispatchEvent(new CustomEvent(Jt))):f.onClose(),a==null||a(S)},caller:Ge}),y=l.useMemo(()=>x?h.current?"delayed-open":"instant-open":"closed",[x]),C=l.useCallback(()=>{window.clearTimeout(v.current),v.current=0,h.current=!1,b(!0)},[b]),E=l.useCallback(()=>{window.clearTimeout(v.current),v.current=0,b(!1)},[b]),M=l.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{h.current=!0,b(!0),v.current=0},g)},[g,b]);return l.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),s.jsx(ho,{...p,children:s.jsx(sl,{scope:t,contentId:m,open:x,stateAttribute:y,trigger:d,onTriggerChange:u,onTriggerEnter:l.useCallback(()=>{f.isOpenDelayedRef.current?M():C()},[f.isOpenDelayedRef,M,C]),onTriggerLeave:l.useCallback(()=>{w?E():(window.clearTimeout(v.current),v.current=0)},[E,w]),onOpen:C,onClose:E,disableHoverableContent:w,children:n})})};Co.displayName=Ge;var en="TooltipTrigger",Eo=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Ye(en,n),a=bn(en,n),i=_t(n),c=l.useRef(null),f=W(t,c,o.onTriggerChange),p=l.useRef(!1),d=l.useRef(!1),u=l.useCallback(()=>p.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),s.jsx(go,{asChild:!0,...i,children:s.jsx(I.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:f,onPointerMove:A(e.onPointerMove,m=>{m.pointerType!=="touch"&&!d.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:A(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:A(e.onPointerDown,()=>{o.open&&o.onClose(),p.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:A(e.onFocus,()=>{p.current||o.onOpen()}),onBlur:A(e.onBlur,o.onClose),onClick:A(e.onClick,o.onClose)})})});Eo.displayName=en;var yn="TooltipPortal",[il,cl]=At(yn,{forceMount:void 0}),So=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,a=Ye(yn,t);return s.jsx(il,{scope:t,forceMount:n,children:s.jsx(le,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:r})})})};So.displayName=yn;var Ne="TooltipContent",Ro=l.forwardRef((e,t)=>{const n=cl(Ne,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=Ye(Ne,e.__scopeTooltip);return s.jsx(le,{present:r||i.open,children:i.disableHoverableContent?s.jsx(Mo,{side:o,...a,ref:t}):s.jsx(ll,{side:o,...a,ref:t})})}),ll=l.forwardRef((e,t)=>{const n=Ye(Ne,e.__scopeTooltip),r=bn(Ne,e.__scopeTooltip),o=l.useRef(null),a=W(t,o),[i,c]=l.useState(null),{trigger:f,onClose:p}=n,d=o.current,{onPointerInTransitChange:u}=r,m=l.useCallback(()=>{c(null),u(!1)},[u]),v=l.useCallback((w,g)=>{const h=w.currentTarget,x={x:w.clientX,y:w.clientY},b=pl(x,h.getBoundingClientRect()),y=ml(x,b),C=hl(g.getBoundingClientRect()),E=vl([...y,...C]);c(E),u(!0)},[u]);return l.useEffect(()=>()=>m(),[m]),l.useEffect(()=>{if(f&&d){const w=h=>v(h,d),g=h=>v(h,f);return f.addEventListener("pointerleave",w),d.addEventListener("pointerleave",g),()=>{f.removeEventListener("pointerleave",w),d.removeEventListener("pointerleave",g)}}},[f,d,v,m]),l.useEffect(()=>{if(i){const w=g=>{const h=g.target,x={x:g.clientX,y:g.clientY},b=(f==null?void 0:f.contains(h))||(d==null?void 0:d.contains(h)),y=!gl(x,i);b?m():y&&(m(),p())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[f,d,i,p,m]),s.jsx(Mo,{...e,ref:a})}),[ul,dl]=At(Ge,{isInside:!1}),fl=Ta("TooltipContent"),Mo=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,f=Ye(Ne,n),p=_t(n),{onClose:d}=f;return l.useEffect(()=>(document.addEventListener(Jt,d),()=>document.removeEventListener(Jt,d)),[d]),l.useEffect(()=>{if(f.trigger){const u=m=>{const v=m.target;v!=null&&v.contains(f.trigger)&&d()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[f.trigger,d]),s.jsx(wt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:u=>u.preventDefault(),onDismiss:d,children:s.jsxs(vo,{"data-state":f.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(fl,{children:r}),s.jsx(ul,{scope:n,isInside:!0,children:s.jsx(rl,{id:f.contentId,role:"tooltip",children:o||r})})]})})});Ro.displayName=Ne;var Ao="TooltipArrow",_o=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=_t(n);return dl(Ao,n).isInside?null:s.jsx(xo,{...o,...r,ref:t})});_o.displayName=Ao;function pl(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function ml(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function hl(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function gl(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],f=t[i],p=c.x,d=c.y,u=f.x,m=f.y;d>r!=m>r&&n<(u-p)*(r-d)/(m-d)+p&&(o=!o)}return o}function vl(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),xl(t)}function xl(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var wl=yo,bl=Co,yl=Eo,Cl=So,El=Ro,Sl=_o;function Po({delayDuration:e=0,...t}){return s.jsx(wl,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Rl({...e}){return s.jsx(Po,{children:s.jsx(bl,{"data-slot":"tooltip",...e})})}function Ml({...e}){return s.jsx(yl,{"data-slot":"tooltip-trigger",...e})}function Al({className:e,sideOffset:t=4,children:n,...r}){return s.jsx(Cl,{children:s.jsxs(El,{"data-slot":"tooltip-content",sideOffset:t,className:N("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,s.jsx(Sl,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const _l="sidebar_state",Pl=3600*24*7,Nl="16rem",jl="18rem",Tl="3rem",Dl="b",No=l.createContext(null);function Pt(){const e=l.useContext(No);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ol({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:a,...i}){const c=xr(),[f,p]=l.useState(!1),[d,u]=l.useState(e),m=t??d,v=l.useCallback(x=>{const b=typeof x=="function"?x(m):x;n?n(b):u(b),document.cookie=`${_l}=${b}; path=/; max-age=${Pl}`},[n,m]),w=l.useCallback(()=>c?p(x=>!x):v(x=>!x),[c,v,p]);l.useEffect(()=>{const x=b=>{b.key===Dl&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),w())};return window.addEventListener("keydown",x),()=>window.removeEventListener("keydown",x)},[w]);const g=m?"expanded":"collapsed",h=l.useMemo(()=>({state:g,open:m,setOpen:v,isMobile:c,openMobile:f,setOpenMobile:p,toggleSidebar:w}),[g,m,v,c,f,p,w]);return s.jsx(No.Provider,{value:h,children:s.jsx(Po,{delayDuration:0,children:s.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Nl,"--sidebar-width-icon":Tl,...o},className:N("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...i,children:a})})})}function Il({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...a}){const{isMobile:i,state:c,openMobile:f,setOpenMobile:p}=Pt();return n==="none"?s.jsx("div",{"data-slot":"sidebar",className:N("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...a,children:o}):i?s.jsxs(Ni,{open:f,onOpenChange:p,...a,children:[s.jsxs(Oi,{className:"sr-only",children:[s.jsx(Ii,{children:"Sidebar"}),s.jsx(ki,{children:"Displays the mobile sidebar."})]}),s.jsx(Di,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":jl},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):s.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[s.jsx("div",{className:N("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),s.jsx("div",{className:N("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:s.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function kl({className:e,onClick:t,...n}){const{toggleSidebar:r}=Pt();return s.jsxs(Be,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:N("h-7 w-7",e),onClick:o=>{t==null||t(o),r()},...n,children:[s.jsx(ss,{}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Ll({className:e,...t}){return s.jsx("main",{"data-slot":"sidebar-inset",className:N("bg-background relative flex min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Fl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:N("flex flex-col gap-2 p-2",e),...t})}function $l({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:N("flex flex-col gap-2 p-2",e),...t})}function Bl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:N("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function Wl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:N("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Hl({className:e,asChild:t=!1,...n}){const r=t?vt:"div";return s.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:N("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function Cn({className:e,...t}){return s.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:N("flex w-full min-w-0 flex-col gap-1",e),...t})}function En({className:e,...t}){return s.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:N("group/menu-item relative",e),...t})}const Ul=mr("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Sn({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:a,...i}){const c=e?vt:"button",{isMobile:f,state:p}=Pt(),d=s.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:N(Ul({variant:n,size:r}),a),...i});return o?(typeof o=="string"&&(o={children:o}),s.jsxs(Rl,{children:[s.jsx(Ml,{asChild:!0,children:d}),s.jsx(Al,{side:"right",align:"center",hidden:p!=="collapsed"||f,...o})]})):d}function Gl({variant:e="header",children:t,...n}){return e==="sidebar"?s.jsx(Ll,{...n,children:t}):s.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function Kl({children:e,variant:t="header"}){const[n,r]=l.useState(()=>typeof window<"u"?localStorage.getItem("sidebar")!=="false":!0),o=a=>{r(a),typeof window<"u"&&localStorage.setItem("sidebar",String(a))};return t==="header"?s.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):s.jsx(Ol,{defaultOpen:n,open:n,onOpenChange:o,children:e})}function zl({items:e=[]}){const t=Me();return s.jsxs(Wl,{className:"px-2 py-0",children:[s.jsx(Hl,{}),s.jsx(Cn,{children:e.map(n=>s.jsx(En,{children:s.jsx(Sn,{asChild:!0,isActive:t.url.startsWith(n.url),children:s.jsxs(de,{href:n.url,prefetch:!0,children:[n.icon&&s.jsx(n.icon,{}),s.jsx("span",{children:n.title})]})})},n.title))})]})}function jo(e){const t=e+"CollectionProvider",[n,r]=me(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:h,children:x}=g,b=ge.useRef(null),y=ge.useRef(new Map).current;return s.jsx(o,{scope:h,itemMap:y,collectionRef:b,children:x})};i.displayName=t;const c=e+"CollectionSlot",f=lt(c),p=ge.forwardRef((g,h)=>{const{scope:x,children:b}=g,y=a(c,x),C=W(h,y.collectionRef);return s.jsx(f,{ref:C,children:b})});p.displayName=c;const d=e+"CollectionItemSlot",u="data-radix-collection-item",m=lt(d),v=ge.forwardRef((g,h)=>{const{scope:x,children:b,...y}=g,C=ge.useRef(null),E=W(h,C),M=a(d,x);return ge.useEffect(()=>(M.itemMap.set(C,{ref:C,...y}),()=>void M.itemMap.delete(C))),s.jsx(m,{[u]:"",ref:E,children:b})});v.displayName=d;function w(g){const h=a(e+"CollectionConsumer",g);return ge.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const y=Array.from(b.querySelectorAll(`[${u}]`));return Array.from(h.itemMap.values()).sort((M,S)=>y.indexOf(M.ref.current)-y.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:i,Slot:p,ItemSlot:v},w,r]}var Vl=l.createContext(void 0);function To(e){const t=l.useContext(Vl);return e||t||"ltr"}var Ut="rovingFocusGroup.onEntryFocus",Yl={bubbles:!1,cancelable:!0},qe="RovingFocusGroup",[tn,Do,ql]=jo(qe),[Xl,Oo]=me(qe,[ql]),[Zl,Ql]=Xl(qe),Io=l.forwardRef((e,t)=>s.jsx(tn.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(tn.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(Jl,{...e,ref:t})})}));Io.displayName=qe;var Jl=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:d=!1,...u}=e,m=l.useRef(null),v=W(t,m),w=To(a),[g,h]=xt({prop:i,defaultProp:c??null,onChange:f,caller:qe}),[x,b]=l.useState(!1),y=re(p),C=Do(n),E=l.useRef(!1),[M,S]=l.useState(0);return l.useEffect(()=>{const _=m.current;if(_)return _.addEventListener(Ut,y),()=>_.removeEventListener(Ut,y)},[y]),s.jsx(Zl,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:g,onItemFocus:l.useCallback(_=>h(_),[h]),onItemShiftTab:l.useCallback(()=>b(!0),[]),onFocusableItemAdd:l.useCallback(()=>S(_=>_+1),[]),onFocusableItemRemove:l.useCallback(()=>S(_=>_-1),[]),children:s.jsx(I.div,{tabIndex:x||M===0?-1:0,"data-orientation":r,...u,ref:v,style:{outline:"none",...e.style},onMouseDown:A(e.onMouseDown,()=>{E.current=!0}),onFocus:A(e.onFocus,_=>{const D=!E.current;if(_.target===_.currentTarget&&D&&!x){const j=new CustomEvent(Ut,Yl);if(_.currentTarget.dispatchEvent(j),!j.defaultPrevented){const F=C().filter(T=>T.focusable),$=F.find(T=>T.active),k=F.find(T=>T.id===g),H=[$,k,...F].filter(Boolean).map(T=>T.ref.current);Fo(H,d)}}E.current=!1}),onBlur:A(e.onBlur,()=>b(!1))})})}),ko="RovingFocusGroupItem",Lo=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:i,...c}=e,f=ve(),p=a||f,d=Ql(ko,n),u=d.currentTabStopId===p,m=Do(n),{onFocusableItemAdd:v,onFocusableItemRemove:w,currentTabStopId:g}=d;return l.useEffect(()=>{if(r)return v(),()=>w()},[r,v,w]),s.jsx(tn.ItemSlot,{scope:n,id:p,focusable:r,active:o,children:s.jsx(I.span,{tabIndex:u?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:A(e.onMouseDown,h=>{r?d.onItemFocus(p):h.preventDefault()}),onFocus:A(e.onFocus,()=>d.onItemFocus(p)),onKeyDown:A(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){d.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const x=nu(h,d.orientation,d.dir);if(x!==void 0){if(h.metaKey||h.ctrlKey||h.altKey||h.shiftKey)return;h.preventDefault();let y=m().filter(C=>C.focusable).map(C=>C.ref.current);if(x==="last")y.reverse();else if(x==="prev"||x==="next"){x==="prev"&&y.reverse();const C=y.indexOf(h.currentTarget);y=d.loop?ru(y,C+1):y.slice(C+1)}setTimeout(()=>Fo(y))}}),children:typeof i=="function"?i({isCurrentTabStop:u,hasTabStop:g!=null}):i})})});Lo.displayName=ko;var eu={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tu(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function nu(e,t,n){const r=tu(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return eu[r]}function Fo(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function ru(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var ou=Io,au=Lo,nn=["Enter"," "],su=["ArrowDown","PageUp","Home"],$o=["ArrowUp","PageDown","End"],iu=[...su,...$o],cu={ltr:[...nn,"ArrowRight"],rtl:[...nn,"ArrowLeft"]},lu={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Xe="Menu",[Ke,uu,du]=jo(Xe),[ye,Bo]=me(Xe,[du,Mt,Oo]),Nt=Mt(),Wo=Oo(),[fu,Ce]=ye(Xe),[pu,Ze]=ye(Xe),Ho=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:i=!0}=e,c=Nt(t),[f,p]=l.useState(null),d=l.useRef(!1),u=re(a),m=To(o);return l.useEffect(()=>{const v=()=>{d.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>d.current=!1;return document.addEventListener("keydown",v,{capture:!0}),()=>{document.removeEventListener("keydown",v,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),s.jsx(ho,{...c,children:s.jsx(fu,{scope:t,open:n,onOpenChange:u,content:f,onContentChange:p,children:s.jsx(pu,{scope:t,onClose:l.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:d,dir:m,modal:i,children:r})})})};Ho.displayName=Xe;var mu="MenuAnchor",Rn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Nt(n);return s.jsx(go,{...o,...r,ref:t})});Rn.displayName=mu;var Mn="MenuPortal",[hu,Uo]=ye(Mn,{forceMount:void 0}),Go=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=Ce(Mn,t);return s.jsx(hu,{scope:t,forceMount:n,children:s.jsx(le,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:r})})})};Go.displayName=Mn;var Y="MenuContent",[gu,An]=ye(Y),Ko=l.forwardRef((e,t)=>{const n=Uo(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=Ce(Y,e.__scopeMenu),i=Ze(Y,e.__scopeMenu);return s.jsx(Ke.Provider,{scope:e.__scopeMenu,children:s.jsx(le,{present:r||a.open,children:s.jsx(Ke.Slot,{scope:e.__scopeMenu,children:i.modal?s.jsx(vu,{...o,ref:t}):s.jsx(xu,{...o,ref:t})})})})}),vu=l.forwardRef((e,t)=>{const n=Ce(Y,e.__scopeMenu),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return jr(a)},[]),s.jsx(_n,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),xu=l.forwardRef((e,t)=>{const n=Ce(Y,e.__scopeMenu);return s.jsx(_n,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),wu=lt("MenuContent.ScrollLock"),_n=l.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:v,disableOutsideScroll:w,...g}=e,h=Ce(Y,n),x=Ze(Y,n),b=Nt(n),y=Wo(n),C=uu(n),[E,M]=l.useState(null),S=l.useRef(null),_=W(t,S,h.onContentChange),D=l.useRef(0),j=l.useRef(""),F=l.useRef(0),$=l.useRef(null),k=l.useRef("right"),O=l.useRef(0),H=w?sn:l.Fragment,T=w?{as:wu,allowPinchZoom:!0}:void 0,B=R=>{var se,Ie;const L=j.current+R,U=C().filter(V=>!V.disabled),J=document.activeElement,De=(se=U.find(V=>V.ref.current===J))==null?void 0:se.textValue,Oe=U.map(V=>V.textValue),Qe=ju(Oe,L,De),he=(Ie=U.find(V=>V.textValue===Qe))==null?void 0:Ie.ref.current;(function V(ke){j.current=ke,window.clearTimeout(D.current),ke!==""&&(D.current=window.setTimeout(()=>V(""),1e3))})(L),he&&setTimeout(()=>he.focus())};l.useEffect(()=>()=>window.clearTimeout(D.current),[]),Cr();const P=l.useCallback(R=>{var U,J;return k.current===((U=$.current)==null?void 0:U.side)&&Du(R,(J=$.current)==null?void 0:J.area)},[]);return s.jsx(gu,{scope:n,searchRef:j,onItemEnter:l.useCallback(R=>{P(R)&&R.preventDefault()},[P]),onItemLeave:l.useCallback(R=>{var L;P(R)||((L=S.current)==null||L.focus(),M(null))},[P]),onTriggerLeave:l.useCallback(R=>{P(R)&&R.preventDefault()},[P]),pointerGraceTimerRef:F,onPointerGraceIntentChange:l.useCallback(R=>{$.current=R},[]),children:s.jsx(H,{...T,children:s.jsx(an,{asChild:!0,trapped:o,onMountAutoFocus:A(a,R=>{var L;R.preventDefault(),(L=S.current)==null||L.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:s.jsx(wt,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:p,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:v,children:s.jsx(ou,{asChild:!0,...y,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:M,onEntryFocus:A(f,R=>{x.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(vo,{role:"menu","aria-orientation":"vertical","data-state":ia(h.open),"data-radix-menu-content":"",dir:x.dir,...b,...g,ref:_,style:{outline:"none",...g.style},onKeyDown:A(g.onKeyDown,R=>{const U=R.target.closest("[data-radix-menu-content]")===R.currentTarget,J=R.ctrlKey||R.altKey||R.metaKey,De=R.key.length===1;U&&(R.key==="Tab"&&R.preventDefault(),!J&&De&&B(R.key));const Oe=S.current;if(R.target!==Oe||!iu.includes(R.key))return;R.preventDefault();const he=C().filter(se=>!se.disabled).map(se=>se.ref.current);$o.includes(R.key)&&he.reverse(),Pu(he)}),onBlur:A(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(D.current),j.current="")}),onPointerMove:A(e.onPointerMove,ze(R=>{const L=R.target,U=O.current!==R.clientX;if(R.currentTarget.contains(L)&&U){const J=R.clientX>O.current?"right":"left";k.current=J,O.current=R.clientX}}))})})})})})})});Ko.displayName=Y;var bu="MenuGroup",Pn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"group",...r,ref:t})});Pn.displayName=bu;var yu="MenuLabel",zo=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{...r,ref:t})});zo.displayName=yu;var ht="MenuItem",lr="menu.itemSelect",jt=l.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=l.useRef(null),i=Ze(ht,e.__scopeMenu),c=An(ht,e.__scopeMenu),f=W(t,a),p=l.useRef(!1),d=()=>{const u=a.current;if(!n&&u){const m=new CustomEvent(lr,{bubbles:!0,cancelable:!0});u.addEventListener(lr,v=>r==null?void 0:r(v),{once:!0}),gr(u,m),m.defaultPrevented?p.current=!1:i.onClose()}};return s.jsx(Vo,{...o,ref:f,disabled:n,onClick:A(e.onClick,d),onPointerDown:u=>{var m;(m=e.onPointerDown)==null||m.call(e,u),p.current=!0},onPointerUp:A(e.onPointerUp,u=>{var m;p.current||(m=u.currentTarget)==null||m.click()}),onKeyDown:A(e.onKeyDown,u=>{const m=c.searchRef.current!=="";n||m&&u.key===" "||nn.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});jt.displayName=ht;var Vo=l.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,i=An(ht,n),c=Wo(n),f=l.useRef(null),p=W(t,f),[d,u]=l.useState(!1),[m,v]=l.useState("");return l.useEffect(()=>{const w=f.current;w&&v((w.textContent??"").trim())},[a.children]),s.jsx(Ke.ItemSlot,{scope:n,disabled:r,textValue:o??m,children:s.jsx(au,{asChild:!0,...c,focusable:!r,children:s.jsx(I.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:p,onPointerMove:A(e.onPointerMove,ze(w=>{r?i.onItemLeave(w):(i.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:A(e.onPointerLeave,ze(w=>i.onItemLeave(w))),onFocus:A(e.onFocus,()=>u(!0)),onBlur:A(e.onBlur,()=>u(!1))})})})}),Cu="MenuCheckboxItem",Yo=l.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return s.jsx(Jo,{scope:e.__scopeMenu,checked:n,children:s.jsx(jt,{role:"menuitemcheckbox","aria-checked":gt(n)?"mixed":n,...o,ref:t,"data-state":jn(n),onSelect:A(o.onSelect,()=>r==null?void 0:r(gt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Yo.displayName=Cu;var qo="MenuRadioGroup",[Eu,Su]=ye(qo,{value:void 0,onValueChange:()=>{}}),Xo=l.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=re(r);return s.jsx(Eu,{scope:e.__scopeMenu,value:n,onValueChange:a,children:s.jsx(Pn,{...o,ref:t})})});Xo.displayName=qo;var Zo="MenuRadioItem",Qo=l.forwardRef((e,t)=>{const{value:n,...r}=e,o=Su(Zo,e.__scopeMenu),a=n===o.value;return s.jsx(Jo,{scope:e.__scopeMenu,checked:a,children:s.jsx(jt,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":jn(a),onSelect:A(r.onSelect,()=>{var i;return(i=o.onValueChange)==null?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})})});Qo.displayName=Zo;var Nn="MenuItemIndicator",[Jo,Ru]=ye(Nn,{checked:!1}),ea=l.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=Ru(Nn,n);return s.jsx(le,{present:r||gt(a.checked)||a.checked===!0,children:s.jsx(I.span,{...o,ref:t,"data-state":jn(a.checked)})})});ea.displayName=Nn;var Mu="MenuSeparator",ta=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ta.displayName=Mu;var Au="MenuArrow",na=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Nt(n);return s.jsx(xo,{...o,...r,ref:t})});na.displayName=Au;var _u="MenuSub",[hf,ra]=ye(_u),We="MenuSubTrigger",oa=l.forwardRef((e,t)=>{const n=Ce(We,e.__scopeMenu),r=Ze(We,e.__scopeMenu),o=ra(We,e.__scopeMenu),a=An(We,e.__scopeMenu),i=l.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:f}=a,p={__scopeMenu:e.__scopeMenu},d=l.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return l.useEffect(()=>d,[d]),l.useEffect(()=>{const u=c.current;return()=>{window.clearTimeout(u),f(null)}},[c,f]),s.jsx(Rn,{asChild:!0,...p,children:s.jsx(Vo,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":ia(n.open),...e,ref:hr(t,o.onTriggerChange),onClick:u=>{var m;(m=e.onClick)==null||m.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:A(e.onPointerMove,ze(u=>{a.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:A(e.onPointerLeave,ze(u=>{var v,w;d();const m=(v=n.content)==null?void 0:v.getBoundingClientRect();if(m){const g=(w=n.content)==null?void 0:w.dataset.side,h=g==="right",x=h?-5:5,b=m[h?"left":"right"],y=m[h?"right":"left"];a.onPointerGraceIntentChange({area:[{x:u.clientX+x,y:u.clientY},{x:b,y:m.top},{x:y,y:m.top},{x:y,y:m.bottom},{x:b,y:m.bottom}],side:g}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(u),u.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:A(e.onKeyDown,u=>{var v;const m=a.searchRef.current!=="";e.disabled||m&&u.key===" "||cu[r.dir].includes(u.key)&&(n.onOpenChange(!0),(v=n.content)==null||v.focus(),u.preventDefault())})})})});oa.displayName=We;var aa="MenuSubContent",sa=l.forwardRef((e,t)=>{const n=Uo(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=Ce(Y,e.__scopeMenu),i=Ze(Y,e.__scopeMenu),c=ra(aa,e.__scopeMenu),f=l.useRef(null),p=W(t,f);return s.jsx(Ke.Provider,{scope:e.__scopeMenu,children:s.jsx(le,{present:r||a.open,children:s.jsx(Ke.Slot,{scope:e.__scopeMenu,children:s.jsx(_n,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:p,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{var u;i.isUsingKeyboardRef.current&&((u=f.current)==null||u.focus()),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:A(e.onFocusOutside,d=>{d.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:A(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:A(e.onKeyDown,d=>{var v;const u=d.currentTarget.contains(d.target),m=lu[i.dir].includes(d.key);u&&m&&(a.onOpenChange(!1),(v=c.trigger)==null||v.focus(),d.preventDefault())})})})})})});sa.displayName=aa;function ia(e){return e?"open":"closed"}function gt(e){return e==="indeterminate"}function jn(e){return gt(e)?"indeterminate":e?"checked":"unchecked"}function Pu(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Nu(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function ju(e,t,n){const o=t.length>1&&Array.from(t).every(p=>p===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let i=Nu(e,Math.max(a,0));o.length===1&&(i=i.filter(p=>p!==n));const f=i.find(p=>p.toLowerCase().startsWith(o.toLowerCase()));return f!==n?f:void 0}function Tu(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],f=t[i],p=c.x,d=c.y,u=f.x,m=f.y;d>r!=m>r&&n<(u-p)*(r-d)/(m-d)+p&&(o=!o)}return o}function Du(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Tu(n,t)}function ze(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Ou=Ho,Iu=Rn,ku=Go,Lu=Ko,Fu=Pn,$u=zo,Bu=jt,Wu=Yo,Hu=Xo,Uu=Qo,Gu=ea,Ku=ta,zu=na,Vu=oa,Yu=sa,Tt="DropdownMenu",[qu,gf]=me(Tt,[Bo]),G=Bo(),[Xu,ca]=qu(Tt),la=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,f=G(t),p=l.useRef(null),[d,u]=xt({prop:o,defaultProp:a??!1,onChange:i,caller:Tt});return s.jsx(Xu,{scope:t,triggerId:ve(),triggerRef:p,contentId:ve(),open:d,onOpenChange:u,onOpenToggle:l.useCallback(()=>u(m=>!m),[u]),modal:c,children:s.jsx(Ou,{...f,open:d,onOpenChange:u,dir:r,modal:c,children:n})})};la.displayName=Tt;var ua="DropdownMenuTrigger",da=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=ca(ua,n),i=G(n);return s.jsx(Iu,{asChild:!0,...i,children:s.jsx(I.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:hr(t,a.triggerRef),onPointerDown:A(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:A(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});da.displayName=ua;var Zu="DropdownMenuPortal",fa=e=>{const{__scopeDropdownMenu:t,...n}=e,r=G(t);return s.jsx(ku,{...r,...n})};fa.displayName=Zu;var pa="DropdownMenuContent",ma=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ca(pa,n),a=G(n),i=l.useRef(!1);return s.jsx(Lu,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:A(e.onCloseAutoFocus,c=>{var f;i.current||(f=o.triggerRef.current)==null||f.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:A(e.onInteractOutside,c=>{const f=c.detail.originalEvent,p=f.button===0&&f.ctrlKey===!0,d=f.button===2||p;(!o.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ma.displayName=pa;var Qu="DropdownMenuGroup",ha=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Fu,{...o,...r,ref:t})});ha.displayName=Qu;var Ju="DropdownMenuLabel",ga=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx($u,{...o,...r,ref:t})});ga.displayName=Ju;var ed="DropdownMenuItem",va=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Bu,{...o,...r,ref:t})});va.displayName=ed;var td="DropdownMenuCheckboxItem",nd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Wu,{...o,...r,ref:t})});nd.displayName=td;var rd="DropdownMenuRadioGroup",od=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Hu,{...o,...r,ref:t})});od.displayName=rd;var ad="DropdownMenuRadioItem",sd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Uu,{...o,...r,ref:t})});sd.displayName=ad;var id="DropdownMenuItemIndicator",cd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Gu,{...o,...r,ref:t})});cd.displayName=id;var ld="DropdownMenuSeparator",xa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Ku,{...o,...r,ref:t})});xa.displayName=ld;var ud="DropdownMenuArrow",dd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(zu,{...o,...r,ref:t})});dd.displayName=ud;var fd="DropdownMenuSubTrigger",pd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Vu,{...o,...r,ref:t})});pd.displayName=fd;var md="DropdownMenuSubContent",hd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Yu,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});hd.displayName=md;var gd=la,vd=da,xd=fa,wd=ma,bd=ha,yd=ga,Cd=va,Ed=xa;function Tn({...e}){return s.jsx(gd,{"data-slot":"dropdown-menu",...e})}function Dn({...e}){return s.jsx(vd,{"data-slot":"dropdown-menu-trigger",...e})}function On({className:e,sideOffset:t=4,...n}){return s.jsx(xd,{children:s.jsx(wd,{"data-slot":"dropdown-menu-content",sideOffset:t,className:N("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function Sd({...e}){return s.jsx(bd,{"data-slot":"dropdown-menu-group",...e})}function rn({className:e,inset:t,variant:n="default",...r}){return s.jsx(Cd,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:N("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function Rd({className:e,inset:t,...n}){return s.jsx(yd,{"data-slot":"dropdown-menu-label","data-inset":t,className:N("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function on({className:e,...t}){return s.jsx(Ed,{"data-slot":"dropdown-menu-separator",className:N("bg-border -mx-1 my-1 h-px",e),...t})}var Gt={exports:{}},Kt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur;function Md(){if(ur)return Kt;ur=1;var e=ja();function t(u,m){return u===m&&(u!==0||1/u===1/m)||u!==u&&m!==m}var n=typeof Object.is=="function"?Object.is:t,r=e.useState,o=e.useEffect,a=e.useLayoutEffect,i=e.useDebugValue;function c(u,m){var v=m(),w=r({inst:{value:v,getSnapshot:m}}),g=w[0].inst,h=w[1];return a(function(){g.value=v,g.getSnapshot=m,f(g)&&h({inst:g})},[u,v,m]),o(function(){return f(g)&&h({inst:g}),u(function(){f(g)&&h({inst:g})})},[u]),i(v),v}function f(u){var m=u.getSnapshot;u=u.value;try{var v=m();return!n(u,v)}catch{return!0}}function p(u,m){return m()}var d=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:c;return Kt.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:d,Kt}var dr;function Ad(){return dr||(dr=1,Gt.exports=Md()),Gt.exports}var _d=Ad();function Pd(){return _d.useSyncExternalStore(Nd,()=>!0,()=>!1)}function Nd(){return()=>{}}var In="Avatar",[jd,vf]=me(In),[Td,wa]=jd(In),ba=l.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,a]=l.useState("idle");return s.jsx(Td,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:s.jsx(I.span,{...r,ref:t})})});ba.displayName=In;var ya="AvatarImage",Ca=l.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...a}=e,i=wa(ya,n),c=Dd(r,a),f=re(p=>{o(p),i.onImageLoadingStatusChange(p)});return xe(()=>{c!=="idle"&&f(c)},[c,f]),c==="loaded"?s.jsx(I.img,{...a,ref:t,src:r}):null});Ca.displayName=ya;var Ea="AvatarFallback",Sa=l.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,a=wa(Ea,n),[i,c]=l.useState(r===void 0);return l.useEffect(()=>{if(r!==void 0){const f=window.setTimeout(()=>c(!0),r);return()=>window.clearTimeout(f)}},[r]),i&&a.imageLoadingStatus!=="loaded"?s.jsx(I.span,{...o,ref:t}):null});Sa.displayName=Ea;function fr(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Dd(e,{referrerPolicy:t,crossOrigin:n}){const r=Pd(),o=l.useRef(null),a=r?(o.current||(o.current=new window.Image),o.current):null,[i,c]=l.useState(()=>fr(a,e));return xe(()=>{c(fr(a,e))},[a,e]),xe(()=>{const f=u=>()=>{c(u)};if(!a)return;const p=f("loaded"),d=f("error");return a.addEventListener("load",p),a.addEventListener("error",d),t&&(a.referrerPolicy=t),typeof n=="string"&&(a.crossOrigin=n),()=>{a.removeEventListener("load",p),a.removeEventListener("error",d)}},[a,n,t]),i}var Od=ba,Id=Ca,kd=Sa;function Ld({className:e,...t}){return s.jsx(Od,{"data-slot":"avatar",className:N("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Fd({className:e,...t}){return s.jsx(Id,{"data-slot":"avatar-image",className:N("aspect-square size-full",e),...t})}function $d({className:e,...t}){return s.jsx(kd,{"data-slot":"avatar-fallback",className:N("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function xf({text:e,className:t}){const n=e.split(" ").map(r=>r[0]).join("").toUpperCase();return s.jsx("span",{className:N("font-bold text-lg",t),children:n})}function Bd(){return l.useCallback(t=>{const n=t.trim().split(" ");if(n.length===0)return"";if(n.length===1)return n[0].charAt(0).toUpperCase();const r=n[0].charAt(0),o=n[n.length-1].charAt(0);return`${r}${o}`.toUpperCase()},[])}function Ra({user:e,showEmail:t=!1}){const n=Bd(),r=e.first_name+" "+e.last_name;return s.jsxs(s.Fragment,{children:[s.jsxs(Ld,{className:"h-8 w-8 overflow-hidden rounded-full",children:[s.jsx(Fd,{src:e.avatar,alt:r}),s.jsx($d,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(r)})]}),s.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[s.jsx("span",{className:"truncate font-medium",children:r}),t&&s.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}function Wd(){return l.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Hd({user:e}){const t=Wd();return s.jsxs(s.Fragment,{children:[s.jsx(Rd,{className:"p-0 font-normal",children:s.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:s.jsx(Ra,{user:e,showEmail:!0})})}),s.jsx(on,{}),s.jsx(Sd,{children:s.jsx(rn,{asChild:!0,children:s.jsxs(de,{className:"block w-full",href:route("password.edit"),as:"button",prefetch:!0,onClick:t,children:[s.jsx(es,{className:"mr-2"}),"Change Password"]})})}),s.jsx(on,{}),s.jsx(rn,{asChild:!0,children:s.jsxs(de,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:t,children:[s.jsx(ns,{className:"mr-2"}),"Log out"]})})]})}function Ud(){const{auth:e}=Me().props,{state:t}=Pt(),n=xr();return s.jsx(Cn,{children:s.jsx(En,{children:s.jsxs(Tn,{children:[s.jsx(Dn,{asChild:!0,children:s.jsxs(Sn,{size:"lg",className:"text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent group",children:[s.jsx(Ra,{user:e.user}),s.jsx(Ya,{className:"ml-auto size-4"})]})}),s.jsx(On,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:n?"bottom":t==="collapsed"?"left":"bottom",children:s.jsx(Hd,{user:e.user})})]})})})}function Gd(){return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-sm",children:s.jsx(Da,{className:"size-7 rounded-sm fill-current"})}),s.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm overflow-hidden",children:s.jsx("span",{className:"mb-0.5 truncate leading-none font-semibold",children:"MOAMS"})})]})}const Kd=[{title:"Dashboard",url:"/dashboard",icon:Qa},{title:"Minibus Management",url:"/minibuses",icon:$a,role:["association clerk","minibus owner"]},{title:"Route Management",url:"/routes",icon:os,role:["association clerk","association manager"]},{title:"Membership Management",url:"/membership-management",icon:Bn,role:["association clerk"]},{title:"Fee Management",url:"/fee-settings",icon:Ba,role:["association manager"]},{title:"My Membership",url:"/my-membership",icon:Bn,role:["minibus owner"]},{title:"Driver Management",url:"/drivers",icon:cs,role:["association clerk","minibus owner"]},{title:"Misconduct Management",url:"/misconducts",icon:Wa,role:["association clerk","association manager","minibus owner"]},{title:"User Management",url:"/admin/users",icon:Ha,role:"system admin"}];function zd(){var o;const{auth:e}=Me().props,t=Me().props;Me();let n=[];t.userRoles&&Array.isArray(t.userRoles)?n=t.userRoles:(o=e==null?void 0:e.user)!=null&&o.roles&&Array.isArray(e.user.roles)&&(n=e.user.roles.map(a=>typeof a=="string"?a:a.name)),n.includes("system admin");const r=Kd.filter(a=>a.role?Array.isArray(a.role)?a.role.some(i=>n.includes(i)):n.includes(a.role):!0).map(a=>a.url==="/minibuses"&&n.includes("minibus owner")?{...a,title:"My Minibuses"}:a.url==="/drivers"&&n.includes("minibus owner")?{...a,title:"My Drivers"}:a);return s.jsxs(Il,{collapsible:"icon",variant:"inset",children:[s.jsx(Fl,{children:s.jsx(Cn,{children:s.jsx(En,{children:s.jsx(Sn,{size:"lg",asChild:!0,children:s.jsx(de,{href:"/dashboard",prefetch:!0,children:s.jsx(Gd,{})})})})})}),s.jsx("hr",{}),s.jsx(Bl,{children:s.jsx(zl,{items:r})}),s.jsx($l,{children:s.jsx(Ud,{})})]})}function Vd({...e}){return s.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Yd({className:e,...t}){return s.jsx("ol",{"data-slot":"breadcrumb-list",className:N("text-muted-foreground flex flex-nowrap items-center gap-1.5 text-sm break-words sm:gap-2.5 max-w-full overflow-hidden",e),...t})}function $e({className:e,...t}){return s.jsx("li",{"data-slot":"breadcrumb-item",className:N("inline-flex items-center gap-1.5 max-w-full",e),...t})}function zt({asChild:e,className:t,...n}){const r=e?vt:"a";return s.jsx(r,{"data-slot":"breadcrumb-link",className:N("hover:text-foreground transition-colors",t),...n})}function Vt({className:e,...t}){return s.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:N("text-foreground font-normal",e),...t})}function at({children:e,className:t,...n}){return s.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:N("[&>svg]:size-3.5",t),...n,children:e??s.jsx(za,{})})}function qd({className:e,...t}){return s.jsxs("span",{"data-slot":"breadcrumb-ellipsis",role:"presentation","aria-hidden":"true",className:N("flex size-9 items-center justify-center",e),...t,children:[s.jsx(Xa,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"More"})]})}function Xd({breadcrumbs:e}){if(e.length===0)return null;const t=e.length>2;return s.jsx(Vd,{children:s.jsx(Yd,{children:t?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"sm:hidden flex items-center",children:[s.jsx($e,{children:s.jsx(zt,{asChild:!0,children:s.jsx(de,{href:e[0].href,children:e[0].title})})}),s.jsx(at,{}),e.length>2&&s.jsxs(s.Fragment,{children:[s.jsx($e,{children:s.jsxs(Tn,{children:[s.jsx(Dn,{className:"flex items-center gap-1 hover:text-foreground",children:s.jsx(qd,{})}),s.jsx(On,{align:"start",children:e.slice(1,-1).map((n,r)=>s.jsx(rn,{asChild:!0,children:s.jsx(de,{href:n.href,className:"w-full",children:n.title})},r))})]})}),s.jsx(at,{})]}),s.jsx($e,{children:s.jsx(Vt,{children:e[e.length-1].title})})]}),s.jsx("div",{className:"hidden sm:flex sm:items-center",children:e.map((n,r)=>{const o=r===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx($e,{children:o?s.jsx(Vt,{children:n.title}):s.jsx(zt,{asChild:!0,children:s.jsx(de,{href:n.href,children:n.title})})}),!o&&s.jsx(at,{})]},r)})})]}):e.map((n,r)=>{const o=r===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx($e,{children:o?s.jsx(Vt,{children:n.title}):s.jsx(zt,{asChild:!0,children:s.jsx(de,{href:n.href,children:n.title})})}),!o&&s.jsx(at,{})]},r)})})})}const Zd=mr("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-auto",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Qd({className:e,variant:t,asChild:n=!1,...r}){const o=n?vt:"span";return s.jsx(o,{"data-slot":"badge",className:N(Zd({variant:t}),e),...r})}function Jd(){const[e,t]=l.useState([]),[n,r]=l.useState(0),[o,a]=l.useState(!1),[i,c]=l.useState(!1);l.useEffect(()=>{f();const h=setInterval(f,3e4);return()=>clearInterval(h)},[]);const f=async()=>{try{const x=await(await fetch("/api/notifications/unread")).json();t(x.notifications||[]),r(x.count||0)}catch(h){console.error("Failed to fetch notifications:",h)}},p=async h=>{var x;try{await fetch(`/api/notifications/${h}/read`,{method:"POST",headers:{"X-CSRF-TOKEN":(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content")}}),t(b=>b.filter(y=>y.id!==h)),r(b=>Math.max(0,b-1))}catch(b){console.error("Failed to mark notification as read:",b)}},d=async()=>{var h;try{c(!0),await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{"X-CSRF-TOKEN":(h=document.querySelector('meta[name="csrf-token"]'))==null?void 0:h.getAttribute("content")}}),t([]),r(0)}catch(x){console.error("Failed to mark all notifications as read:",x)}finally{c(!1)}},u=async h=>{var x;try{await fetch(`/api/notifications/${h}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content")}}),t(b=>b.filter(y=>y.id!==h)),r(b=>Math.max(0,b-1))}catch(b){console.error("Failed to delete notification:",b)}},m=h=>h.includes("Payment")?"💳":h.includes("Transfer")?"🚐":h.includes("Clearance")?"📋":h.includes("Fee")?"💰":h.includes("Membership")?"👤":"🔔",v=h=>{const x=h.data;if(x.title)return x.title;const b=h.type;return b.includes("PaymentSuccess")?"Payment Successful":b.includes("PaymentFailure")?"Payment Failed":b.includes("TransferRequest")?"Transfer Request":b.includes("ClearanceRequest")?"Clearance Request":b.includes("FeeChange")?"Fee Update":b.includes("MembershipExpiration")?"Membership Reminder":"Notification"},w=h=>{const x=h.data;return x.message?x.message:x.user_name&&x.amount?`${x.user_name} - MK ${x.amount}`:x.minibus_number_plate?`Minibus: ${x.minibus_number_plate}`:x.driver_name?`Driver: ${x.driver_name}`:"New notification received"},g=h=>{p(h.id);const x=h.data,b=h.type;b.includes("TransferRequest")&&x.transfer_request_id?Fe.visit(`/minibuses/transfer-requests/${x.transfer_request_id}`):b.includes("ClearanceRequest")&&x.clearance_request_id?Fe.visit(`/drivers/clearance-requests/${x.clearance_request_id}`):b.includes("Payment")&&x.payment_id?Fe.visit("/payments"):Fe.visit("/dashboard"),a(!1)};return s.jsxs(Tn,{open:o,onOpenChange:a,children:[s.jsx(Dn,{asChild:!0,children:s.jsxs(Be,{variant:"ghost",size:"icon",className:"relative h-9 w-9",children:[s.jsx($n,{className:"h-5 w-5"}),n>0&&s.jsx(Qd,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs",children:n>99?"99+":n})]})}),s.jsxs(On,{align:"end",className:"w-80",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 border-b",children:[s.jsx("h3",{className:"font-semibold",children:"Notifications"}),n>0&&s.jsxs(Be,{variant:"ghost",size:"sm",onClick:d,disabled:i,className:"text-xs",children:[s.jsx(ka,{className:"h-3 w-3 mr-1"}),"Mark all read"]})]}),s.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.length===0?s.jsxs("div",{className:"p-4 text-center text-gray-500",children:[s.jsx($n,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"No new notifications"})]}):e.map(h=>s.jsx("div",{className:"p-3 border-b hover:bg-gray-50 cursor-pointer group",onClick:()=>g(h),children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("span",{className:"text-lg flex-shrink-0",children:m(h.type)}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"font-medium text-sm truncate",children:v(h)}),s.jsx("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:w(h)}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:h.created_at})]}),s.jsx(Be,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 h-6 w-6 p-0",onClick:x=>{x.stopPropagation(),u(h.id)},children:s.jsx(vr,{className:"h-3 w-3"})})]})},h.id))}),e.length>0&&s.jsxs(s.Fragment,{children:[s.jsx(on,{}),s.jsx("div",{className:"p-2",children:s.jsx(Be,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{Fe.visit("/notifications"),a(!1)},children:"View all notifications"})})]})]})]})}function ef({breadcrumbs:e=[]}){return s.jsxs("header",{className:"border-sidebar-border/50 flex h-16 shrink-0 items-center gap-2 border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:[s.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[s.jsx(kl,{className:"-ml-1"}),s.jsx(Xd,{breadcrumbs:e})]}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx(Jd,{})})]})}function pr({message:e,type:t="success",duration:n=5e3,className:r="",onClose:o}){if(!e||typeof e!="string"||e.trim()==="")return null;const[a,i]=l.useState(!!e);if(l.useEffect(()=>{if(e&&(i(!0),n>0)){const f=setTimeout(()=>{i(!1),o&&o()},n);return()=>clearTimeout(f)}},[e,n,o]),!a||!e)return null;const c={success:"bg-green-200 text-green-900 border-green-400",error:"bg-red-200 text-red-900 border-red-400",info:"bg-blue-200 text-blue-900 border-blue-400",warning:"bg-yellow-200 text-yellow-900 border-yellow-400"};return s.jsx("div",{className:N("fixed top-0 left-0 w-full flex justify-center z-50 transition-all duration-300",a?"opacity-100 translate-y-0":"opacity-0 -translate-y-2 pointer-events-none",r),children:s.jsx("div",{className:N("mt-6 max-w-2xl w-full flex items-center text-center gap-3 px-5 py-3 rounded-lg border shadow-2xl font-semibold text-base text-center",c[t]||c.success),style:{minWidth:220},children:e})})}function tf({children:e,breadcrumbs:t=[]}){const{flash:n}=Me().props;return s.jsxs(Kl,{variant:"sidebar",children:[s.jsx(zd,{}),s.jsxs(Gl,{variant:"sidebar",children:[s.jsx(ef,{breadcrumbs:t}),(n==null?void 0:n.success)&&s.jsx(pr,{message:n.success,type:"success"}),(n==null?void 0:n.error)&&s.jsx(pr,{message:n.error,type:"error"}),e]})]})}const wf=({children:e,breadcrumbs:t,...n})=>s.jsx(tf,{breadcrumbs:t,...n,children:e});export{wf as A,Qd as B,za as C,_i as D,an as F,Ri as O,Si as P,Ei as R,Po as T,cs as U,tl as V,vr as X,Ya as a,Rl as b,Ml as c,Al as d,Mi as e,Pi as f,Ai as g,Mt as h,ho as i,ve as j,jo as k,go as l,bt as m,re as n,jr as o,Cr as p,sn as q,wt as r,vo as s,xo as t,To as u,Ld as v,$d as w,xf as x};
