import{r as a,j as p}from"./app-BbBkOpss.js";import{b as V,c as j}from"./utils-BB2gXWs2.js";function b(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function x(...e){return n=>{let t=!1;const o=e.map(r=>{const i=b(r,n);return!t&&typeof i=="function"&&(t=!0),i});if(t)return()=>{for(let r=0;r<o.length;r++){const i=o[r];typeof i=="function"?i():b(e[r],null)}}}}function z(...e){return a.useCallback(x(...e),e)}function E(e){const n=_(e),t=a.forwardRef((o,r)=>{const{children:i,...u}=o,l=a.Children.toArray(i),f=l.find(R);if(f){const s=f.props.children,c=l.map(d=>d===f?a.Children.count(s)>1?a.Children.only(null):a.isValidElement(s)?s.props.children:null:d);return p.jsx(n,{...u,ref:r,children:a.isValidElement(s)?a.cloneElement(s,void 0,c):null})}return p.jsx(n,{...u,ref:r,children:i})});return t.displayName=`${e}.Slot`,t}var N=E("Slot");function _(e){const n=a.forwardRef((t,o)=>{const{children:r,...i}=t;if(a.isValidElement(r)){const u=A(r),l=w(i,r.props);return r.type!==a.Fragment&&(l.ref=o?x(o,u):u),a.cloneElement(r,l)}return a.Children.count(r)>1?a.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var C=Symbol("radix.slottable");function L(e){const n=({children:t})=>p.jsx(p.Fragment,{children:t});return n.displayName=`${e}.Slottable`,n.__radixId=C,n}function R(e){return a.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===C}function w(e,n){const t={...n};for(const o in n){const r=e[o],i=n[o];/^on[A-Z]/.test(o)?r&&i?t[o]=(...l)=>{const f=i(...l);return r(...l),f}:r&&(t[o]=r):o==="style"?t[o]={...r,...i}:o==="className"&&(t[o]=[r,i].filter(Boolean).join(" "))}return{...e,...t}}function A(e){var o,r;let n=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,t=n&&"isReactWarning"in n&&n.isReactWarning;return t?e.ref:(n=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,t=n&&"isReactWarning"in n&&n.isReactWarning,t?e.props.ref:e.props.ref||e.ref)}const y=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,h=V,O=(e,n)=>t=>{var o;if((n==null?void 0:n.variants)==null)return h(e,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:r,defaultVariants:i}=n,u=Object.keys(r).map(s=>{const c=t==null?void 0:t[s],d=i==null?void 0:i[s];if(c===null)return null;const v=y(c)||y(d);return r[s][v]}),l=t&&Object.entries(t).reduce((s,c)=>{let[d,v]=c;return v===void 0||(s[d]=v),s},{}),f=n==null||(o=n.compoundVariants)===null||o===void 0?void 0:o.reduce((s,c)=>{let{class:d,className:v,...S}=c;return Object.entries(S).every(k=>{let[m,g]=k;return Array.isArray(g)?g.includes({...i,...l}[m]):{...i,...l}[m]===g})?[...s,d,v]:s},[]);return h(e,u,f,t==null?void 0:t.class,t==null?void 0:t.className)},I=O("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function B({className:e,variant:n,size:t,asChild:o=!1,...r}){const i=o?N:"button";return p.jsx(i,{"data-slot":"button",className:j(I({variant:n,size:t,className:e})),...r})}function T(e){return p.jsx("img",{...e,src:"/assets/logos/MoamLogo.png",alt:"App Logo"})}export{T as A,B,N as S,x as a,L as b,E as c,O as d,z as u};
