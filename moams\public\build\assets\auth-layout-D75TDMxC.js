import{j as e}from"./app-BbBkOpss.js";import{A as l}from"./app-logo-icon-EJK9zfCM.js";import{C as c,a as x,b as d,d as i,c as n}from"./card-CeuYtmvf.js";function m({children:s,title:a,description:r}){return e.jsx("div",{className:"bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10 relative",children:e.jsx("div",{className:"flex w-full max-w-md flex-col gap-6",children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs(c,{className:"rounded-xl",children:[e.jsxs(x,{className:"px-10 pt-2 pb-0 text-center",children:[e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(l,{className:"h-[auto] w-25 rounded-xl"})}),e.jsx(d,{className:"text-xl",children:a}),e.jsx(i,{children:r})]}),e.jsx(n,{className:"px-10 py-2",children:s})]})})})})}function f({children:s,title:a,description:r,...t}){return e.jsx(m,{title:a,description:r,...t,children:s})}export{f as A};
