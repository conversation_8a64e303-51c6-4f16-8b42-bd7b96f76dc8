import{r as p,j as e,H as _,b as t,T as A}from"./app-BbBkOpss.js";import{n as F}from"./navigation-DXenxO2h.js";import{B as l}from"./app-logo-icon-EJK9zfCM.js";import{C as j,a as u,b as f,c as v}from"./card-CeuYtmvf.js";import{A as D,C as k,B as m}from"./app-layout-DI72WroM.js";import{I as L}from"./input-DlpWRXnj.js";import{L as g}from"./label-BDoD3lfN.js";import{S as T,a as B,b as P,c as E,d as i}from"./select-B1BNOBy4.js";import{A as V}from"./arrow-left-Df03XlYH.js";import{S as N}from"./search-CaYnfO0m.js";import{F as $}from"./filter-zx04Nj3d.js";import{C as b}from"./clock-8tbQuA-h.js";import{E as z}from"./eye-BL-84HuG.js";import{C as H}from"./chevron-left-BQ99U5mw.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";function ce({clearanceRequests:a,userRole:I,filters:r}){const[n,o]=p.useState((r==null?void 0:r.search)||""),[d,x]=p.useState((r==null?void 0:r.status)||"all"),h=[{title:"Dashboard",href:"/dashboard"},{title:"Driver Management",href:"/drivers"},{title:"Clearance Requests",href:"/drivers/clearance-requests"}],w=s=>{switch(s){case"pending":return e.jsx(m,{variant:"outline",className:"text-amber-600 border-amber-600",children:"Pending"});case"approved":return e.jsx(m,{variant:"default",className:"bg-green-600",children:"Approved"});case"rejected":return e.jsx(m,{variant:"destructive",children:"Rejected"});default:return null}},y=s=>{o(s),t.get("/drivers/clearance-requests",{search:s,status:d},{preserveState:!0,preserveScroll:!0})},S=s=>{x(s),t.get("/drivers/clearance-requests",{search:n,status:s},{preserveState:!0,preserveScroll:!0})},C=()=>{o(""),x("all"),t.get("/drivers/clearance-requests",{},{preserveState:!0,preserveScroll:!0})},c=(a==null?void 0:a.data)||[];return e.jsxs(D,{breadcrumbs:h,children:[e.jsx(_,{title:"Driver Clearance Requests"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex flex-col gap-4 mb-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>F(h),children:[e.jsx(V,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View and manage driver clearance requests."})]})}),e.jsxs(j,{className:"mb-6 w-full",children:[e.jsx(u,{children:e.jsxs(f,{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(v,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(g,{htmlFor:"search",children:"Search"}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx(L,{id:"search",placeholder:"Search by driver name, owner, phone, or reason...",value:n,onChange:s=>y(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(g,{htmlFor:"status",children:"Status"}),e.jsxs(T,{value:d,onValueChange:S,children:[e.jsx(B,{children:e.jsx(P,{placeholder:"All Requests"})}),e.jsxs(E,{children:[e.jsx(i,{value:"all",children:"All Requests"}),e.jsx(i,{value:"pending",children:"Pending"}),e.jsx(i,{value:"approved",children:"Approved"}),e.jsx(i,{value:"rejected",children:"Rejected"})]})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(l,{variant:"outline",onClick:C,className:"w-full",children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(j,{className:"w-full border-0 shadow-none",children:[e.jsx(u,{className:"px-0",children:e.jsxs(f,{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5"}),"Clearance Requests (",(a==null?void 0:a.total)||c.length,")"]})}),e.jsx(v,{className:"p-0",children:c.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Driver"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Owner"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Reason"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Submitted Date"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:c.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 font-medium",children:s.driver?`${s.driver.first_name} ${s.driver.last_name}`:"N/A"}),e.jsx("td",{className:"px-4 py-3",children:s.owner?`${s.owner.first_name} ${s.owner.last_name}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 max-w-24",children:e.jsx("div",{className:"truncate",title:s.reason||"N/A",children:s.reason||"N/A"})}),e.jsx("td",{className:"px-4 py-3",children:w(s.status)}),e.jsx("td",{className:"px-4 py-3",children:s.created_at?new Date(s.created_at).toLocaleDateString():"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>t.visit(A("drivers.clearance-requests.show",s.id)),disabled:!s.driver||!s.owner,children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),s.status==="pending"?"Review":"View Details"]})})]},s.id))})]})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(b,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No clearance requests found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:n||d!=="all"?"Try adjusting your search or filter criteria.":"No clearance requests have been submitted yet."})]})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>t.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(H,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>t.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(k,{className:"h-4 w-4"})]})]})]})})]})})]})}export{ce as default};
