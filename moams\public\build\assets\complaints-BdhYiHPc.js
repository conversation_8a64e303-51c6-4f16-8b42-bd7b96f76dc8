import{a,j as e,H as t}from"./app-BbBkOpss.js";import{A as l}from"./app-layout-DI72WroM.js";import"./app-logo-icon-EJK9zfCM.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function u(){const{userRoles:i}=a().props,s=[{title:"Dashboard",href:"/dashboard"},{title:"Complaint Management",href:"/complaints"}];return e.jsxs(l,{breadcrumbs:s,children:[e.jsx(t,{title:"Complaint Management"}),e.jsx("div",{className:"p-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Complaint Management"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-400",children:"Complaint management is under development."})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-8 max-w-2xl mx-auto",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Coming Soon Features:"}),e.jsxs("ul",{className:"text-left space-y-3 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Submit and track complaints"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Complaint categorization and prioritization"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Complaint status tracking (Open, In Progress, Resolved, Closed)"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Response management and communication"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Escalation procedures"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Complaint analytics and reporting"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"SLA monitoring and alerts"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-600 mr-2",children:"•"}),"Complaint history and audit trails"]})]})]}),e.jsx("div",{className:"mt-8 text-sm text-gray-500 dark:text-gray-400",children:"This module will provide comprehensive complaint management capabilities for the MOAM system."})]})})})]})}export{u as default};
