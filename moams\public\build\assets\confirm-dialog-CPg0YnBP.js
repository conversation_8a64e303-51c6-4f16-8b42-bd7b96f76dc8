import{j as i}from"./app-BbBkOpss.js";import{D as x,a as j,b as f,c as D,d as m,e as p,f as C}from"./dialog-CQdogash.js";import{B as o}from"./app-logo-icon-EJK9zfCM.js";function y({open:n,title:r="Are you sure?",description:t="This action cannot be undone.",onConfirm:a,onCancel:s,confirmText:l="Confirm",cancelText:c="Cancel",loading:e=!1,confirmVariant:d="destructive",children:h}){return i.jsx(x,{open:n,onOpenChange:u=>{!u&&s&&s()},children:i.jsxs(j,{showCloseButton:!1,children:[i.jsxs(f,{children:[i.jsx(D,{children:r}),i.jsx(m,{children:t})]}),h,i.jsxs(p,{children:[i.jsx(C,{asChild:!0,children:i.jsx(o,{type:"button",variant:"outline",onClick:s,disabled:e,children:c})}),i.jsx(o,{type:"button",variant:d,onClick:a,disabled:e,children:e?"Processing...":l})]})]})})}export{y as C};
