import{u as c,j as s,H as u}from"./app-BbBkOpss.js";import{I as f}from"./input-error-NZKBKapl.js";import{B as w}from"./app-logo-icon-EJK9zfCM.js";import{I as h}from"./input-DlpWRXnj.js";import{L as x}from"./label-BDoD3lfN.js";import{A as j}from"./auth-layout-D75TDMxC.js";import{L as g}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./card-CeuYtmvf.js";function B(){const{data:a,setData:e,post:t,processing:o,errors:i,reset:n}=c({password:""}),d=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>n("password")})},p=r=>{const{id:m,value:l}=r.target;e(m,l)};return s.jsxs(j,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(u,{title:"Confirm password"}),s.jsx("form",{onSubmit:d,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(x,{htmlFor:"password",children:"Password"}),s.jsx(h,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:a.password,autoFocus:!0,onChange:p}),s.jsx(f,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(w,{className:"w-full bg-blue-400 hover:bg-blue-500",disabled:o,children:[o&&s.jsx(g,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{B as default};
