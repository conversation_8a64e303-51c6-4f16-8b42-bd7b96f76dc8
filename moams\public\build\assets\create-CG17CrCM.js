import{u as v,j as e,H as N,L as o}from"./app-BbBkOpss.js";import{A as b}from"./app-layout-DI72WroM.js";import{C as m,a as d,b as f,c as x}from"./card-CeuYtmvf.js";import{B as l}from"./app-logo-icon-EJK9zfCM.js";import{I as p}from"./input-DlpWRXnj.js";import{L as i}from"./label-BDoD3lfN.js";import{S as y,a as S,b as w,c as F,d as h}from"./select-B1BNOBy4.js";import{T as _}from"./textarea-DysrrCaS.js";import{I as n}from"./input-error-NZKBKapl.js";import{A as C}from"./arrow-left-Df03XlYH.js";import{D as A}from"./dollar-sign-Cgj5GuZe.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";function Q(){const{data:a,setData:r,post:u,processing:c,errors:t}=v({fee_type:"",amount:"",description:"",effective_from:""}),j=s=>{s.preventDefault(),u(route("fee-settings.store"))},g=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:"Set New Fee",href:"/fee-settings/create"}];return e.jsxs(b,{breadcrumbs:g,children:[e.jsx(N,{title:"Set New Fee"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[e.jsx(o,{href:route("fee-settings.index"),children:e.jsxs(l,{variant:"outline",size:"sm",children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"Back to Fee Management"]})}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"Set new fee amount for the association"})]}),e.jsxs(m,{children:[e.jsx(d,{children:e.jsxs(f,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Set New Fee"]})}),e.jsx(x,{children:e.jsxs("form",{onSubmit:j,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"fee_type",children:"Fee Type *"}),e.jsxs(y,{value:a.fee_type,onValueChange:s=>r("fee_type",s),children:[e.jsx(S,{className:t.fee_type?"border-red-500":"",children:e.jsx(w,{placeholder:"Select fee type"})}),e.jsxs(F,{children:[e.jsx(h,{value:"registration",children:"Registration Fee"}),e.jsx(h,{value:"affiliation",children:"Affiliation Fee"})]})]}),e.jsx(n,{message:t.fee_type,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"amount",children:"Amount (MK) *"}),e.jsx(p,{id:"amount",type:"number",step:"0.01",min:"0",value:a.amount,onChange:s=>r("amount",s.target.value),placeholder:"Enter amount in Malawi Kwacha",className:t.amount?"border-red-500":""}),e.jsx(n,{message:t.amount,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"effective_from",children:"Effective From *"}),e.jsx(p,{id:"effective_from",type:"date",value:a.effective_from,onChange:s=>r("effective_from",s.target.value),min:new Date().toISOString().split("T")[0],className:t.effective_from?"border-red-500":""}),e.jsx(n,{message:t.effective_from,className:"mt-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"This fee will become active from the selected date"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"description",children:"Description (Optional)"}),e.jsx(_,{id:"description",value:a.description,onChange:s=>r("description",s.target.value),placeholder:"Enter a description for this fee change (e.g., 'Annual fee increase due to economic changes')",rows:3,className:t.description?"border-red-500":""}),e.jsx(n,{message:t.description,className:"mt-2"})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(o,{href:route("fee-settings.index"),children:e.jsx(l,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(l,{type:"submit",disabled:c,className:"bg-blue-600 hover:bg-blue-700",children:c?"Setting Fee...":"Set Fee"})]})]})})]}),e.jsxs(m,{className:"mt-6",children:[e.jsx(d,{children:e.jsx(f,{children:"Important Information"})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Setting a new fee will automatically deactivate the previous fee from the effective date."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Fees cannot be set for past dates to maintain data integrity."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"All fee changes are logged with your user account for audit purposes."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Members will be charged the active fee amount when payments are recorded."})]})]})})]})]})})]})}export{Q as default};
