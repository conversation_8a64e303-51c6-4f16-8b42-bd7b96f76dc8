import{u as A,r as t,j as e,H as z,L as p}from"./app-BbBkOpss.js";import{C as B,a as I,b as O,d as P,c as R}from"./card-CeuYtmvf.js";import{B as h}from"./app-logo-icon-EJK9zfCM.js";import{I as c}from"./input-DlpWRXnj.js";import{L as n}from"./label-BDoD3lfN.js";import{S as j,a as b,b as v,c as f,d as _}from"./select-B1BNOBy4.js";import{A as V,b as H,c as K,d as U}from"./app-layout-DI72WroM.js";import{M as Y}from"./minibus-owner-combobox-NImMr_rD.js";import{A as g}from"./arrow-left-Df03XlYH.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./search-CaYnfO0m.js";import"./loader-circle-DTMNgGt5.js";function je({userRole:o,minibusOwners:Z}){const{data:l,setData:i,post:N,processing:u,errors:a}=A({first_name:"",last_name:"",phone_number:"",license_number:"",district:"",village_town:"",owner_id:"",license:null}),[w,C]=t.useState(null),[$,y]=t.useState([]),[q,G]=t.useState(null),[x,S]=t.useState([]),[J,D]=t.useState(null),[d,F]=t.useState(null);t.useEffect(()=>{fetch("/api/vehicle-routes").then(s=>s.json()).then(s=>y(s))},[]),t.useEffect(()=>{fetch("/api/minibuses").then(s=>s.json()).then(s=>S(s))},[]);const M=s=>{const r=s.target.files[0];if(d&&r&&r.name===d.name&&r.size===d.size&&r.lastModified===d.lastModified){alert("You have already selected this file. Please choose a different file."),s.target.value="";return}F(r),i("license",r)},L=s=>{s.preventDefault(),N(route("drivers.store"),{forceFormData:!0})},k=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"],E=[{title:"Dashboard",href:"/dashboard"},{title:o==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Add Driver",href:"/drivers/create"}],T=t.useCallback(async s=>(await(await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`)).json()).filter(m=>m.id!==null&&m.id!==void 0&&m.id!==""),[]);return e.jsxs(V,{breadcrumbs:E,children:[e.jsx(z,{title:o==="minibus owner"?"Add My Driver":"Add New Driver"}),e.jsx("div",{className:"p-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(p,{href:route("drivers.index"),children:e.jsxs(h,{variant:"outline",size:"sm",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(H,{children:[e.jsx(K,{asChild:!0,children:e.jsx("span",{children:e.jsx(g,{className:"h-4 w-4"})})}),e.jsx(U,{children:"Back to Drivers"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(g,{className:"mr-2 h-4 w-4"}),"Back to Drivers"]})]})}),e.jsx("p",{className:"text-muted-foreground",children:"Enter the driver's information below"})]}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs(B,{children:[e.jsxs(I,{children:[e.jsx(O,{children:"Driver Information"}),e.jsx(P,{children:"Fill in the driver's personal and contact details"})]}),e.jsx(R,{children:e.jsxs("form",{onSubmit:L,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"first_name",children:"First Name *"}),e.jsx(c,{id:"first_name",value:l.first_name||"",onChange:s=>i("first_name",s.target.value),placeholder:"Enter first name",className:a.first_name?"border-red-500":""}),a.first_name&&e.jsx("p",{className:"text-sm text-red-500",children:a.first_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"last_name",children:"Last Name *"}),e.jsx(c,{id:"last_name",value:l.last_name||"",onChange:s=>i("last_name",s.target.value),placeholder:"Enter last name",className:a.last_name?"border-red-500":""}),a.last_name&&e.jsx("p",{className:"text-sm text-red-500",children:a.last_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"phone_number",children:"Phone Number *"}),e.jsx(c,{id:"phone_number",value:l.phone_number||"",onChange:s=>i("phone_number",s.target.value),placeholder:"Enter phone number",className:a.phone_number?"border-red-500":""}),a.phone_number&&e.jsx("p",{className:"text-sm text-red-500",children:a.phone_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"license_number",children:"License Number *"}),e.jsx(c,{id:"license_number",value:l.license_number||"",onChange:s=>i("license_number",s.target.value),placeholder:"Enter license number",className:a.license_number?"border-red-500":""}),a.license_number&&e.jsx("p",{className:"text-sm text-red-500",children:a.license_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"license",children:"Driver's License (PDF/Image) *"}),e.jsx(c,{id:"license",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:M,className:a.license?"border-red-500":""}),a.license&&e.jsx("p",{className:"text-sm text-red-500",children:a.license})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"district",children:"District *"}),e.jsxs(j,{value:l.district||"",onValueChange:s=>i("district",s),children:[e.jsx(b,{className:a.district?"border-red-500":"",children:e.jsx(v,{placeholder:"Select district"})}),e.jsx(f,{children:k.map(s=>e.jsx(_,{value:s,children:s},s))})]}),a.district&&e.jsx("p",{className:"text-sm text-red-500",children:a.district})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"village_town",children:"Village/Town *"}),e.jsx(c,{id:"village_town",value:l.village_town||"",onChange:s=>i("village_town",s.target.value),placeholder:"Enter village or town",className:a.village_town?"border-red-500":""}),a.village_town&&e.jsx("p",{className:"text-sm text-red-500",children:a.village_town})]}),o==="association clerk"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"owner_id",children:"Minibus Owner *"}),e.jsx(Y,{value:w||null,onChange:s=>{C(s),i("owner_id",(s==null?void 0:s.id)||"")},fetchOwners:T,placeholder:"Select minibus owner..."}),a.owner_id&&e.jsx("p",{className:"text-sm text-red-500",children:a.owner_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"minibus_id",children:"Minibus *"}),e.jsxs(j,{value:l.minibus_id||"",onValueChange:s=>{i("minibus_id",s),D(x.find(r=>r.id.toString()===s))},children:[e.jsx(b,{className:a.minibus_id?"border-red-500":"",children:e.jsx(v,{placeholder:"Select minibus"})}),e.jsx(f,{children:x.map(s=>e.jsx(_,{value:s.id.toString(),children:s.number_plate},s.id))})]}),a.minibus_id&&e.jsx("p",{className:"text-sm text-red-500",children:a.minibus_id})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6",children:[e.jsx(p,{href:route("drivers.index"),children:e.jsx(h,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(h,{type:"submit",disabled:u,className:"bg-blue-600 hover:bg-blue-700",children:u?"Creating...":"Create Driver"})]})]})})]})})]})})]})}export{je as default};
