import{r as d,u as A,j as e,H as L,L as S}from"./app-BbBkOpss.js";import{n as R}from"./navigation-DXenxO2h.js";import{B as x}from"./app-logo-icon-EJK9zfCM.js";import{I as c}from"./input-DlpWRXnj.js";import{L as r}from"./label-BDoD3lfN.js";import{A as I}from"./app-layout-DI72WroM.js";import{I as o}from"./input-error-NZKBKapl.js";import{M as O}from"./minibus-owner-combobox-NImMr_rD.js";import{R as P}from"./route-combobox-BsN6IcNu.js";import{A as D}from"./arrow-left-Df03XlYH.js";import{L as B}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./search-CaYnfO0m.js";function re({minibusOwners:E=[],flash:H,userRole:p}){const[j,b]=d.useState(null),[_,g]=d.useState(null),{data:l,setData:u,post:v,processing:t,errors:i,reset:w}=A({number_plate:"",make:"",model:"",year_of_make:"",main_colour:"",proof_of_ownership:null,assigned_route:"",owner_id:"",route_id:""}),[h,y]=d.useState(null),C=s=>{const a=s.target.files[0];if(h&&a&&a.name===h.name&&a.size===h.size&&a.lastModified===h.lastModified){alert("You have already selected this file. Please choose a different file."),s.target.value="";return}y(a),u("proof_of_ownership",a)},N=Array.from({length:45},(s,a)=>new Date().getFullYear()-a),f=[{title:"Dashboard",href:"/dashboard"},{title:p==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:"Add Minibus",href:"/minibuses/create"}],n=s=>{const{id:a,value:m}=s.target;u(a,m)},k=d.useCallback(async s=>(await(await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`)).json()).filter(m=>m.id!==null&&m.id!==void 0&&m.id!==""),[]),M=d.useCallback(async s=>await(await fetch(`/api/vehicle-routes?search=${encodeURIComponent(s)}`)).json(),[]),F=s=>{s.preventDefault(),v(route("minibuses.store"),{onFinish:()=>w("number_plate","assigned_route","owner_id")})};return e.jsxs(I,{breadcrumbs:f,children:[e.jsx(L,{title:p==="minibus owner"?"Add My Minibus":"Add Minibus"}),e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-8",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(x,{variant:"outline",onClick:()=>R(f),className:"w-full sm:w-auto",children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Register a new minibus for the minibus owner"})})]})}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs("form",{className:"space-y-6",onSubmit:F,children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"number_plate",children:"Number Plate"}),e.jsx(c,{id:"number_plate",type:"text",autoFocus:!0,autoComplete:"number_plate",value:l.number_plate,onChange:n,disabled:t,placeholder:"e.g. DZ 4536"}),e.jsx(o,{message:i.number_plate,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"make",children:"Make"}),e.jsx(c,{id:"make",type:"text",value:l.make,onChange:n,disabled:t,placeholder:"e.g. Toyota"}),e.jsx(o,{message:i.make,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"model",children:"Model"}),e.jsx(c,{id:"model",type:"text",value:l.model,onChange:n,disabled:t,placeholder:"e.g. Hiace"}),e.jsx(o,{message:i.model,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"year_of_make",children:"Year of Make"}),e.jsxs("select",{id:"year_of_make",value:l.year_of_make,onChange:n,disabled:t,className:"w-full border rounded px-3 py-2",children:[e.jsx("option",{value:"",children:"Select year"}),N.map(s=>e.jsx("option",{value:s,children:s},s))]}),e.jsx(o,{message:i.year_of_make,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"main_colour",children:"Main Colour"}),e.jsx(c,{id:"main_colour",type:"text",value:l.main_colour,onChange:n,disabled:t,placeholder:"e.g. White/Blue"}),e.jsx(o,{message:i.main_colour,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"proof_of_ownership",children:"Proof of Ownership (PDF/Image)"}),e.jsx(c,{id:"proof_of_ownership",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:C,disabled:t}),e.jsx(o,{message:i.proof_of_ownership,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"route_id",children:"Assigned Route"}),e.jsx(P,{value:_,onChange:s=>{g(s),u("route_id",(s==null?void 0:s.id)||"")},fetchRoutes:M,placeholder:"Select route..."}),e.jsx(o,{message:i.route_id,className:"mt-1"})]}),p==="association clerk"&&e.jsxs("div",{children:[e.jsx(r,{htmlFor:"owner_id",children:"Owner *"}),e.jsx(O,{value:j||null,onChange:s=>{b(s),u("owner_id",(s==null?void 0:s.id)||"")},fetchOwners:k,placeholder:"Select minibus owner..."}),i.owner_id&&e.jsx("p",{className:"text-sm text-red-500",children:i.owner_id})]}),e.jsxs("div",{className:"flex space-x-4 pt-6",children:[e.jsxs(x,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",disabled:t,children:[t&&e.jsx(B,{className:"h-4 w-4 animate-spin mr-2"}),"Add minibus"]}),e.jsx(S,{href:"/minibuses",children:e.jsx(x,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]})]})}export{re as default};
