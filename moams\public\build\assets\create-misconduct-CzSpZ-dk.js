import{a as D,r as M,u as T,T as j,j as e,H as F}from"./app-BbBkOpss.js";import{n as R}from"./navigation-DXenxO2h.js";import{B as v}from"./app-logo-icon-EJK9zfCM.js";import{I as g}from"./input-DlpWRXnj.js";import{L as i}from"./label-BDoD3lfN.js";import{C as P,a as I,b as L,c as O}from"./card-CeuYtmvf.js";import{A as k}from"./app-layout-DI72WroM.js";import{S as c,a as d,b as o,c as m,d as r}from"./select-B1BNOBy4.js";import{T as V}from"./textarea-DysrrCaS.js";import{C as $}from"./chevron-left-BQ99U5mw.js";import{T as B}from"./triangle-alert-Dh6aTEch.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";function re({drivers:x=[]}){var u;const{userRoles:H}=D().props,[l,h]=M.useState(null),{data:n,setData:a,post:N,processing:p,errors:t,reset:b}=T({name:"",description:"",offense_date:"",severity:"medium",evidence_file:null,offender_type:"driver",offender_id:""}),y=["Reckless Driving","Speeding","Overloading","Route Deviation","Poor Customer Service","Vehicle Maintenance Issues","Unauthorized Stops","Inappropriate Behavior","Operating Without Valid License","Operating Outside Designated Hours","Unauthorized Fare Increase","Other"],f=[{title:"Misconduct Management",href:j("misconducts.index")},{title:"Report Misconduct",href:j("misconducts.create")}],w=s=>{h(s),a("offender_id",(s==null?void 0:s.id)||"")},S=s=>{s.preventDefault(),N("/misconducts",{forceFormData:!0,onSuccess:()=>{b(),h(null)}})};return e.jsxs(k,{breadcrumbs:f,children:[e.jsx(F,{title:"Report Driver Misconduct"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex flex-col gap-4 mb-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>R(f),children:[e.jsx($,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Report a misconduct incident for one of your drivers"})})]})}),e.jsxs(P,{className:"max-w-4xl mx-auto",children:[e.jsxs(I,{children:[e.jsxs(L,{className:"flex items-center gap-2 text-red-600",children:[e.jsx(B,{className:"h-6 w-6"}),"Report Driver Misconduct"]}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Please provide detailed information about the misconduct incident. This will affect the driver's trust score and may result in disciplinary action."})]}),e.jsx(O,{children:e.jsxs("form",{onSubmit:S,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx(i,{htmlFor:"driver-select",className:"text-base font-medium",children:"Select Driver *"}),e.jsxs(c,{value:((u=l==null?void 0:l.id)==null?void 0:u.toString())||"",onValueChange:s=>{const C=x.find(_=>_.id.toString()===s);w(C)},children:[e.jsx(d,{className:`mt-2 ${t.offender_id?"border-red-500":""}`,children:e.jsx(o,{placeholder:"Choose a driver..."})}),e.jsx(m,{children:x.map(s=>e.jsx(r,{value:s.id.toString(),children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"font-medium",children:[s.first_name," ",s.last_name]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[s.phone_number,s.minibus&&` • ${s.minibus.plate_number}`]})]})},s.id))})]}),t.offender_id&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.offender_id})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"misconduct-type",className:"text-base font-medium",children:"Misconduct Type *"}),e.jsxs(c,{value:n.name,onValueChange:s=>a("name",s),children:[e.jsx(d,{className:`mt-2 ${t.name?"border-red-500":""}`,children:e.jsx(o,{placeholder:"Select misconduct type..."})}),e.jsx(m,{children:y.map(s=>e.jsx(r,{value:s,children:s},s))})]}),t.name&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.name})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"offense-date",className:"text-base font-medium",children:"Offense Date *"}),e.jsx(g,{id:"offense-date",type:"date",value:n.offense_date,onChange:s=>a("offense_date",s.target.value),className:`mt-2 ${t.offense_date?"border-red-500":""}`,max:new Date().toISOString().split("T")[0]}),t.offense_date&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.offense_date})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"severity",className:"text-base font-medium",children:"Severity Level *"}),e.jsxs(c,{value:n.severity,onValueChange:s=>a("severity",s),children:[e.jsx(d,{className:`mt-2 ${t.severity?"border-red-500":""}`,children:e.jsx(o,{})}),e.jsxs(m,{children:[e.jsx(r,{value:"low",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:"Low"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Minor infraction"})]})}),e.jsx(r,{value:"medium",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:"Medium"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Moderate concern"})]})}),e.jsx(r,{value:"high",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:"High"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Serious violation"})]})})]})]}),t.severity&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.severity})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"evidence-file",className:"text-base font-medium",children:"Evidence File (Optional)"}),e.jsxs("div",{className:"mt-2",children:[e.jsx(g,{id:"evidence-file",type:"file",onChange:s=>a("evidence_file",s.target.files[0]),className:t.evidence_file?"border-red-500":"",accept:".pdf,.jpg,.jpeg,.png"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Accepted formats: Images (JPG, JPEG, PNG) and PDF files only (Max: 5MB)"})]}),t.evidence_file&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.evidence_file})]})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"description",className:"text-base font-medium",children:"Description"}),e.jsx(V,{id:"description",placeholder:"Provide detailed information about the misconduct incident, including what happened, when, where, and any other relevant details...",value:n.description,onChange:s=>a("description",s.target.value),className:`mt-2 min-h-[120px] ${t.description?"border-red-500":""}`,rows:5}),t.description&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.description})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 pt-6 border-t",children:[e.jsx(v,{type:"button",variant:"outline",onClick:()=>window.history.back(),className:"sm:w-auto",children:"Cancel"}),e.jsx(v,{type:"submit",disabled:p,className:"bg-red-600 hover:bg-red-700 text-white sm:w-auto",children:p?"Reporting...":"Report Misconduct"})]})]})})]})]})})]})}export{re as default};
