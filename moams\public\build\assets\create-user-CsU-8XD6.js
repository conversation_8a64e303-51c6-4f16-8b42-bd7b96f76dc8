import{u as P,a as B,r as w,j as e,H as R,L as U}from"./app-BbBkOpss.js";import{n as V}from"./navigation-DXenxO2h.js";import{I as l}from"./input-error-NZKBKapl.js";import{B as u}from"./app-logo-icon-EJK9zfCM.js";import{I as o}from"./input-DlpWRXnj.js";import{L as i}from"./label-BDoD3lfN.js";import{S as g,a as j,b,c as f,e as C,d as p}from"./select-B1BNOBy4.js";import{A as O}from"./app-layout-DI72WroM.js";import{C as z}from"./checkbox-Bvpeb2i3.js";import{A as G}from"./arrow-left-Df03XlYH.js";import{L as H}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";const y="I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all times.",K=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"];function he(){const{data:s,setData:n,post:F,processing:t,errors:r,reset:M}=P({first_name:"",last_name:"",gender:"",district:"",village:"",email:"",phone_number:"",password:"",password_confirmation:"",role:"",commitment_statement:"",user_id:"",joining_date:"",national_id:null}),{flash:q,roles:c,userRoles:v}=B().props,[S,h]=w.useState(!1),d=v&&v.includes("association clerk");if(w.useEffect(()=>{d&&n("role","minibus owner")},[d,n]),!c||!Array.isArray(c)||c.length===0)return e.jsx("div",{children:"Loading..."});const N=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"},{title:"Create User",href:"/admin/create-user"}],m=a=>{const{id:x,value:D,type:_,files:E,checked:T}=a.target;_==="checkbox"?(n(x,T),x==="commitment_statement"&&h(!1)):n(x,_==="file"?E[0]:D)},k=a=>{n("gender",a)},I=a=>{n("role",a)},L=a=>{n("commitment_statement",a?y:""),h(!1)},A=a=>{if(a.preventDefault(),s.role==="minibus owner"&&!s.commitment_statement){h(!0);return}h(!1),F(d?"/membership-management/create":"/admin/create-user",{onFinish:()=>M("password","password_confirmation","national_id"),forceFormData:!0})};return e.jsxs(O,{breadcrumbs:N,children:[e.jsx(R,{title:"Create User"}),e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-8",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(u,{variant:"outline",onClick:()=>V(N),className:"w-full sm:w-auto",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Create a new user account"})})]})}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs("form",{className:"space-y-6",onSubmit:A,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx(i,{htmlFor:"first_name",children:"First name"}),e.jsx(o,{id:"first_name",type:"text",autoFocus:!0,tabIndex:1,autoComplete:"first_name",value:s.first_name,onChange:m,disabled:t,placeholder:"First name"}),e.jsx(l,{message:r.first_name,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"last_name",children:"Last name"}),e.jsx(o,{id:"last_name",type:"text",autoFocus:!0,tabIndex:2,autoComplete:"last_name",value:s.last_name,onChange:m,disabled:t,placeholder:"Last name"}),e.jsx(l,{message:r.last_name,className:"mt-2"})]})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"gender",children:"Gender"}),e.jsxs(g,{id:"gender",type:"text",autoFocus:!0,autoComplete:"gender",value:s.gender,onValueChange:k,disabled:t,children:[e.jsx(j,{children:e.jsx(b,{placeholder:"Male/Female",className:"overflow-hidden"})}),e.jsx(f,{children:e.jsxs(C,{children:[e.jsx(p,{value:"Male",children:"Male"}),e.jsx(p,{value:"Female",children:"Female"})]})})]}),e.jsx(l,{message:r.gender,className:"mt-2"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(i,{htmlFor:"district",className:"text-gray-700",children:"District"}),e.jsxs(g,{value:s.district,onValueChange:a=>n("district",a),disabled:t,children:[e.jsx(j,{className:r.district?"border-red-500":"",children:e.jsx(b,{placeholder:"Select district"})}),e.jsx(f,{children:K.map(a=>e.jsx(p,{value:a,children:a},a))})]}),e.jsx(l,{message:r.district,className:"mt-2"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(i,{htmlFor:"village",className:"text-gray-700",children:"Village/Town"}),e.jsx(o,{id:"village",type:"text",autoComplete:"village",value:s.village,onChange:m,disabled:t,placeholder:"e.g. Muwalo",className:"placeholder:text-gray-400 text-gray-900 mt-1"}),e.jsx(l,{message:r.village,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"email",children:"Email address"}),e.jsx(o,{id:"email",type:"email",tabIndex:4,autoComplete:"email",value:s.email,onChange:m,disabled:t,placeholder:"<EMAIL>"}),e.jsx(l,{message:r.email,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"phone_number",children:"Phone number"}),e.jsx(o,{id:"phone_number",type:"tel",autoFocus:!0,tabIndex:5,autoComplete:"phone_number",value:s.phone_number,onChange:m,placeholder:"0889... or 0995... 0r +26599...."}),e.jsx(l,{message:r.phone_number,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"password",children:"Password"}),e.jsx(o,{id:"password",type:"password",tabIndex:5,autoComplete:"new-password",value:s.password,onChange:m,disabled:t,placeholder:"Password"}),e.jsx(l,{message:r.password,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"password_confirmation",children:"Confirm password"}),e.jsx(o,{id:"password_confirmation",type:"password",tabIndex:6,autoComplete:"new-password",value:s.password_confirmation,onChange:m,placeholder:"Confirm password"}),e.jsx(l,{message:r.password_confirmation,className:"mt-2"})]}),!d&&e.jsxs("div",{children:[e.jsx(i,{htmlFor:"role",children:"Role"}),e.jsxs(g,{id:"role",value:s.role,onValueChange:I,disabled:t,children:[e.jsx(j,{children:e.jsx(b,{placeholder:"Select role"})}),e.jsx(f,{children:e.jsx(C,{children:c.map(a=>e.jsx(p,{value:a,children:a},a))})})]}),e.jsx(l,{message:r.role,className:"mt-2"})]}),s.role==="minibus owner"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col my-2 space-y-2 p-4 bg-gray-50 rounded-md border border-gray-200",children:[e.jsx(i,{htmlFor:"national_id",className:"text-gray-800 font-semibold text-base",children:"National ID (Upload)"}),e.jsx(o,{id:"national_id",name:"national_id",type:"file",accept:"image/*,application/pdf",onChange:a=>n("national_id",a.target.files[0]),disabled:t}),e.jsx(l,{message:r.national_id,className:"mt-2"})]}),e.jsxs("div",{className:"flex flex-col my-2 space-y-2 p-4 bg-gray-50 rounded-md border border-gray-200",children:[e.jsx(i,{htmlFor:"joining_date",className:"text-gray-800 font-semibold text-base",children:"Joining Date"}),e.jsx(o,{id:"joining_date",name:"joining_date",type:"date",max:new Date().toISOString().split("T")[0],value:s.joining_date||"",onChange:a=>n("joining_date",a.target.value),required:s.role==="minibus owner",disabled:t}),e.jsx(l,{message:r.joining_date,className:"mt-2"})]}),e.jsxs("div",{className:"flex flex-col my-2 space-y-2 p-4 bg-gray-50 rounded-md border border-gray-200",children:[e.jsx("span",{className:"text-gray-800 font-semibold text-base",children:"Commitment Statement"}),e.jsx("span",{className:"text-gray-600 text-sm",children:y}),e.jsxs("div",{className:"flex items-center space-x-3 mt-2",children:[e.jsx(z,{id:"commitment_statement",name:"commitment_statement",checked:!!s.commitment_statement,onCheckedChange:L,disabled:t,className:"h-5 w-5"}),e.jsx(i,{htmlFor:"commitment_statement",className:"text-gray-700 text-sm",children:"I have read and agree to the above commitment statement."})]}),S&&e.jsx(l,{message:"You must agree to the commitment statement to proceed.",className:"mt-2"})]})]}),e.jsxs("div",{className:"flex space-x-4 pt-6",children:[e.jsxs(u,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",disabled:t,children:[t&&e.jsx(H,{className:"h-4 w-4 animate-spin mr-2"}),"Add user"]}),e.jsx(U,{href:d?"/membership-management":"/admin/users",children:e.jsx(u,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]})]})}export{he as default};
