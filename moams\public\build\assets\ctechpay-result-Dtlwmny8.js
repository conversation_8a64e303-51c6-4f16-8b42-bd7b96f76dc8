import{j as e,H as x,b as h}from"./app-BbBkOpss.js";import{C as u,a as f,b as y,c as p}from"./card-CeuYtmvf.js";import{B as s}from"./app-logo-icon-EJK9zfCM.js";import{A as j}from"./app-layout-DI72WroM.js";import{C as g}from"./circle-check-big-QC8tmp3b.js";import{T as b}from"./triangle-alert-Dh6aTEch.js";import{C as N}from"./clock-8tbQuA-h.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";function E({success:t,message:l,payment:r,ctechpay_status:a,ctechpay_response:w,verification_needed:n,order_reference:i,error_type:o}){const d=()=>t===!0?e.jsx(g,{className:"h-12 w-12 text-green-600 mb-2"}):t===!1?e.jsx(b,{className:"h-12 w-12 text-red-600 mb-2"}):e.jsx(N,{className:"h-12 w-12 text-yellow-600 mb-2"}),m=()=>t===!0?"Payment Successful":t===!1?"Payment Failed":"Payment Verification Needed",c=()=>t===!0?"text-green-700":t===!1?"text-red-700":"text-yellow-700";return e.jsxs(j,{children:[e.jsx(x,{title:"Payment Result"}),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12",children:e.jsxs(u,{className:"max-w-md w-full shadow-lg",children:[e.jsxs(f,{className:"flex flex-col items-center",children:[d(),e.jsx(y,{className:c(),children:m()})]}),e.jsxs(p,{className:"space-y-4",children:[e.jsx("div",{className:"text-center text-gray-700",children:l}),n&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsxs("div",{className:"text-sm text-yellow-800",children:[e.jsx("div",{className:"font-semibold mb-2",children:"What to do next:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[e.jsxs("li",{children:["Save your order reference: ",e.jsx("code",{className:"bg-yellow-100 px-1 rounded",children:i})]}),e.jsx("li",{children:"Contact support to verify your payment"}),e.jsx("li",{children:"Check your bank statement for the transaction"})]})]})}),r&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsxs("div",{children:[e.jsx("b",{children:"Amount:"})," ",r.amount]}),e.jsxs("div",{children:[e.jsx("b",{children:"Status:"})," ",r.status]}),e.jsxs("div",{children:[e.jsx("b",{children:"Order Ref:"})," ",r.order_reference||i]})]}),a&&e.jsxs("div",{className:"text-xs text-gray-400",children:["Ctechpay Status: ",a]}),o==="connection_timeout"&&e.jsx("div",{className:"text-xs text-gray-500 bg-gray-50 p-2 rounded",children:"Technical note: Connection timeout to payment gateway"}),e.jsxs("div",{className:"flex justify-center gap-3 mt-6",children:[t===!1&&e.jsx(s,{onClick:()=>{window.location.href="/my-membership?t="+Date.now()},className:"bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 font-medium",children:"Retry Payment"}),t===!0&&e.jsx(s,{onClick:()=>{window.location.href="/my-membership?t="+Date.now()},className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 font-medium",children:"Back to My Membership"}),e.jsx(s,{variant:"outline",onClick:()=>h.visit("/dashboard"),className:"border-gray-400 text-gray-600 hover:bg-gray-50 px-6 py-2 font-medium",children:"Go to Dashboard"})]})]})]})})]})}export{E as default};
