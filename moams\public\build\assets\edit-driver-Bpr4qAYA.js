import{r as o,u as D,j as e,H as E,L as b}from"./app-BbBkOpss.js";import{C as k,a as M,b as F,d as L,c as B}from"./card-CeuYtmvf.js";import{B as h}from"./app-logo-icon-EJK9zfCM.js";import{I as m}from"./input-DlpWRXnj.js";import{L as n}from"./label-BDoD3lfN.js";import{S as I,a as R,b as U,c as z,d as A}from"./select-B1BNOBy4.js";import{A as T}from"./app-layout-DI72WroM.js";import{M as $}from"./minibus-owner-combobox-NImMr_rD.js";import{A as H}from"./arrow-left-Df03XlYH.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./search-CaYnfO0m.js";import"./loader-circle-DTMNgGt5.js";function he({driver:t,userRole:p,minibusOwners:V,currentOwner:r}){var x,j;const[f,_]=o.useState(r?{...r,email:r.email||((x=t.minibus_owner)==null?void 0:x.email)||""}:null),{data:l,setData:i,put:v,processing:u,errors:a}=D({first_name:t.first_name,last_name:t.last_name,phone_number:t.phone_number,district:t.district,village_town:t.village_town,owner_id:(r==null?void 0:r.id)||((j=t.owner_id)==null?void 0:j.toString())||""}),[K,g]=o.useState([]),[P,Z]=o.useState(null);o.useEffect(()=>{fetch("/api/vehicle-routes").then(s=>s.json()).then(s=>g(s))},[]);const N=s=>{s.preventDefault(),v(route("drivers.update",t.id),{forceFormData:!0})},w=s=>{_(s),i("owner_id",(s==null?void 0:s.id)||"")},C=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"],y=[{title:"Dashboard",href:"/dashboard"},{title:p==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Edit Driver",href:`/drivers/${t.id}/edit`}],S=o.useCallback(async s=>{try{const c=await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`);if(c.ok)return(await c.json()).map(d=>({id:d.id,name:`${d.first_name} ${d.last_name}`,email:d.email}))}catch(c){console.error("Error fetching minibus owners:",c)}return[]},[]);return e.jsxs(T,{breadcrumbs:y,children:[e.jsx(E,{title:"Edit Driver"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[e.jsx(b,{href:route("drivers.show",t.id),children:e.jsxs(h,{variant:"outline",size:"sm",children:[e.jsx(H,{className:"mr-2 h-4 w-4"}),"Back to Driver"]})}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"Update the driver's information. Note: License number cannot be changed for security reasons."})]}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs(k,{children:[e.jsxs(M,{children:[e.jsx(F,{children:"Driver Information"}),e.jsx(L,{children:"Update the driver's personal and contact details"})]}),e.jsx(B,{children:e.jsxs("form",{onSubmit:N,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"first_name",children:"First Name *"}),e.jsx(m,{id:"first_name",value:l.first_name||"",onChange:s=>i("first_name",s.target.value),placeholder:"Enter first name",className:a.first_name?"border-red-500":""}),a.first_name&&e.jsx("p",{className:"text-sm text-red-500",children:a.first_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"last_name",children:"Last Name *"}),e.jsx(m,{id:"last_name",value:l.last_name||"",onChange:s=>i("last_name",s.target.value),placeholder:"Enter last name",className:a.last_name?"border-red-500":""}),a.last_name&&e.jsx("p",{className:"text-sm text-red-500",children:a.last_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"phone_number",children:"Phone Number *"}),e.jsx(m,{id:"phone_number",value:l.phone_number||"",onChange:s=>i("phone_number",s.target.value),placeholder:"Enter phone number",className:a.phone_number?"border-red-500":""}),a.phone_number&&e.jsx("p",{className:"text-sm text-red-500",children:a.phone_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"district",children:"District *"}),e.jsxs(I,{value:l.district||"",onValueChange:s=>i("district",s),children:[e.jsx(R,{className:a.district?"border-red-500":"",children:e.jsx(U,{placeholder:"Select district"})}),e.jsx(z,{children:C.map(s=>e.jsx(A,{value:s,children:s},s))})]}),a.district&&e.jsx("p",{className:"text-sm text-red-500",children:a.district})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"village_town",children:"Village/Town *"}),e.jsx(m,{id:"village_town",value:l.village_town||"",onChange:s=>i("village_town",s.target.value),placeholder:"Enter village or town",className:a.village_town?"border-red-500":""}),a.village_town&&e.jsx("p",{className:"text-sm text-red-500",children:a.village_town})]}),p==="association clerk"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"owner_id",children:"Minibus Owner *"}),e.jsx($,{value:f||null,onChange:w,fetchOwners:S,placeholder:"Select minibus owner..."}),a.owner_id&&e.jsx("p",{className:"text-sm text-red-500",children:a.owner_id})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(b,{href:route("drivers.show",t.id),children:e.jsx(h,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(h,{type:"submit",disabled:u,className:"bg-blue-600 hover:bg-blue-700",children:u?"Updating...":"Update Driver"})]})]})})]})})]})})]})}export{he as default};
