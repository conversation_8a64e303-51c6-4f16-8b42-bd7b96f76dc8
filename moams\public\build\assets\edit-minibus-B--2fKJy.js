import{j as e,r as m,u as L,L as F}from"./app-BbBkOpss.js";import{n as R}from"./navigation-DXenxO2h.js";import{A as f}from"./app-layout-DI72WroM.js";import{B as h}from"./app-logo-icon-EJK9zfCM.js";import{I as u}from"./input-DlpWRXnj.js";import{L as l}from"./label-BDoD3lfN.js";import{I as i}from"./input-error-NZKBKapl.js";import{M as E}from"./minibus-owner-combobox-NImMr_rD.js";import{R as A}from"./route-combobox-BsN6IcNu.js";import{A as b}from"./arrow-left-Df03XlYH.js";import{L as I}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./search-CaYnfO0m.js";function ee({minibus:a={},minibusOwners:_=[],userRole:p}){if(!a||!a.id)return e.jsx(f,{children:e.jsx("div",{className:"p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-red-600",children:"Error Loading Minibus"}),e.jsx("p",{className:"mt-2",children:"Unable to load minibus details. Please try again."}),e.jsxs(h,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Back"]})]})})});const j=[{title:"Dashboard",href:"/dashboard"},{title:p==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:`Edit ${a.number_plate}`,href:`/minibuses/${a.id}/edit`}],[v,g]=m.useState(_.find(s=>String(s.id)===String(a.owner_id))||null),[w,N]=m.useState(null),{data:o,setData:x,put:y,processing:r,errors:t}=L({number_plate:a.number_plate||"",make:a.make||"",model:a.model||"",year_of_make:a.year_of_make||"",main_colour:a.main_colour||"",assigned_route:a.assigned_route||"",owner_id:a.owner_id||"",route_id:a.route_id||""}),n=s=>{const{id:d,value:c}=s.target;x(d,c)},k=s=>{s.preventDefault(),y(route("minibuses.update",a.id))},C=m.useCallback(async s=>(await(await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`)).json()).filter(c=>c.id!==null&&c.id!==void 0&&c.id!==""),[]),M=m.useCallback(async s=>await(await fetch(`/api/vehicle-routes?search=${encodeURIComponent(s)}`)).json(),[]),S=Array.from({length:45},(s,d)=>new Date().getFullYear()-d);return e.jsx(f,{breadcrumbs:j,children:e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-8",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(h,{variant:"outline",onClick:()=>R(j),className:"w-full sm:w-auto",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Edit minibus details for the minibus owner"})})]})}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs("form",{className:"space-y-6",onSubmit:k,children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"number_plate",children:"Number Plate"}),e.jsx(u,{id:"number_plate",type:"text",value:o.number_plate,onChange:n,disabled:r}),e.jsx(i,{message:t.number_plate,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"make",children:"Make"}),e.jsx(u,{id:"make",type:"text",value:o.make,onChange:n,disabled:r,placeholder:"e.g. Toyota"}),e.jsx(i,{message:t.make,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"model",children:"Model"}),e.jsx(u,{id:"model",type:"text",value:o.model,onChange:n,disabled:r,placeholder:"e.g. Hiace"}),e.jsx(i,{message:t.model,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"year_of_make",children:"Year of Make"}),e.jsxs("select",{id:"year_of_make",value:o.year_of_make,onChange:n,disabled:r,className:"w-full border rounded px-3 py-2",children:[e.jsx("option",{value:"",children:"Select year"}),S.map(s=>e.jsx("option",{value:s,children:s},s))]}),e.jsx(i,{message:t.year_of_make,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"main_colour",children:"Main Colour"}),e.jsx(u,{id:"main_colour",type:"text",value:o.main_colour,onChange:n,disabled:r,placeholder:"e.g. White/Blue"}),e.jsx(i,{message:t.main_colour,className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"route_id",children:"Assigned Route"}),e.jsx(A,{value:w,onChange:s=>{N(s),x("route_id",(s==null?void 0:s.id)||"")},fetchRoutes:M,placeholder:"Select route..."}),e.jsx(i,{message:t.route_id,className:"mt-1"})]}),p==="association clerk"&&a.archived&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"owner_id",children:"Owner *"}),e.jsx(E,{value:v||null,onChange:s=>{g(s),x("owner_id",(s==null?void 0:s.id)||"")},fetchOwners:C,placeholder:"Select minibus owner..."}),e.jsx("p",{className:"mt-2 text-sm text-amber-600",children:"This minibus was previously archived. Please select a new owner to complete the unarchiving process."}),t.owner_id&&e.jsx("p",{className:"text-sm text-red-500",children:t.owner_id})]}),e.jsxs("div",{className:"flex space-x-4 pt-6",children:[e.jsxs(h,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",disabled:r,children:[r&&e.jsx(I,{className:"h-4 w-4 animate-spin mr-2"}),"Save changes"]}),e.jsx(F,{href:"/minibuses",children:e.jsx(h,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]})})}export{ee as default};
