import{r as j,u as D,j as e,H as M,L as g}from"./app-BbBkOpss.js";import{B as N}from"./app-logo-icon-EJK9zfCM.js";import{I as v}from"./input-DlpWRXnj.js";import{L as d}from"./label-BDoD3lfN.js";import{T}from"./textarea-DysrrCaS.js";import{C as I,a as L,b as F,c as E}from"./card-CeuYtmvf.js";import{S as y,a as _,b,c as S,d as p}from"./select-B1BNOBy4.js";import{A as O,X as P}from"./app-layout-DI72WroM.js";import{I as l}from"./input-error-NZKBKapl.js";import{C as k}from"./chevron-left-BQ99U5mw.js";import{T as U}from"./triangle-alert-Dh6aTEch.js";import{a as B}from"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],V=B("Save",H);function $({value:t,onChange:o,drivers:m,placeholder:f}){const[u,n]=j.useState(!1),[i,x]=j.useState(""),c=m.filter(a=>{var h;return`${a.first_name} ${a.last_name}`.toLowerCase().includes(i.toLowerCase())||((h=a.phone_number)==null?void 0:h.includes(i))}),r=m.find(a=>a.id===(t==null?void 0:t.id));return e.jsxs("div",{className:"relative",children:[e.jsx(v,{placeholder:f,value:r?`${r.first_name} ${r.last_name}`:i,onChange:a=>x(a.target.value),onFocus:()=>n(!0),onBlur:()=>setTimeout(()=>n(!1),200)}),u&&e.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:c.length===0?e.jsx("div",{className:"px-3 py-2 text-gray-500",children:"No drivers found"}):c.map(a=>e.jsxs("div",{className:"px-3 py-2 hover:bg-gray-100 cursor-pointer",onClick:()=>{o(a),x(""),n(!1)},children:[e.jsxs("div",{className:"font-medium",children:[a.first_name," ",a.last_name]}),e.jsx("div",{className:"text-sm text-gray-500",children:a.phone_number})]},a.id))})]})}function de({misconduct:t,drivers:o}){const m=s=>s?new Date(s).toISOString().split("T")[0]:"",[f,u]=j.useState(o.find(s=>s.id===t.offender_id)||null),{data:n,setData:i,post:x,processing:c,errors:r}=D({name:t.name,description:t.description||"",offense_date:m(t.offense_date),severity:t.severity||"medium",evidence_file:null,offender_type:"driver",offender_id:t.offender_id}),a=["Reckless Driving","Speeding","Overloading","Route Deviation","Poor Customer Service","Vehicle Maintenance Issues","Late Arrival","Unauthorized Stops","Inappropriate Behavior","Other"],h=s=>{s.preventDefault(),x(`/misconducts/${t.id}`,{forceFormData:!0,data:{...n,_method:"PUT"}})},C=s=>{u(s),i("offender_id",(s==null?void 0:s.id)||"")},w=[{title:"Dashboard",href:"/dashboard"},{title:"Misconduct Management",href:"/misconducts"},{title:"Edit Misconduct",href:`/misconducts/${t.id}/edit`}];return e.jsxs(O,{breadcrumbs:w,children:[e.jsx(M,{title:"Edit Driver Misconduct"}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs(g,{href:"/misconducts",className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",children:[e.jsx(k,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600",children:"Update misconduct incident details"})})]}),e.jsxs(I,{children:[e.jsx(L,{children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-5 w-5 text-indigo-600"}),"Update Misconduct Details"]})}),e.jsx(E,{children:e.jsxs("form",{onSubmit:h,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"name",children:"Misconduct Type *"}),e.jsxs(y,{value:n.name,onValueChange:s=>i("name",s),children:[e.jsx(_,{children:e.jsx(b,{placeholder:"Select misconduct type"})}),e.jsx(S,{children:a.map(s=>e.jsx(p,{value:s,children:s},s))})]}),e.jsx(l,{message:r.name,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"offender_id",children:"Select Driver *"}),e.jsx($,{value:f,onChange:C,drivers:o,placeholder:"Search and select a driver..."}),e.jsx(l,{message:r.offender_id,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"offense_date",children:"Offense Date *"}),e.jsx(v,{id:"offense_date",type:"date",value:n.offense_date,onChange:s=>i("offense_date",s.target.value),max:new Date().toISOString().split("T")[0],required:!0}),e.jsx(l,{message:r.offense_date,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"severity",children:"Severity Level *"}),e.jsxs(y,{value:n.severity,onValueChange:s=>i("severity",s),children:[e.jsx(_,{children:e.jsx(b,{placeholder:"Select severity level"})}),e.jsxs(S,{children:[e.jsx(p,{value:"low",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"}),"Low (5 points deducted)"]})}),e.jsx(p,{value:"medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-orange-500"}),"Medium (10 points deducted)"]})}),e.jsx(p,{value:"high",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500"}),"High (20 points deducted)"]})})]})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Points will be adjusted based on severity changes."}),e.jsx(l,{message:r.severity,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"description",children:"Description"}),e.jsx(T,{id:"description",value:n.description,onChange:s=>i("description",s.target.value),placeholder:"Provide detailed description of the misconduct incident...",rows:4,className:"resize-none"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Include specific details about what happened and any relevant circumstances."}),e.jsx(l,{message:r.description,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"evidence_file",children:"Evidence (Optional)"}),t.evidence_file&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["Current file: ",t.evidence_file.split("/").pop()]}),e.jsx(v,{id:"evidence_file",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:s=>i("evidence_file",s.target.files[0])}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Upload new evidence to replace existing file (PDF, JPG, JPEG, PNG - Max 5MB)"}),e.jsx(l,{message:r.evidence_file,className:"mt-2"})]}),e.jsxs("div",{className:"flex gap-4 pt-4",children:[e.jsxs(N,{type:"submit",disabled:c,className:"flex-1",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),c?"Updating...":"Update Misconduct"]}),e.jsx(g,{href:"/misconducts",className:"flex-1",children:e.jsxs(N,{type:"button",variant:"outline",className:"w-full",children:[e.jsx(P,{className:"h-4 w-4 mr-2"}),"Cancel"]})})]})]})})]})]})]})}export{de as default};
