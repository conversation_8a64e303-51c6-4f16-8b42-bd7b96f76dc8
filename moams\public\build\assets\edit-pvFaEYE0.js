import{u as h,j as e,L as o}from"./app-BbBkOpss.js";import{A as u}from"./app-layout-DI72WroM.js";import{C as j,a as g,b as v,c as N}from"./card-CeuYtmvf.js";import{B as m}from"./app-logo-icon-EJK9zfCM.js";import{I as d}from"./input-DlpWRXnj.js";import{L as r}from"./label-BDoD3lfN.js";import{T as b}from"./textarea-DysrrCaS.js";import{I as c}from"./input-error-NZKBKapl.js";import{A as y}from"./arrow-left-Df03XlYH.js";import{D as w}from"./dollar-sign-Cgj5GuZe.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function H({feeSetting:s}){const{data:i,setData:l,patch:p,processing:n,errors:t}=h({amount:s.amount||"",description:s.description||"",effective_from:s.effective_from?new Date(s.effective_from).toISOString().split("T")[0]:""}),f=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:`Edit ${s.fee_type} Fee`,href:`/fee-settings/${s.id}/edit`}],x=a=>{a.preventDefault(),p(route("fee-settings.update",s.id))};return e.jsx(u,{breadcrumbs:f,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{href:"/fee-settings",children:e.jsxs(m,{variant:"outline",className:"w-full sm:w-auto",children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Back to Fee Management"]})}),e.jsxs("span",{className:"text-muted-foreground text-base font-medium",children:["Update the ",s.fee_type," fee setting."]})]})}),e.jsx("div",{className:"w-full max-w-2xl mx-auto",children:e.jsxs(j,{children:[e.jsx(g,{children:e.jsxs(v,{className:"flex items-center gap-2",children:["Edit ",s.fee_type.charAt(0).toUpperCase()+s.fee_type.slice(1)," Fee"]})}),e.jsx(N,{children:e.jsxs("form",{onSubmit:x,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"fee_type",children:"Fee Type"}),e.jsx("div",{className:"flex items-center gap-2 p-3 border rounded-md bg-gray-50",children:e.jsx("span",{className:"font-medium capitalize",children:s.fee_type})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Fee type cannot be changed once created."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"current_amount",children:"Current Amount"}),e.jsxs("div",{className:"flex items-center gap-2 p-3 border rounded-md bg-gray-50",children:[e.jsx(w,{className:"h-4 w-4 text-green-600"}),e.jsxs("span",{className:"font-semibold",children:["$",s.amount]})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Current active amount for this fee type."})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"amount",children:"New Amount (MK) *"}),e.jsx(d,{id:"amount",type:"number",step:"0.01",min:"0",value:i.amount,onChange:a=>l("amount",a.target.value),placeholder:"Enter new amount",className:t.amount?"border-red-500":""}),e.jsx(c,{message:t.amount,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"effective_from",children:"Effective From *"}),e.jsx(d,{id:"effective_from",type:"date",value:i.effective_from,onChange:a=>l("effective_from",a.target.value),className:t.effective_from?"border-red-500":""}),e.jsx(c,{message:t.effective_from,className:"mt-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Changing this date will create a new fee setting"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"description",children:"Description (Optional)"}),e.jsx(b,{id:"description",value:i.description,onChange:a=>l("description",a.target.value),placeholder:"Enter a description for this fee change",rows:3,className:t.description?"border-red-500":""}),e.jsx(c,{message:t.description,className:"mt-2"})]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Important Note"}),e.jsx("p",{className:"text-sm text-blue-700",children:"If you change the effective date, a new fee setting will be created. The current fee will remain active until the new effective date is reached."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 pt-4",children:[e.jsx(m,{type:"submit",disabled:n,className:"flex-1 bg-blue-500 hover:bg-blue-600",children:n?"Updating...":"Update Fee Setting"}),e.jsx(o,{href:"/fee-settings",children:e.jsx(m,{variant:"outline",className:"w-full sm:w-auto",children:"Cancel"})})]})]})})]})})]})})}export{H as default};
