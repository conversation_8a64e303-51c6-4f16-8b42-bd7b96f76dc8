import{a as S,u as F,R as $,j as e,H as M,L as R}from"./app-BbBkOpss.js";import{n as U}from"./navigation-DXenxO2h.js";import{I as t}from"./input-error-NZKBKapl.js";import{B as u}from"./app-logo-icon-EJK9zfCM.js";import{I as o}from"./input-DlpWRXnj.js";import{L as n}from"./label-BDoD3lfN.js";import{S as p,a as j,b as g,c as f,e as N,d as h}from"./select-B1BNOBy4.js";import{A as D}from"./app-layout-DI72WroM.js";import{A as E}from"./arrow-left-Df03XlYH.js";import{L as V}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";const B=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"];function le(){const{user:s,roles:_,userRoles:b,flash:I}=S().props,{data:i,setData:m,patch:C,processing:l,errors:r}=F({first_name:s.first_name,last_name:s.last_name,gender:s.gender,phone_number:s.phone_number,role:s.roles.length>0?s.roles[0].name:"",district:s.district||"",village:s.village||""}),d=b&&b.includes("association clerk");$.useEffect(()=>{d&&m("role","minibus owner")},[d,m]);const v=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"},{title:`${s.first_name} ${s.last_name}`,href:`/admin/users/${s.id}`},{title:"Edit User",href:`/admin/users/${s.id}/edit`}],c=a=>{const{id:x,value:L}=a.target;m(x,L)},w=a=>{m("gender",a)},y=a=>{m("role",a)},k=a=>{a.preventDefault();const x=d?`/membership-management/${s.id}`:`/admin/users/${s.id}`;C(x)};return e.jsxs(D,{breadcrumbs:v,children:[e.jsx(M,{title:`Edit User - ${s.first_name} ${s.last_name}`}),e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-8",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(u,{variant:"outline",onClick:()=>U(v),className:"w-full sm:w-auto",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("div",{children:e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:["Update information for ",s.first_name," ",s.last_name]})})]})}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs("form",{className:"space-y-6",onSubmit:k,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"first_name",children:"First name"}),e.jsx(o,{id:"first_name",type:"text",value:i.first_name,onChange:c,disabled:l,placeholder:"First name"}),e.jsx(t,{message:r.first_name,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"last_name",children:"Last name"}),e.jsx(o,{id:"last_name",type:"text",value:i.last_name,onChange:c,disabled:l,placeholder:"Last name"}),e.jsx(t,{message:r.last_name,className:"mt-2"})]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"gender",children:"Gender"}),e.jsxs(p,{value:i.gender,onValueChange:w,disabled:l,children:[e.jsx(j,{children:e.jsx(g,{placeholder:"Select gender"})}),e.jsx(f,{children:e.jsxs(N,{children:[e.jsx(h,{value:"male",children:"Male"}),e.jsx(h,{value:"female",children:"Female"})]})})]}),e.jsx(t,{message:r.gender,className:"mt-2"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(n,{htmlFor:"district",className:"text-gray-700",children:"District"}),e.jsxs(p,{value:i.district,onValueChange:a=>m("district",a),disabled:l,children:[e.jsx(j,{className:r.district?"border-red-500":"",children:e.jsx(g,{placeholder:"Select district"})}),e.jsx(f,{children:B.map(a=>e.jsx(h,{value:a,children:a},a))})]}),e.jsx(t,{className:"mt-2",message:r.district})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(n,{htmlFor:"village",className:"text-gray-700",children:"Village/Town"}),e.jsx(o,{id:"village",className:"mt-1 block w-full",value:i.village,onChange:c,required:!0,autoComplete:"village",placeholder:"Village/Town",disabled:l}),e.jsx(t,{className:"mt-2",message:r.village})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"phone_number",children:"Phone number"}),e.jsx(o,{id:"phone_number",type:"tel",value:i.phone_number,onChange:c,disabled:l,placeholder:"0889... or 0995... or +26599...."}),e.jsx(t,{message:r.phone_number,className:"mt-2"})]}),!d&&e.jsxs("div",{children:[e.jsx(n,{htmlFor:"role",children:"Role"}),e.jsxs(p,{value:i.role,onValueChange:y,disabled:l,children:[e.jsx(j,{children:e.jsx(g,{placeholder:"Select role"})}),e.jsx(f,{children:e.jsx(N,{children:_.map(a=>e.jsx(h,{value:a,children:a},a))})})]}),e.jsx(t,{message:r.role,className:"mt-2"})]}),e.jsxs("div",{className:"flex space-x-4 pt-6",children:[e.jsxs(u,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",disabled:l,children:[l&&e.jsx(V,{className:"h-4 w-4 animate-spin mr-2"}),"Update User"]}),e.jsx(R,{href:d?"/membership-management":`/admin/users/${s.id}`,children:e.jsx(u,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]})]})}export{le as default};
