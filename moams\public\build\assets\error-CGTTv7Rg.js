import{j as e,H as a,L as o}from"./app-BbBkOpss.js";import{B as l}from"./app-logo-icon-EJK9zfCM.js";import{C as i,a as m,b as n,c}from"./card-CeuYtmvf.js";import{A as d}from"./app-layout-DI72WroM.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function y({status:t=500,title:r="Something went wrong",description:s="An unexpected error occurred. Please try again later or contact support if the problem persists."}){return e.jsxs(d,{children:[e.jsx(a,{title:r}),e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[60vh] p-8",children:e.jsxs(i,{className:"max-w-lg w-full text-center",children:[e.jsxs(m,{children:[e.jsx(n,{className:"text-4xl font-bold text-red-600 mb-2",children:t}),e.jsx("h2",{className:"text-2xl font-semibold mb-2",children:r})]}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-600 mb-6",children:s}),e.jsx(o,{href:"/dashboard",children:e.jsx(l,{className:"bg-blue-600 hover:bg-blue-700",children:"Back to Dashboard"})})]})]})})]})}export{y as default};
