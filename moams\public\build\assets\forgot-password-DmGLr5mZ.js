import{u as n,j as e,H as d,L as p}from"./app-BbBkOpss.js";import{I as u}from"./input-error-NZKBKapl.js";import{B as c}from"./app-logo-icon-EJK9zfCM.js";import{I as x}from"./input-DlpWRXnj.js";import{L as f}from"./label-BDoD3lfN.js";import{A as g}from"./auth-layout-D75TDMxC.js";import{L as h}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./card-CeuYtmvf.js";function C({status:r}){const{data:a,setData:o,post:i,processing:t,errors:m}=n({email:""}),l=s=>{s.preventDefault(),i(route("password.email"))};return e.jsxs(g,{title:"Forgot password",description:"Enter your email to receive a password reset link",children:[e.jsx(d,{title:"Forgot password"}),r&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:r}),e.jsx("div",{className:"space-y-6",children:e.jsxs("form",{onSubmit:l,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(f,{htmlFor:"email",children:"Email address"}),e.jsx(x,{id:"email",type:"email",name:"email",autoComplete:"off",value:a.email,autoFocus:!0,onChange:s=>o("email",s.target.value),placeholder:"<EMAIL>"}),e.jsx(u,{message:m.email})]}),e.jsxs("div",{className:"my-4 gap-2 flex items-center justify-between",children:[e.jsx(p,{href:route("login"),className:"h-9 w-full rounded-sm px-5 py-1.5 text-sm bg-gray-400 hover:bg-gray-500 text-center text-white",children:"Return to Log in"}),e.jsxs(c,{className:"w-full bg-blue-400 hover:bg-blue-500 cursor-pointer",disabled:t,children:[t&&e.jsx(h,{className:"h-4 w-4 animate-spin"}),"Send"]})]})]})})]})}export{C as default};
