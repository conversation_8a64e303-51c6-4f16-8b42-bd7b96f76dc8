import{j as e,L as a}from"./app-BbBkOpss.js";import{A as p}from"./app-layout-DI72WroM.js";import{C as c,a as f,b as j,c as m}from"./card-CeuYtmvf.js";import{B as r}from"./app-logo-icon-EJK9zfCM.js";import{A as g}from"./arrow-left-Df03XlYH.js";import{D as d}from"./dollar-sign-Cgj5GuZe.js";import{C as u}from"./calendar-HpFaW1Ru.js";import{U as N}from"./user-BYQKZGaY.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function k({feeType:t,history:i}){const x=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:`${t.charAt(0).toUpperCase()+t.slice(1)} Fee History`,href:`/fee-settings/history/${t}`}],o=s=>s?e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Active"}):e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"Inactive"});return e.jsx(p,{breadcrumbs:x,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a,{href:"/fee-settings",children:e.jsxs(r,{variant:"outline",className:"w-full sm:w-auto",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Back to Fee Management"]})}),e.jsxs("span",{className:"text-muted-foreground text-base font-medium",children:["View complete history of ",t," fee changes."]})]})}),e.jsx("div",{className:"w-full mx-auto",children:e.jsxs(c,{children:[e.jsx(f,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[t.charAt(0).toUpperCase()+t.slice(1)," Fee History"]})}),e.jsx(m,{children:i.length>0?e.jsx("div",{className:"space-y-4",children:i.map((s,h)=>{var l,n;return e.jsx(c,{className:"border-l-4 border-l-blue-500",children:e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(d,{className:"h-4 w-4 text-green-600"}),e.jsxs("span",{className:"text-lg font-semibold",children:["$",s.amount.toFixed(2)]}),o(s.is_active)]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:s.description}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(u,{className:"h-3 w-3"}),e.jsxs("span",{children:["Effective: ",new Date(s.effective_from).toLocaleDateString()]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(N,{className:"h-3 w-3"}),e.jsxs("span",{children:["Set by: ",(l=s.created_by_user)==null?void 0:l.first_name," ",(n=s.created_by_user)==null?void 0:n.last_name]})]}),e.jsx("div",{children:e.jsxs("span",{children:["Created: ",new Date(s.created_at).toLocaleDateString()]})})]})]}),h===0&&s.is_active&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx(a,{href:route("fee-settings.edit",s.id),children:e.jsx(r,{variant:"outline",size:"sm",children:"Edit"})})})]})})},s.id)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(d,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Fee History"}),e.jsxs("p",{className:"text-gray-500 mb-4",children:["No ",t," fee changes have been recorded yet."]}),e.jsx(a,{href:route("fee-settings.create"),children:e.jsx(r,{className:"bg-blue-600 hover:bg-blue-700 text-white",children:"Set First Fee"})})]})})]})})]})})}export{k as default};
