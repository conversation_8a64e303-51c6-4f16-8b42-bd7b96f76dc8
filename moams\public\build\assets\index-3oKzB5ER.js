import{r as w,j as e,H as S,L as i}from"./app-BbBkOpss.js";import{A as D,B as m}from"./app-layout-DI72WroM.js";import{C as o,a as h,b as g,c as j}from"./card-CeuYtmvf.js";import{B as a}from"./app-logo-icon-EJK9zfCM.js";import{P as N}from"./plus-BToMwexr.js";import{D as r}from"./dollar-sign-Cgj5GuZe.js";import{C as u}from"./calendar-HpFaW1Ru.js";import{S as f}from"./square-pen-s3881TYi.js";import{H as p}from"./history-CZ1KXo3f.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function T({currentFees:t,allActiveFees:c,feeHistory:l}){const[b,v]=w.useState(!1),y=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"}],d=s=>{if(!s)return e.jsx(m,{variant:"secondary",children:"No Fee Set"});const n=new Date,x=new Date(s.effective_from);return s.is_active?x>n?e.jsx(m,{variant:"outline",children:"Future"}):e.jsx(m,{className:"bg-green-100 text-green-800 border border-green-600",children:"Active"}):e.jsx(m,{variant:"secondary",children:"Inactive"})};return e.jsxs(D,{breadcrumbs:y,children:[e.jsx(S,{title:"Fee Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Fee Management"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Manage registration and affiliation fees for the association"})]}),e.jsx(i,{href:route("fee-settings.create"),children:e.jsxs(a,{className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Set New Fee"]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(o,{children:[e.jsx(h,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(r,{className:"h-5 w-5 text-blue-600"}),"Registration Fee"]})}),e.jsx(j,{children:t.registration?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-2xl font-bold text-green-600",children:["MK ",parseFloat(t.registration.amount).toLocaleString()]}),d(t.registration)]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(u,{className:"h-4 w-4"}),"Effective from: ",new Date(t.registration.effective_from).toLocaleDateString()]}),t.registration.description&&e.jsx("p",{className:"mt-2",children:t.registration.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(i,{href:route("fee-settings.edit",t.registration.id),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Edit"]})}),e.jsx(i,{href:route("fee-settings.history","registration"),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200",children:[e.jsx(p,{className:"h-3 w-3 mr-1"}),"History"]})})]})]}):e.jsxs("div",{className:"text-center py-6",children:[e.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"No registration fee set"}),e.jsx(i,{href:route("fee-settings.create"),children:e.jsxs(a,{variant:"outline",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Set Registration Fee"]})})]})})]}),e.jsxs(o,{children:[e.jsx(h,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(r,{className:"h-5 w-5 text-green-600"}),"Affiliation Fee"]})}),e.jsx(j,{children:t.affiliation?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-2xl font-bold text-green-600",children:["MK ",parseFloat(t.affiliation.amount).toLocaleString()]}),d(t.affiliation)]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(u,{className:"h-4 w-4"}),"Effective from: ",new Date(t.affiliation.effective_from).toLocaleDateString()]}),t.affiliation.description&&e.jsx("p",{className:"mt-2",children:t.affiliation.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(i,{href:route("fee-settings.edit",t.affiliation.id),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Edit"]})}),e.jsx(i,{href:route("fee-settings.history","affiliation"),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200",children:[e.jsx(p,{className:"h-3 w-3 mr-1"}),"History"]})})]})]}):e.jsxs("div",{className:"text-center py-6",children:[e.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"No affiliation fee set"}),e.jsx(i,{href:route("fee-settings.create"),children:e.jsxs(a,{variant:"outline",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Set Affiliation Fee"]})})]})})]})]}),e.jsxs(o,{children:[e.jsx(h,{children:e.jsx(g,{children:"All Active Fee Settings"})}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-lg mb-3 text-blue-600",children:"Registration Fees"}),e.jsx("div",{className:"space-y-3",children:c.registration.length>0?c.registration.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-gray-50",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx(r,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-lg",children:["MK ",parseFloat(s.amount).toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Effective from: ",new Date(s.effective_from).toLocaleDateString()]}),s.description&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:s.description})]})]}),e.jsxs("div",{className:"text-right",children:[d(s),e.jsx("div",{className:"mt-2",children:e.jsx(i,{href:route("fee-settings.edit",s.id),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Edit"]})})})]})]},s.id)):e.jsxs("div",{className:"text-center py-6",children:[e.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No registration fees set"})]})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-lg mb-3 text-green-600",children:"Affiliation Fees"}),e.jsx("div",{className:"space-y-3",children:c.affiliation.length>0?c.affiliation.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-gray-50",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(r,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-lg",children:["MK ",parseFloat(s.amount).toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Effective from: ",new Date(s.effective_from).toLocaleDateString()]}),s.description&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:s.description})]})]}),e.jsxs("div",{className:"text-right",children:[d(s),e.jsx("div",{className:"mt-2",children:e.jsx(i,{href:route("fee-settings.edit",s.id),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Edit"]})})})]})]},s.id)):e.jsxs("div",{className:"text-center py-6",children:[e.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No affiliation fees set"})]})})]})]})})]}),e.jsxs(o,{children:[e.jsx(h,{children:e.jsx(g,{children:"Recent Fee Changes"})}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-4",children:[[...l.registration,...l.affiliation].sort((s,n)=>new Date(n.created_at)-new Date(s.created_at)).slice(0,b?void 0:5).map(s=>{var n,x;return e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx(r,{className:"h-5 w-5 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium capitalize",children:[s.fee_type," Fee"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["MK ",parseFloat(s.amount).toLocaleString()," • Effective ",new Date(s.effective_from).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm text-gray-600",children:new Date(s.created_at).toLocaleDateString()}),e.jsxs("p",{className:"text-xs text-gray-500",children:["by ",(n=s.created_by)==null?void 0:n.first_name," ",(x=s.created_by)==null?void 0:x.last_name]})]})]},s.id)}),(l.registration.length>0||l.affiliation.length>0)&&[...l.registration,...l.affiliation].length>5&&e.jsx("div",{className:"flex justify-center pt-2",children:e.jsx(a,{variant:"outline",size:"sm",onClick:()=>v(s=>!s),children:b?"Show Less":"Show More"})}),l.registration.length===0&&l.affiliation.length===0&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(p,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No fee changes recorded yet"})]})]})})]})]})})]})}export{T as default};
