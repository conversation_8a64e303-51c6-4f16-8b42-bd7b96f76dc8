import{c as p,g as u,r as f,j as l}from"./app-BbBkOpss.js";import{c as v}from"./app-logo-icon-EJK9zfCM.js";var e=p();const h=u(e);var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],D=d.reduce((r,t)=>{const s=v(`Primitive.${t}`),i=f.forwardRef((o,a)=>{const{asChild:c,...m}=o,n=c?s:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(n,{...m,ref:a})});return i.displayName=`Primitive.${t}`,{...r,[t]:i}},{});function w(r,t){r&&e.flushSync(()=>r.dispatchEvent(t))}export{D as P,h as R,w as d,e as r};
