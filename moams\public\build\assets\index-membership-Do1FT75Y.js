import{a as _,r as p,j as e,L as n,b as l}from"./app-BbBkOpss.js";import{A as k,C as F}from"./app-layout-DI72WroM.js";import{C as f,a as u,b as j,c as g}from"./card-CeuYtmvf.js";import{B as t}from"./app-logo-icon-EJK9zfCM.js";import{I as M}from"./input-DlpWRXnj.js";import{L as N}from"./label-BDoD3lfN.js";import{C as b}from"./chevron-left-BQ99U5mw.js";import{P as A}from"./plus-BToMwexr.js";import{S as v}from"./search-CaYnfO0m.js";import{F as L}from"./filter-zx04Nj3d.js";import{U as y}from"./user-BYQKZGaY.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function Y({minibusOwners:r}){const{userRoles:d,flash:P}=_().props,o=d&&d.includes("association clerk"),[i,x]=p.useState(""),[m,h]=p.useState("all"),c=(r==null?void 0:r.data)||r||[],a=r!=null&&r.data?r:null,w=s=>{x(s),l.get("/membership-management",{search:s,status:m},{preserveState:!0,preserveScroll:!0})},C=s=>{h(s),l.get("/membership-management",{search:i,status:s},{preserveState:!0,preserveScroll:!0})},S=[{title:"Membership Management",href:"/membership-management"}];return e.jsx(k,{breadcrumbs:S,children:e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>window.history.back(),children:[e.jsx(b,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Browse all minibus owners and view their membership records."})})]}),e.jsx("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:o&&e.jsx(n,{href:"/membership-management/create",children:e.jsxs(t,{className:"w-fit flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2",children:[e.jsx(A,{className:"h-4 w-4"}),"Add New Member"]})})})]}),e.jsxs(f,{className:"mb-6 w-full",children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(g,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(N,{htmlFor:"search",children:"Search Members"}),e.jsxs("div",{className:"relative",children:[e.jsx(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(M,{id:"search",placeholder:"Search by name, email...",value:i,onChange:s=>w(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(N,{htmlFor:"status-filter",className:"text-sm mb-1 block",children:"Status"}),e.jsxs("select",{id:"status-filter",value:m,onChange:s=>C(s.target.value),className:"w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white",children:[e.jsx("option",{value:"all",children:"All Members"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"archived",children:"Archived"})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(t,{variant:"outline",onClick:()=>{x(""),h("all"),l.get("/membership-management")},className:"w-full",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(f,{className:"w-full border-0 shadow-none",children:[e.jsx(u,{className:"px-0",children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Minibus Owners (",(a==null?void 0:a.total)||c.length,")"]})}),e.jsx(g,{className:"p-0",children:c.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No minibus owners found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:i||m!=="all"?"Try adjusting your search or filter criteria.":"No minibus owners have been registered yet."})]}):e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Name"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Email"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Phone"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Joined"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:c.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 font-medium",children:[s.first_name," ",s.last_name]}),e.jsx("td",{className:"px-4 py-3",children:s.email}),e.jsx("td",{className:"px-4 py-3",children:s.phone_number}),e.jsx("td",{className:"px-4 py-3",children:s.joining_date?new Date(s.joining_date).toLocaleDateString():"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{href:`/membership-management/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",className:"border-blue-500 text-blue-700 hover:bg-blue-50 hover:border-blue-700",children:"View Memberships"})}),o&&e.jsx(n,{href:`/membership-management/${s.id}/edit`,children:e.jsx(t,{variant:"secondary",size:"sm",className:"bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border border-indigo-300",children:"Edit Member"})})]})})]},s.id))})]})})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>l.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(b,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>l.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(F,{className:"h-4 w-4"})]})]})]})})]})})})}export{Y as default};
