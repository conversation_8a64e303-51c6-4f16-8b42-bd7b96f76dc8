import{r as m,j as e,b as r,T as d,L as b}from"./app-BbBkOpss.js";import{n as L}from"./navigation-DXenxO2h.js";import{B as i}from"./app-logo-icon-EJK9zfCM.js";import{I as B}from"./input-DlpWRXnj.js";import{L as v}from"./label-BDoD3lfN.js";import{C as w,a as y,b as C,c as S}from"./card-CeuYtmvf.js";import{A as E,B as h,T as V,b as z,c as I,d as H,C as W}from"./app-layout-DI72WroM.js";import{C as $}from"./confirm-dialog-CPg0YnBP.js";import{C as T}from"./chevron-left-BQ99U5mw.js";import{P as G}from"./plus-BToMwexr.js";import{S as M}from"./search-CaYnfO0m.js";import{F as J}from"./filter-zx04Nj3d.js";import{B as _}from"./bus-Bw3CllrX.js";import{E as K}from"./eye-BL-84HuG.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./dialog-CQdogash.js";function pe({minibuses:t,transferRequests:n,userRole:l,auth:O}){const[o,p]=m.useState(""),[u,f]=m.useState("all"),[c,j]=m.useState({open:!1,minibus:null}),[k,g]=m.useState(!1),x=(t==null?void 0:t.data)||t||[],a=t!=null&&t.data?t:null,N=[{title:"Dashboard",href:"/dashboard"},{title:l==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"}],A=s=>{p(s),r.get("/minibuses",{search:s,status:u},{preserveState:!0,preserveScroll:!0})},F=s=>{f(s),r.get("/minibuses",{search:o,status:s},{preserveState:!0,preserveScroll:!0})},P=()=>j({open:!1,minibus:null}),D=async()=>{g(!0),await r.delete(d("minibuses.destroy",c.minibus.id)),g(!1),j({open:!1,minibus:null})};return e.jsxs(E,{breadcrumbs:N,children:[e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>L(N),children:[e.jsx(T,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:l==="minibus owner"?"View and manage your registered minibuses.":"View and manage all registered minibuses."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[l==="association clerk"&&e.jsxs(i,{variant:"outline",onClick:()=>r.visit(d("minibuses.transfer-requests.index")),className:"w-fit flex items-center gap-2",children:[e.jsx("span",{className:"hidden sm:inline",children:"Pending Transfer Requests"}),e.jsx("span",{className:"sm:hidden",children:"Pending"}),(n==null?void 0:n.filter(s=>s.status==="pending").length)>0&&e.jsx(h,{variant:"secondary",className:"ml-2 bg-red-100 text-red-800 font-bold",children:n.filter(s=>s.status==="pending").length})]}),(l==="association clerk"||l==="minibus owner")&&e.jsx(b,{href:d("minibuses.create"),children:e.jsxs(i,{className:"w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2",children:[e.jsx(G,{className:"h-4 w-4"}),"Add New Minibus"]})})]})]}),e.jsxs(w,{className:"mb-6 w-full",children:[e.jsx(y,{children:e.jsxs(C,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(S,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{htmlFor:"search",children:"Search Minibuses"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(B,{id:"search",placeholder:"Search by plate, route...",value:o,onChange:s=>A(s.target.value),className:"pl-10"})]})]}),l==="association clerk"&&e.jsxs("div",{children:[e.jsx(v,{htmlFor:"status-filter",className:"text-sm mb-1 block",children:"Status"}),e.jsxs("select",{id:"status-filter",value:u,onChange:s=>F(s.target.value),className:"w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white",children:[e.jsx("option",{value:"all",children:"All Minibuses"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"archived",children:"Archived"})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(i,{variant:"outline",onClick:()=>{p(""),f("all"),r.get("/minibuses")},className:"w-full",children:[e.jsx(J,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(w,{className:"w-full border-0 shadow-none",children:[e.jsx(y,{className:"px-0",children:e.jsxs(C,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),l==="minibus owner"?"My Minibuses":"Minibuses"," (",(a==null?void 0:a.total)||x.length,")"]})}),e.jsx(S,{className:"p-0",children:x.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsx(V,{children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Number Plate"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Make"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Model"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Main Colour"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:x.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50 text-sm",children:[e.jsx("td",{className:"font-medium px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[100px] sm:max-w-none",children:s.number_plate})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[80px] sm:max-w-none",children:s.make||"-"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[80px] sm:max-w-none",children:s.model||"-"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[80px] sm:max-w-none",children:s.main_colour||"-"})}),e.jsx("td",{className:"px-4 py-3",children:s.archived?e.jsx(h,{variant:"destructive",className:"text-sm",children:"Archived"}):e.jsx(h,{className:"bg-green-100 text-green-800 text-sm",children:"Active"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex items-center gap-1 sm:gap-2",children:e.jsxs(z,{children:[e.jsx(I,{asChild:!0,children:e.jsx(b,{href:d("minibuses.show",s.id),children:e.jsx(i,{variant:"outline",size:"sm",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",children:e.jsx(K,{className:"h-3 w-3 sm:h-4 sm:w-4"})})})}),e.jsx(H,{children:"View Details"})]})})})]},s.id))})]})})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(_,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No minibuses found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:o||filterTransferType?"Try adjusting your search or filters":"No minibuses have been registered yet"})]})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>r.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(T,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>r.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(W,{className:"h-4 w-4"})]})]})]})})]})}),e.jsx($,{open:c.open,title:c.minibus?`Delete minibus ${c.minibus.number_plate}?`:"Delete minibus?",description:"This action cannot be undone and will permanently remove the minibus.",confirmText:"Delete",confirmVariant:"destructive",loading:k,onCancel:P,onConfirm:D})]})}export{pe as default};
