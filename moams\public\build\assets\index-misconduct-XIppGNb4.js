import{a as de,r as x,j as e,H as me,b as f,L as H,T as w}from"./app-BbBkOpss.js";import{n as xe}from"./navigation-DXenxO2h.js";import{B as n}from"./app-logo-icon-EJK9zfCM.js";import{I as he}from"./input-DlpWRXnj.js";import{L as N}from"./label-BDoD3lfN.js";import{C as I,a as O,b as z,c as q}from"./card-CeuYtmvf.js";import{T as ue,a as fe,b as W,c as o,d as je,e as c}from"./table-DIpduh_T.js";import{A as ge,T as pe,B as b,b as X,c as G,d as J,C as ve}from"./app-layout-DI72WroM.js";import{S as K,a as Q,b as Y,c as Z,d,C as ee}from"./select-B1BNOBy4.js";import{C as se}from"./chevron-left-BQ99U5mw.js";import{D as we}from"./download-u6EDakOu.js";import{C as Ne}from"./chart-column-BRNjmg3d.js";import{T as p}from"./triangle-alert-Dh6aTEch.js";import{S as re}from"./search-CaYnfO0m.js";import{F as be}from"./filter-zx04Nj3d.js";import{U as ye}from"./user-BYQKZGaY.js";import{E as Ce}from"./eye-BL-84HuG.js";import{C as Se}from"./circle-x-DBFLH8az.js";import{C as _e}from"./circle-check-big-QC8tmp3b.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";function Ze({misconducts:a}){const{userRoles:i}=de().props,[j,y]=x.useState(""),[h,C]=x.useState("all"),[u,S]=x.useState("all"),[m,ae]=x.useState("offense_date"),[g,_]=x.useState("desc"),[T,k]=x.useState(!1),D=i&&i.includes("association clerk"),L=i&&i.includes("association manager"),M=i&&i.includes("system admin"),v=i&&i.includes("minibus owner"),R=v,te=M||D||L,F=a.data.filter(s=>{var A,V,P,U,$;const r=j.toLowerCase(),t=s.name.toLowerCase().includes(r)||((A=s.description)==null?void 0:A.toLowerCase().includes(r))||((P=(V=s.offender)==null?void 0:V.first_name)==null?void 0:P.toLowerCase().includes(r))||(($=(U=s.offender)==null?void 0:U.last_name)==null?void 0:$.toLowerCase().includes(r)),l=h==="all"||s.severity===h,ce=u==="all"||s.resolution_status===u;return t&&l&&ce}).sort((s,r)=>{let t=s[m],l=r[m];return m==="offense_date"&&(t=new Date(t),l=new Date(l)),g==="asc"?t>l?1:-1:t<l?1:-1}),B=s=>{m===s?_(g==="asc"?"desc":"asc"):(ae(s),_("asc"))},le=async s=>{const r=s.resolution_status==="resolved"?"unresolved":"resolved";try{await f.patch(`/misconducts/${s.id}/resolution`,{resolution_status:r,resolution_notes:r==="resolved"?"Marked as resolved via quick action":null})}catch(t){console.error("Error updating resolution status:",t)}},ne=s=>{const r={low:"bg-green-100 text-green-800",medium:"bg-orange-100 text-orange-800",high:"bg-red-100 text-red-800"};return e.jsx(b,{className:r[s]||"bg-gray-100 text-gray-800",children:s==null?void 0:s.toUpperCase()})},ie=s=>e.jsx(b,{className:s==="resolved"?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:s==="resolved"?"RESOLVED":"UNRESOLVED"}),E=[{title:"Dashboard",href:"/dashboard"},{title:"Misconduct Management",href:"/misconducts"}],oe=async()=>{try{k(!0);const s=document.createElement("a"),r=new URLSearchParams({view:"monthly",month:new Date().toISOString().slice(0,7)});s.href=`/misconduct-analytics/report?${r.toString()}`,s.download=`misconduct-report-${new Date().toISOString().slice(0,7)}.pdf`,document.body.appendChild(s),s.click(),document.body.removeChild(s)}catch(s){console.error("Error downloading PDF:",s)}finally{k(!1)}};return e.jsxs(ge,{breadcrumbs:E,children:[e.jsx(me,{title:"Misconduct Management"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>xe(E),children:[e.jsx(se,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:v?"Track and manage misconduct reports for your drivers.":"Track and manage driver misconducts."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[(M||D||L)&&e.jsxs(e.Fragment,{children:[e.jsxs(n,{variant:"outline",onClick:oe,disabled:T,className:"w-fit flex items-center gap-2 px-4 py-2",children:[e.jsx(we,{className:"h-4 w-4"}),T?"Downloading...":"Download Report"]}),e.jsxs(n,{variant:"outline",onClick:()=>f.visit("/misconduct-analytics"),className:"w-fit flex items-center gap-2 px-4 py-2",children:[e.jsx(Ne,{className:"h-4 w-4"}),"Analytics"]})]}),R&&e.jsx(H,{href:w("misconducts.create"),children:e.jsxs(n,{variant:"outline",className:"w-fit flex items-center gap-2 border-red-600 text-red-600 hover:bg-red-50 px-4 py-2",children:[e.jsx(p,{className:"h-4 w-4"}),"Report Misconduct"]})})]})]}),e.jsxs(I,{className:"mb-6 w-full",children:[e.jsx(O,{children:e.jsxs(z,{className:"flex items-center gap-2",children:[e.jsx(re,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(q,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx(N,{htmlFor:"search",children:"Search Misconducts"}),e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(he,{id:"search",placeholder:"Search misconducts...",value:j,onChange:s=>y(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(N,{htmlFor:"severity",children:"Severity"}),e.jsxs(K,{value:h,onValueChange:C,children:[e.jsx(Q,{children:e.jsx(Y,{placeholder:"All Severities"})}),e.jsxs(Z,{children:[e.jsx(d,{value:"all",children:"All Severities"}),e.jsx(d,{value:"low",children:"Low"}),e.jsx(d,{value:"medium",children:"Medium"}),e.jsx(d,{value:"high",children:"High"})]})]})]}),e.jsxs("div",{children:[e.jsx(N,{htmlFor:"resolution",children:"Resolution Status"}),e.jsxs(K,{value:u,onValueChange:S,children:[e.jsx(Q,{children:e.jsx(Y,{placeholder:"All Statuses"})}),e.jsxs(Z,{children:[e.jsx(d,{value:"all",children:"All Statuses"}),e.jsx(d,{value:"unresolved",children:"Unresolved"}),e.jsx(d,{value:"resolved",children:"Resolved"})]})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(n,{variant:"outline",onClick:()=>{y(""),C("all"),S("all")},className:"w-full",children:[e.jsx(be,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(I,{className:"w-full border-0",children:[e.jsx(O,{children:e.jsxs(z,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),v?`My Drivers' Misconducts (${a.total})`:`Driver Misconducts (${a.total})`]})}),e.jsx(q,{className:"p-0",children:F.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(p,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No misconducts found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:j||h!=="all"||u!=="all"?"Try adjusting your search or filter criteria.":"No driver misconducts have been reported yet."}),R&&!j&&h==="all"&&u==="all"&&e.jsx(H,{href:w("misconducts.create"),children:e.jsxs(n,{variant:"outline",className:"border-red-600 text-red-600 hover:bg-red-50",children:[e.jsx(p,{className:"h-4 w-4 mr-2"}),"Report First Misconduct"]})})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsx(pe,{children:e.jsxs(ue,{className:"w-full min-w-[800px]",children:[e.jsx(fe,{children:e.jsxs(W,{children:[e.jsx(o,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>B("name"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Misconduct Type",m==="name"&&e.jsx(ee,{className:`h-4 w-4 transition-transform ${g==="desc"?"rotate-180":""}`})]})}),e.jsx(o,{children:"Driver"}),e.jsx(o,{children:"Trust Score"}),e.jsx(o,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>B("offense_date"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Date",m==="offense_date"&&e.jsx(ee,{className:`h-4 w-4 transition-transform ${g==="desc"?"rotate-180":""}`})]})}),e.jsx(o,{children:"Severity"}),e.jsx(o,{children:"Status"}),e.jsx(o,{children:"Actions"})]})}),e.jsx(je,{children:F.map(s=>{var r,t,l;return e.jsxs(W,{children:[e.jsx(c,{className:"font-medium",children:s.name}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ye,{className:"h-4 w-4 text-gray-500"}),(r=s.offender)==null?void 0:r.first_name," ",(t=s.offender)==null?void 0:t.last_name]})}),e.jsx(c,{children:((l=s.offender)==null?void 0:l.trust_score)!==void 0&&e.jsxs(b,{className:s.offender.trust_score>=80?"bg-green-100 text-green-800":s.offender.trust_score>=60?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",children:[s.offender.trust_score,"%"]})}),e.jsx(c,{children:new Date(s.offense_date).toLocaleDateString()}),e.jsx(c,{children:ne(s.severity)}),e.jsx(c,{children:ie(s.resolution_status)}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(X,{children:[e.jsx(G,{asChild:!0,children:e.jsx(n,{variant:"outline",size:"sm",onClick:()=>f.visit(w("misconducts.show",s.id)),children:e.jsx(Ce,{className:"h-4 w-4"})})}),e.jsx(J,{children:e.jsx("p",{children:"View Details"})})]}),te&&e.jsxs(X,{children:[e.jsx(G,{asChild:!0,children:e.jsx(n,{variant:s.resolution_status==="resolved"?"outline":"default",size:"sm",onClick:()=>le(s),className:s.resolution_status==="resolved"?"text-orange-700 border-orange-300 hover:bg-orange-50":"bg-green-600 text-white hover:bg-green-700",children:s.resolution_status==="resolved"?e.jsx(Se,{className:"h-4 w-4"}):e.jsx(_e,{className:"h-4 w-4"})})}),e.jsx(J,{children:e.jsx("p",{children:s.resolution_status==="resolved"?"Mark as Unresolved":"Mark as Resolved"})})]})]})})]},s.id)})})]})})})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>f.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(se,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>f.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(ve,{className:"h-4 w-4"})]})]})]})})]})})]})}export{Ze as default};
