import{r as l,j as e,H as $,b as o,T as D}from"./app-BbBkOpss.js";import{n as xe}from"./navigation-DXenxO2h.js";import{B as n}from"./app-logo-icon-EJK9zfCM.js";import{I as B}from"./input-DlpWRXnj.js";import{L as j}from"./label-BDoD3lfN.js";import{C as H,a as O,b as V,c as z}from"./card-CeuYtmvf.js";import{T as he,a as je,b as q,c as y,d as pe,e as w}from"./table-DIpduh_T.js";import{A as U,B as ge,T as W,b as Z,c as G,d as J,C as fe}from"./app-layout-DI72WroM.js";import{S as R,a as F,b as A,c as M,d as r,C as P}from"./select-B1BNOBy4.js";import{D as K,a as Q,b as X,c as Y,d as I,e as ee}from"./dialog-CQdogash.js";import{C as se}from"./chevron-left-BQ99U5mw.js";import{P as ve}from"./plus-BToMwexr.js";import{F as be}from"./file-text-XwZwBs9T.js";import{F as Ne}from"./filter-zx04Nj3d.js";import{S as ye}from"./search-CaYnfO0m.js";import{a as L}from"./utils-BB2gXWs2.js";import{T as we}from"./triangle-alert-Dh6aTEch.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Se=L("PowerOff",Ce);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]],_e=L("Power",ke);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]],te=L("Route",Te);function Ye({routes:i=[],auth:C={}}){const[p,ae]=l.useState(""),[g,re]=l.useState("all"),[d,ne]=l.useState("name"),[m,le]=l.useState("asc"),[ie,f]=l.useState(!1),[u,v]=l.useState({open:!1,route:null,action:null}),[x,b]=l.useState(!1),[h,S]=l.useState({name:"",status:"active"}),[k,E]=l.useState({}),_=(i==null?void 0:i.data)||i||[],t=i!=null&&i.data?i:null,T=[{title:"Dashboard",href:"/dashboard"},{title:"Route Management",href:"/routes"},{title:"Lagos-Ibadan Route",href:"/routes/1"},{title:"Edit Route Details",href:"/routes/1/edit"},{title:"Current Page",href:"/routes"}],ce=s=>{ae(s),o.get("/routes",{search:s,status:g,sort:d,direction:m},{preserveState:!0,preserveScroll:!0})},oe=s=>{re(s),o.get("/routes",{search:p,status:s,sort:d,direction:m},{preserveState:!0,preserveScroll:!0})},N=s=>{const a=d===s&&m==="asc"?"desc":"asc";ne(s),le(a),o.get("/routes",{search:p,status:g,sort:s,direction:a},{preserveState:!0,preserveScroll:!0})},de=s=>{const c=(s.status==="active"?"inactive":"active")==="active"?"activate":"deactivate";v({open:!0,route:s,action:c,title:`${c.charAt(0).toUpperCase()+c.slice(1)} Route`,description:c==="deactivate"&&s.minibuses_count>0?`Warning: This route is assigned to ${s.minibuses_count} active minibus(es). Deactivating it may affect operations.`:`Are you sure you want to ${c} this route?`})},me=async()=>{b(!0);const{route:s,action:a}=u;try{switch(a){case"activate":await o.post(D("routes.activate",s.id));break;case"deactivate":await o.post(D("routes.deactivate",s.id));break}v({open:!1,route:null,action:null})}catch(c){console.error(`Error ${a}ing route:`,c)}finally{b(!1)}},ue=async()=>{b(!0),E({});try{await o.post(D("routes.store"),h,{onError:s=>{E(s)},onSuccess:()=>{f(!1),S({name:"",status:"active"})}})}catch(s){console.error("Error creating route:",s)}finally{b(!1)}};try{return e.jsxs(U,{breadcrumbs:T,children:[e.jsx($,{title:"Route Management"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>xe(T),children:[e.jsx(se,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Manage minibus routes and monitor usage patterns."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[C.user&&C.user.roles&&C.user.roles.includes("association clerk")&&e.jsxs(n,{onClick:()=>f(!0),className:"w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2",children:[e.jsx(ve,{className:"h-4 w-4"}),"Add New Route"]}),e.jsxs(n,{variant:"outline",onClick:()=>{window.open("/routes/export?format=pdf","_blank")},className:"w-fit border-green-500 text-green-700 hover:bg-green-50 px-4 py-2",children:[e.jsx(be,{className:"h-4 w-4 mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Export Routes Analysis PDF"}),e.jsx("span",{className:"sm:hidden",children:"Export PDF"})]})]})]}),e.jsxs(H,{className:"mb-6 w-full",children:[e.jsx(O,{children:e.jsxs(V,{className:"flex items-center gap-2",children:[e.jsx(Ne,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(z,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(j,{htmlFor:"search",children:"Search Routes"}),e.jsxs("div",{className:"relative",children:[e.jsx(ye,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(B,{id:"search",placeholder:"Search by route name...",value:p,onChange:s=>ce(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"status-filter",children:"Filter by Status"}),e.jsxs(R,{value:g,onValueChange:oe,children:[e.jsx(F,{children:e.jsx(A,{placeholder:"All statuses"})}),e.jsxs(M,{children:[e.jsx(r,{value:"all",children:"All Statuses"}),e.jsx(r,{value:"active",children:"Active"}),e.jsx(r,{value:"inactive",children:"Inactive"})]})]})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"sort-by",children:"Sort By"}),e.jsxs(R,{value:`${d}-${m}`,onValueChange:s=>{const[a,c]=s.split("-");N(a)},children:[e.jsx(F,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(r,{value:"name-asc",children:"Name (A-Z)"}),e.jsx(r,{value:"name-desc",children:"Name (Z-A)"}),e.jsx(r,{value:"minibuses_count-desc",children:"Most Popular"}),e.jsx(r,{value:"minibuses_count-asc",children:"Least Popular"}),e.jsx(r,{value:"status-asc",children:"Status (Active First)"}),e.jsx(r,{value:"status-desc",children:"Status (Inactive First)"})]})]})]})]})})]}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(H,{className:"w-full border-0",children:[e.jsx(O,{children:e.jsxs(V,{className:"flex items-center gap-2",children:[e.jsx(te,{className:"h-5 w-5"}),"Routes (",(t==null?void 0:t.total)||_.length,")"]})}),e.jsx(z,{className:"p-0",children:_.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(te,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No routes found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:p||g!=="all"?"Try adjusting your search or filter criteria.":"No routes available."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(he,{className:"w-full min-w-[600px]",children:[e.jsx(je,{children:e.jsxs(q,{children:[e.jsx(y,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("name"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Route Name",d==="name"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(y,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("status"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Status",d==="status"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(y,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("minibuses_count"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Usage Count",d==="minibuses_count"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(y,{children:"Actions"})]})}),e.jsx(pe,{children:_.map(s=>e.jsxs(q,{children:[e.jsx(w,{className:"font-medium",children:s.name}),e.jsx(w,{children:e.jsx(ge,{variant:s.status==="active"?"default":"secondary",className:s.status==="active"?"bg-green-100 text-green-800":"",children:s.status==="active"?"Active":"Inactive"})}),e.jsx(w,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:s.minibuses_count||0}),e.jsxs("span",{className:"text-muted-foreground text-sm",children:["minibus",(s.minibuses_count||0)!==1?"es":""]}),(s.minibuses_count||0)>0&&e.jsx(W,{children:e.jsxs(Z,{children:[e.jsx(G,{children:e.jsx(we,{className:"h-4 w-4 text-amber-500"})}),e.jsx(J,{children:"This route is assigned to active minibuses"})]})})]})}),e.jsx(w,{children:e.jsx(W,{children:e.jsxs(Z,{children:[e.jsx(G,{asChild:!0,children:e.jsx(n,{variant:"outline",size:"sm",onClick:()=>de(s),style:s.status==="active"?{borderColor:"#ea580c",color:"#ea580c",backgroundColor:"transparent"}:{borderColor:"#16a34a",color:"#16a34a",backgroundColor:"transparent"},onMouseEnter:a=>{s.status==="active"?a.target.style.backgroundColor="#fff7ed":a.target.style.backgroundColor="#f0fdf4"},onMouseLeave:a=>{a.target.style.backgroundColor="transparent"},children:s.status==="active"?e.jsx(Se,{className:"h-4 w-4"}):e.jsx(_e,{className:"h-4 w-4"})})}),e.jsxs(J,{children:[s.status==="active"?"Deactivate":"Activate"," route"]})]})})})]},s.id))})]})})})]})}),t&&t.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",t.from," to ",t.to," of ",t.total," results"]}),t.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>o.get(t.prev_page_url),disabled:!t.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(se,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:["Page ",t.current_page," of ",t.last_page]}),e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>o.get(t.next_page_url),disabled:!t.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(fe,{className:"h-4 w-4"})]})]})]})}),e.jsx(K,{open:ie,onOpenChange:f,children:e.jsxs(Q,{children:[e.jsxs(X,{children:[e.jsx(Y,{children:"Add New Route"}),e.jsx(I,{children:'Create a new minibus route. Route names must follow the pattern "Location - Location" (e.g., "Ntcheu - Chingeni").'})]}),e.jsxs("form",{onSubmit:s=>{s.preventDefault(),ue()},children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(j,{htmlFor:"route-name",children:"Route Name"}),e.jsx(B,{id:"route-name",placeholder:"e.g., Ntcheu - Chingeni",value:h.name,onChange:s=>S({...h,name:s.target.value}),className:k.name?"border-red-500":""}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:'Format: "Location - Location" with exactly one space before and after the dash'}),k.name&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:k.name})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"route-status",children:"Status"}),e.jsxs(R,{value:h.status,onValueChange:s=>S({...h,status:s}),children:[e.jsx(F,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(r,{value:"active",children:"Active"}),e.jsx(r,{value:"inactive",children:"Inactive"})]})]})]})]}),e.jsxs(ee,{className:"mt-6",children:[e.jsx(n,{type:"button",variant:"outline",onClick:()=>f(!1),children:"Cancel"}),e.jsx(n,{type:"submit",disabled:x,className:"bg-blue-600 hover:bg-blue-700 text-white",children:x?"Creating...":"Create Route"})]})]})]})}),e.jsx(K,{open:u.open,onOpenChange:s=>{s||v({open:!1,route:null,action:null})},children:e.jsxs(Q,{showCloseButton:!1,children:[e.jsxs(X,{children:[e.jsx(Y,{children:u.title}),e.jsx(I,{children:u.description})]}),e.jsxs(ee,{children:[e.jsx(n,{type:"button",variant:"outline",onClick:()=>v({open:!1,route:null,action:null}),disabled:x,children:"Cancel"}),e.jsx(n,{type:"button",onClick:me,disabled:x,children:x?"Loading...":u.action==="activate"?"Activate":"Deactivate"})]})]})})]})})]})}catch(s){return console.error("RouteManagement render error:",s),e.jsxs(U,{breadcrumbs:T,children:[e.jsx($,{title:"Route Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Error Loading Routes"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Error: ",s.message]}),e.jsx(n,{onClick:()=>window.location.reload(),children:"Reload Page"})]})})]})}}export{Ye as default};
