import{r as i,a as J,j as e,H as j,b as c,L as F}from"./app-BbBkOpss.js";import{B as x}from"./app-logo-icon-EJK9zfCM.js";import{I as K}from"./input-DlpWRXnj.js";import{L as g}from"./label-BDoD3lfN.js";import{S as L,a as M,b as k,c as E,e as P,d as n}from"./select-B1BNOBy4.js";import{C as f,a as R,b as $,c as v}from"./card-CeuYtmvf.js";import{A as N,T as Q,B as u,b as X,c as Y,d as Z}from"./app-layout-DI72WroM.js";import{C as ee}from"./confirm-dialog-CPg0YnBP.js";import{P as se}from"./pagination-Dav6jFJz.js";import{P as ae}from"./plus-BToMwexr.js";import{S as re}from"./search-CaYnfO0m.js";import{F as te}from"./filter-zx04Nj3d.js";import{E as y}from"./eye-BL-84HuG.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./index-CHr_Yg1t.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./dialog-CQdogash.js";import"./chevron-left-BQ99U5mw.js";function ke(){var A,_,U,T;const[o,b]=i.useState(""),[d,w]=i.useState("all"),[m,S]=i.useState("active"),[a,p]=i.useState({open:!1,action:null,user:null}),[D,C]=i.useState(!1);try{const{users:r,statistics:le}=J().props,h=(r==null?void 0:r.data)||r||[],t=r!=null&&r.data?r:null;if(!r)return e.jsxs(N,{children:[e.jsx(j,{title:"User Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"text-center",children:"Loading..."})})]});const ne=(s,l)=>{p({open:!0,action:s,user:l})},B=()=>p({open:!1,action:null,user:null}),V=async()=>{if(!a.user)return;C(!0);const{id:s,first_name:l,last_name:ce,archived_at:oe}=a.user;(a.action==="archive"||a.action==="unarchive")&&await c.put(`/admin/users/${s}/${a.action}`),C(!1),p({open:!1,action:null,user:null})},I=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"}],H=s=>{b(s),c.get("/admin/users",{search:s,role:d,status:m},{preserveState:!0,preserveScroll:!0})},z=s=>{w(s),c.get("/admin/users",{search:o,role:s,status:m},{preserveState:!0,preserveScroll:!0})},G=s=>{S(s),c.get("/admin/users",{search:o,role:d,status:s},{preserveState:!0,preserveScroll:!0})},O=s=>{switch(s){case"system admin":return"bg-red-100 text-red-800 border-red-200";case"minibus owner":return"bg-green-100 text-green-800 border-green-200";case"association clerk":return"bg-purple-100 text-purple-800 border-purple-200";case"association manager":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},W=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),q=h.filter(s=>s.roles&&s.roles.some(l=>l.name==="system admin")&&!s.archived_at),ie=s=>s.roles&&s.roles.some(l=>l.name==="system admin")&&q.length===1;return e.jsxs(N,{breadcrumbs:I,children:[e.jsx(j,{title:"User Management"}),e.jsxs("div",{className:"w-full max-w-full overflow-hidden",children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("div",{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage all system users, their roles, and permissions"})}),e.jsx(F,{href:"/admin/create-user",children:e.jsxs(x,{className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Create User"]})})]}),e.jsxs(f,{className:"mb-6",children:[e.jsx(R,{children:e.jsx($,{className:"text-lg",children:"Filters & Search"})}),e.jsx(v,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx(g,{htmlFor:"search",children:"Search Users"}),e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),e.jsx(K,{id:"search",placeholder:"Search by name, email, or phone...",value:o,onChange:s=>H(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(g,{htmlFor:"role-filter",children:"Filter by Role"}),e.jsxs(L,{value:d,onValueChange:z,children:[e.jsx(M,{children:e.jsx(k,{placeholder:"All roles"})}),e.jsx(E,{children:e.jsxs(P,{children:[e.jsx(n,{value:"all",children:"All Roles"}),e.jsx(n,{value:"system admin",children:"System Admin"}),e.jsx(n,{value:"minibus owner",children:"Minibus Owner"}),e.jsx(n,{value:"association clerk",children:"Association Clerk"}),e.jsx(n,{value:"association manager",children:"Association Manager"})]})})]})]}),e.jsxs("div",{children:[e.jsx(g,{htmlFor:"status-filter",children:"Account Status"}),e.jsxs(L,{value:m,onValueChange:G,children:[e.jsx(M,{children:e.jsx(k,{placeholder:"Active"})}),e.jsx(E,{children:e.jsxs(P,{children:[e.jsx(n,{value:"active",children:"Active"}),e.jsx(n,{value:"inactive",children:"Inactive"}),e.jsx(n,{value:"all",children:"All"})]})})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(x,{variant:"outline",onClick:()=>{b(""),w("all"),S("active"),c.get("/admin/users")},className:"w-full",children:[e.jsx(te,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(f,{className:"w-full border-0 shadow-none",children:[e.jsx(R,{className:"px-0",children:e.jsxs($,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Users (",(t==null?void 0:t.total)||h.length,")"]})}),e.jsx(v,{className:"p-0",children:h.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsx(Q,{children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Name"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Email"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Phone"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Gender"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Roles"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Created"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:h.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3",children:[s.first_name||"Unknown"," ",s.last_name||"User"]}),e.jsx("td",{className:"px-4 py-3",children:s.email}),e.jsx("td",{className:"px-4 py-3",children:s.phone_number||"N/A"}),e.jsx("td",{className:"px-4 py-3 capitalize",children:s.gender||"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex flex-wrap gap-1",children:s.roles&&Array.isArray(s.roles)?s.roles.map(l=>e.jsx(u,{variant:"outline",className:`text-xs ${O(l.name)}`,children:l.name},l.id)):e.jsx(u,{variant:"outline",className:"text-xs",children:"No roles assigned"})})}),e.jsx("td",{className:"px-4 py-3",children:s.archived_at?e.jsx(u,{variant:"destructive",children:"Archived"}):e.jsx(u,{className:"bg-green-100 text-green-800",children:"Active"})}),e.jsx("td",{className:"px-4 py-3",children:s.created_at?W(s.created_at):"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(X,{children:[e.jsx(Y,{asChild:!0,children:e.jsx(F,{href:`/admin/users/${s.id}`,children:e.jsx(x,{variant:"outline",size:"sm",children:e.jsx(y,{className:"h-4 w-4"})})})}),e.jsx(Z,{children:e.jsx("p",{children:"View user details"})})]})})})]},s.id))})]})})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No users found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:o||d!=="all"||m!=="all"?"Try adjusting your search or filters":"No users have been registered yet"})]})})]})}),t&&t.total>0&&t.total>t.per_page&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",t.from," to ",t.to," of ",t.total," results"]}),t.last_page>1&&e.jsx(se,{data:t})]})})]}),e.jsx(ee,{open:a.open,title:a.action==="archive"?`Archive ${(A=a.user)==null?void 0:A.first_name} ${(_=a.user)==null?void 0:_.last_name}?`:a.action==="unarchive"?`Unarchive ${(U=a.user)==null?void 0:U.first_name} ${(T=a.user)==null?void 0:T.last_name}?`:"Are you sure?",description:a.action==="archive"?"This user will be archived and will not be able to access the system.":a.action==="unarchive"?"This user will be restored and regain access to the system.":"",confirmText:a.action==="archive"?"Archive":a.action==="unarchive"?"Unarchive":"Confirm",confirmVariant:a.action==="archive"?"destructive":"default",loading:D,onCancel:B,onConfirm:V})]})]})}catch(r){return console.error("Error in UserManagement component:",r),e.jsxs(N,{children:[e.jsx(j,{title:"User Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(f,{children:e.jsxs(v,{className:"p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-red-600 mb-2",children:"Error Loading Users"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"There was an error loading the user management page."}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Error: ",r.message]}),e.jsx(x,{onClick:()=>window.location.reload(),className:"mt-4",children:"Reload Page"})]})})})]})}}export{ke as default};
