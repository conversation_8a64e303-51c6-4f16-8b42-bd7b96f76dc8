import{j as s,b as r}from"./app-BbBkOpss.js";import{B as t}from"./app-logo-icon-EJK9zfCM.js";import{C as i}from"./chevron-left-BQ99U5mw.js";import{C as l}from"./app-layout-DI72WroM.js";function x({data:e}){return!e||e.total===0||e.last_page<=1?null:s.jsxs("div",{className:"flex items-center justify-between px-6 py-4 border-t",children:[s.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",e.from," to ",e.to," of ",e.total," results"]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs(t,{variant:"outline",size:"sm",onClick:()=>r.get(e.prev_page_url),disabled:!e.prev_page_url,className:"flex items-center gap-1",children:[s.jsx(i,{className:"h-4 w-4"}),"Previous"]}),s.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",e.current_page," of ",e.last_page]}),s.jsxs(t,{variant:"outline",size:"sm",onClick:()=>r.get(e.next_page_url),disabled:!e.next_page_url,className:"flex items-center gap-1",children:["Next",s.jsx(l,{className:"h-4 w-4"})]})]})]})}export{x as P};
