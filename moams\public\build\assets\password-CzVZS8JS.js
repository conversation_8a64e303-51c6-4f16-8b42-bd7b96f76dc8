import{r as w,u as j,j as s,H as v}from"./app-BbBkOpss.js";import{I as n}from"./input-error-NZKBKapl.js";import{A as N}from"./app-layout-DI72WroM.js";import{B as b}from"./app-logo-icon-EJK9zfCM.js";import{H as C}from"./heading-small-S_lGqMxt.js";import{I as l}from"./input-DlpWRXnj.js";import{L as c}from"./label-BDoD3lfN.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";const _=[{title:"Change Password",href:"/settings/password"}];function q(){const i=w.useRef(null),m=w.useRef(null),{data:a,setData:f,errors:o,put:h,reset:t,processing:x}=j({current_password:"",password:"",password_confirmation:""}),d=p=>{const{id:r,value:e}=p.target;f(r,e)},g=p=>{p.preventDefault(),h(route("password.update"),{preserveScroll:!0,onSuccess:()=>t(),onError:r=>{var e,u;r.password&&(t("password","password_confirmation"),(e=i.current)==null||e.focus()),r.current_password&&(t("current_password"),(u=m.current)==null||u.focus())}})};return s.jsxs(N,{breadcrumbs:_,children:[s.jsx(v,{title:"Change Password"}),s.jsxs("div",{className:"mt-10 pl-4 md:pl-8",children:[s.jsxs("div",{className:"text-left",children:[s.jsx(C,{title:"Change Password"}),s.jsx("p",{className:"text-gray-600 text-sm mb-6",children:"Update your password regularly to keep your account secure. Use a strong, unique password that you do not use elsewhere."})]}),s.jsx("div",{className:"border p-2 md:p-10 space-y-6 rounded-md max-w-xl",children:s.jsxs("form",{onSubmit:g,className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(c,{htmlFor:"current_password",children:"Current password"}),s.jsx(l,{id:"current_password",ref:m,value:a.current_password,onChange:d,type:"password",className:"mt-1 block w-full",autoComplete:"current-password",placeholder:"Current password"}),s.jsx(n,{message:o.current_password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(c,{htmlFor:"password",children:"New password"}),s.jsx(l,{id:"password",ref:i,value:a.password,onChange:d,type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"New password"}),s.jsx(n,{message:o.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(c,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(l,{id:"password_confirmation",value:a.password_confirmation,onChange:d,type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"Confirm password"}),s.jsx(n,{message:o.password_confirmation})]}),s.jsx("div",{className:"flex items-center gap-4",children:s.jsx(b,{disabled:x,className:"bg-[darkslateblue] hover:bg-[darkblue]",children:"Save password"})})]})})]})]})}export{q as default};
