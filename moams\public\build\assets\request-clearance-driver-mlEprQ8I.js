import{u as S,R as _,r as f,j as e,H as k,L as p,b as j}from"./app-BbBkOpss.js";import{C as a,a as c,b as d,c as o,d as L}from"./card-CeuYtmvf.js";import{B as t}from"./app-logo-icon-EJK9zfCM.js";import{L as I}from"./label-BDoD3lfN.js";import{T as A}from"./textarea-DysrrCaS.js";import{A as F,b as P,c as B,d as E}from"./app-layout-DI72WroM.js";import{C as H}from"./confirm-dialog-CPg0YnBP.js";import{A as g}from"./arrow-left-Df03XlYH.js";import{T as O}from"./triangle-alert-Dh6aTEch.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";import"./dialog-CQdogash.js";function te({driver:s,userRole:N,pendingRequest:m}){const{data:u,setData:v,post:b,processing:x,errors:n}=S({reason:""});_.useEffect(()=>{console.log("Component mounted"),console.log("Driver object:",s),console.log("Window.route function:",typeof window.route);try{const r=route("drivers.store-clearance-request",s.id);console.log("Route test successful:",r)}catch(r){console.error("Route test failed:",r)}},[]);const[C,l]=f.useState(!1),[w,h]=f.useState(!1),y=r=>{r.preventDefault(),console.log("Form submission started"),console.log("Driver ID:",s.id),console.log("Form data:",u),console.log("Route URL:",route("drivers.store-clearance-request",s.id)),b(route("drivers.store-clearance-request",s.id),{onStart:()=>{console.log("Inertia request started")},onSuccess:i=>{console.log("Inertia request successful:",i)},onError:i=>{console.log("Inertia request errors:",i)},onFinish:()=>{console.log("Inertia request finished")}})},D=()=>{l(!0)},R=()=>l(!1),q=async()=>{h(!0),await j.delete(route("drivers.clearance-requests.destroy",m.id),{onSuccess:()=>j.visit(route("drivers.show",s.id))}),h(!1),l(!1)},T=[{title:"Dashboard",href:"/dashboard"},{title:N==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Request Clearance",href:"#"}];return e.jsxs(F,{breadcrumbs:T,children:[e.jsx(k,{title:`Request Clearance - ${s.first_name} ${s.last_name}`}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx(p,{href:route("drivers.show",s.id),children:e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(P,{children:[e.jsx(B,{asChild:!0,children:e.jsx("span",{children:e.jsx(g,{className:"h-4 w-4"})})}),e.jsx(E,{children:"Back to Driver"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(g,{className:"mr-2 h-4 w-4"}),"Back to Driver"]})]})}),e.jsxs("p",{className:"text-muted-foreground",children:["Submit a clearance request for ",s.first_name," ",s.last_name]}),e.jsx(t,{onClick:()=>{console.log("Test button clicked"),console.log("Route function type:",typeof window.route);try{const r=route("drivers.store-clearance-request",s.id);console.log("Generated URL:",r),alert("Route test successful: "+r)}catch(r){console.error("Route error:",r),alert("Route error: "+r.message)}},variant:"outline",className:"mb-4",children:"Test Route Function"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(a,{className:"mb-8",children:[e.jsx(c,{children:e.jsx(d,{children:"Driver Information"})}),e.jsx(o,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Name"}),e.jsxs("p",{className:"text-sm",children:[s.first_name," ",s.last_name]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Phone Number"}),e.jsx("p",{className:"text-sm",children:s.phone_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"District"}),e.jsx("p",{className:"text-sm",children:s.district})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Village/Town"}),e.jsx("p",{className:"text-sm",children:s.village_town})]})]})})]})}),e.jsxs("div",{children:[e.jsxs(a,{className:"mb-8",children:[e.jsxs(c,{children:[e.jsx(d,{children:"Clearance Request"}),e.jsx(L,{children:"Provide a reason for requesting driver clearance"})]}),e.jsx(o,{children:e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(I,{htmlFor:"reason",children:"Reason for Clearance *"}),e.jsx(A,{id:"reason",value:u.reason,onChange:r=>v("reason",r.target.value),placeholder:"Please provide a detailed reason for requesting driver clearance...",rows:6,className:n.reason?"border-red-500":""}),n.reason&&e.jsx("p",{className:"text-sm text-red-500",children:n.reason})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(p,{href:route("drivers.show",s.id),children:e.jsx(t,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(t,{type:"submit",disabled:x,children:x?"Submitting...":"Submit Request"})]})]})})]}),m&&e.jsxs(a,{className:"mb-8",children:[e.jsx(c,{children:e.jsx(d,{children:"Quick Actions"})}),e.jsx(o,{children:e.jsx(t,{type:"button",variant:"destructive",onClick:D,className:"w-full",children:"Delete Pending Clearance Request"})})]}),e.jsx(a,{className:"mt-4 border-yellow-200 bg-yellow-50",children:e.jsx(o,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(O,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-yellow-800",children:"Important Notice"}),e.jsx("p",{className:"text-sm text-yellow-700",children:"Once clearance is approved, the driver will be archived and removed from your active drivers list. You will need to contact an association clerk to unarchive the driver if you want to rehire them."})]})]})})})]})]})]})}),e.jsx(H,{open:C,title:"Delete Pending Clearance Request?",description:"This action cannot be undone and will permanently remove the clearance request.",confirmText:"Delete",confirmVariant:"destructive",loading:w,onCancel:R,onConfirm:q})]})}export{te as default};
