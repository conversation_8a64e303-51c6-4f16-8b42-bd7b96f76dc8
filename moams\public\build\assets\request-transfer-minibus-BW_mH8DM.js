import{u as g,j as e,L as j}from"./app-BbBkOpss.js";import{A as _}from"./app-layout-DI72WroM.js";import{C as y}from"./card-CeuYtmvf.js";import{B as c}from"./app-logo-icon-EJK9zfCM.js";import{I as d}from"./input-error-NZKBKapl.js";import{L as t}from"./label-BDoD3lfN.js";import{I as i}from"./input-DlpWRXnj.js";import{T as N}from"./textarea-DysrrCaS.js";import{A as v}from"./arrow-left-Df03XlYH.js";import{B as T}from"./bus-Bw3CllrX.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function $({minibus:s,userRole:u,ownerName:p="",ownerPhone:f=""}){const{data:r,setData:l,post:h,processing:n,errors:o}=g({number_plate:s.number_plate,current_owner:p,owner_phone:f,new_owner_type:"member",reason:"",ownership_transfer_certificate:null}),x=[{title:"Dashboard",href:"/dashboard"},{title:u==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:s.number_plate,href:`/minibuses/${s.id}`},{title:"Request Transfer",href:`/minibuses/${s.id}/transfer/request`}],b=a=>{l("ownership_transfer_certificate",a.target.files[0])},w=a=>{a.preventDefault(),console.log("Transfer form submission started"),console.log("Minibus ID:",s.id),console.log("Form data:",r),console.log("Route URL:",route("minibuses.transfer.request.store",s.id)),h(route("minibuses.transfer.request.store",s.id),{forceFormData:!0,onStart:()=>{console.log("Transfer Inertia request started")},onSuccess:m=>{console.log("Transfer Inertia request successful:",m)},onError:m=>{console.log("Transfer Inertia request errors:",m)},onFinish:()=>{console.log("Transfer Inertia request finished")}})};return e.jsx(_,{breadcrumbs:x,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6",children:[e.jsxs(c,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left",children:"Request Minibus Transfer"})]}),e.jsx("div",{className:"w-full max-w-3xl mx-auto",children:e.jsxs(y,{className:"p-4 sm:p-6",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("h3",{className:"text-base sm:text-lg md:text-xl font-semibold mb-3",children:[e.jsx(T,{className:"inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom"}),"Ownership Transfer Request Details"]})}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"number_plate",children:"Number Plate"}),e.jsx(i,{id:"number_plate",value:r.number_plate,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"current_owner",children:"Current Owner"}),e.jsx(i,{id:"current_owner",value:r.current_owner,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"owner_phone",children:"Owner Phone"}),e.jsx(i,{id:"owner_phone",value:r.owner_phone,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"new_owner_type",children:"New Owner Type"}),e.jsxs("select",{id:"new_owner_type",value:r.new_owner_type,onChange:a=>l("new_owner_type",a.target.value),className:"mt-1 block w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",disabled:n,children:[e.jsx("option",{value:"member",children:"Association Member"}),e.jsx("option",{value:"non_member",children:"Non-Member"})]}),e.jsx(d,{message:o.new_owner_type})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"reason",children:"Reason for Ownership Transfer"}),e.jsx(N,{id:"reason",value:r.reason,onChange:a=>l("reason",a.target.value),placeholder:"Please provide a detailed reason for requesting the transfer...",rows:4,className:"mt-1 block w-full",disabled:n}),e.jsx(d,{message:o.reason})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"ownership_transfer_certificate",children:"Ownership Transfer Certificate (PDF/Image) (Optional)"}),e.jsx(i,{id:"ownership_transfer_certificate",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:b,className:o.ownership_transfer_certificate?"border-red-500":""}),e.jsx(d,{message:o.ownership_transfer_certificate}),e.jsx("p",{className:"text-sm text-gray-600",children:"Upload supporting documents if available"})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(j,{href:route("minibuses.show",s.id),children:e.jsx(c,{type:"button",variant:"outline",disabled:n,children:"Cancel"})}),e.jsx(c,{type:"submit",disabled:n||!r.reason,className:"bg-blue-500 hover:bg-blue-600 text-white disabled:bg-blue-300",children:n?"Submitting...":"Submit Transfer Request"})]})]})]})})]})})}export{$ as default};
