import{u as x,j as s,H as j}from"./app-BbBkOpss.js";import{I as t}from"./input-error-NZKBKapl.js";import{B as g}from"./app-logo-icon-EJK9zfCM.js";import{I as i}from"./input-DlpWRXnj.js";import{L as m}from"./label-BDoD3lfN.js";import{A as b}from"./auth-layout-D75TDMxC.js";import{L as N}from"./loader-circle-DTMNgGt5.js";import"./utils-BB2gXWs2.js";import"./index-CAhsadr8.js";import"./card-CeuYtmvf.js";function E({token:d,email:n}){const{data:a,setData:p,post:c,processing:l,errors:e,reset:w}=x({token:d,email:n,password:"",password_confirmation:""}),o=r=>{const{id:f,value:h}=r.target;p(f,h)},u=r=>{r.preventDefault(),c(route("password.store"),{onFinish:()=>w("password","password_confirmation")})};return s.jsxs(b,{title:"Reset password",description:"Please enter your new password below",children:[s.jsx(j,{title:"Reset password"}),s.jsx("form",{onSubmit:u,children:s.jsxs("div",{className:"grid gap-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"email",children:"Email"}),s.jsx(i,{id:"email",type:"email",name:"email",autoComplete:"email",value:a.email,className:"mt-1 block w-full",readOnly:!0,onChange:o}),s.jsx(t,{message:e.email,className:"mt-2"})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"password",children:"Password"}),s.jsx(i,{id:"password",type:"password",name:"password",autoComplete:"new-password",value:a.password,className:"mt-1 block w-full",autoFocus:!0,onChange:o,placeholder:"Password"}),s.jsx(t,{message:e.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(i,{id:"password_confirmation",type:"password",name:"password_confirmation",autoComplete:"new-password",value:a.password_confirmation,className:"mt-1 block w-full",onChange:o,placeholder:"Confirm password"}),s.jsx(t,{message:e.password_confirmation,className:"mt-2"})]}),s.jsxs(g,{type:"submit",className:"mt-4 w-full bg-blue-400 hover:bg-blue-500",disabled:l,children:[l&&s.jsx(N,{className:"h-4 w-4 animate-spin"}),"Reset password"]})]})})]})}export{E as default};
