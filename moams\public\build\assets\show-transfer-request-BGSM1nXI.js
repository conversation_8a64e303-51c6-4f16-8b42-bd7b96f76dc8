import{j as e,r as f,b as h,T as N}from"./app-BbBkOpss.js";import{C as i,a as n,b as l,c}from"./card-CeuYtmvf.js";import{A as _,B as a}from"./app-layout-DI72WroM.js";import{n as O}from"./navigation-DXenxO2h.js";import{B as d}from"./app-logo-icon-EJK9zfCM.js";import{C as P}from"./confirm-dialog-CPg0YnBP.js";import{D as k,a as R,b as S,c as B,d as L}from"./dialog-CQdogash.js";import{A as M}from"./arrow-left-Df03XlYH.js";import{T as $}from"./trash-2-WTTZzPA4.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";function se({transferRequest:u,breadcrumbs:j}){if(!u)return e.jsx("div",{children:"Transfer request not found."});const{minibus:t,owner:s,transfer_type:m,status:o,reason:v,created_at:p,id:w,ownership_transfer_certificate:r}=u,[y,x]=f.useState(!1),[C,g]=f.useState(!1),[T,b]=f.useState(!1),D=()=>{x(!0)},q=()=>x(!1),A=async()=>{g(!0),await h.delete(N("minibuses.transfer-requests.destroy",w),{onSuccess:()=>h.visit("/minibuses/transfer-requests")}),g(!1),x(!1)};return e.jsxs(_,{breadcrumbs:j,children:[e.jsxs("div",{className:"container mx-auto px-2 sm:px-4 py-6 sm:py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-1",children:"Minibus Transfer Request Details"}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 flex-wrap gap-2",children:[e.jsxs(d,{variant:"outline",onClick:()=>O(j),className:"w-full sm:w-auto",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("p",{className:"text-muted-foreground flex-1 min-w-0 mt-2 sm:mt-0 sm:ml-4",children:"View and manage this minibus transfer request."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(i,{children:[e.jsx(n,{children:e.jsx(l,{children:"Minibus & Transfer Information"})}),e.jsx(c,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Minibus"}),e.jsx("p",{className:"text-lg font-mono",children:t?t.number_plate:"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Transfer Type"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:m==="internal"?e.jsx(a,{className:"bg-green-100 text-green-800 border-green-200 border border-green-600",children:m}):e.jsx(a,{className:"bg-red-100 text-red-800 border-red-200 border border-red-600",children:m})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:o==="pending"?e.jsx(a,{variant:"outline",className:"text-amber-600 border-amber-600",children:"Pending"}):o==="transferred"?e.jsx(a,{variant:"default",className:"bg-green-600",children:"Transferred"}):e.jsx(a,{variant:"secondary",children:o})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Request Date"}),e.jsx("p",{className:"text-lg",children:p?new Date(p).toLocaleString():"N/A"})]})]})})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsx(l,{children:"Owner Information"})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Owner Name"}),e.jsx("p",{className:"text-lg font-medium",children:s?`${s.first_name} ${s.last_name}`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),e.jsx("p",{className:"text-lg",children:s&&s.phone_number?s.phone_number:"N/A"})]})]}),s&&s.email&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{className:"text-lg",children:s.email})]})]})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsx(l,{children:"Reason for Transfer"})}),e.jsx(c,{children:e.jsx("div",{className:"break-all whitespace-pre-line max-w-full text-base",children:v})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(i,{children:[e.jsx(n,{children:e.jsx(l,{children:"Quick Actions"})}),e.jsxs(c,{className:"space-y-3",children:[t&&e.jsx(d,{className:"w-full justify-start bg-blue-600 hover:bg-blue-700 text-white",onClick:()=>h.visit(N("minibuses.transfer",t.id)),children:"Process Request"}),r&&e.jsx(d,{variant:"outline",className:"w-full justify-start bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",onClick:()=>b(!0),children:"View Transfer Certificate"}),e.jsxs(d,{variant:"destructive",className:"w-full justify-start",onClick:D,children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Delete Request"]})]})]}),e.jsx(k,{open:T,onOpenChange:b,children:e.jsxs(R,{className:"max-w-2xl",children:[e.jsxs(S,{children:[e.jsx(B,{children:"Ownership Transfer Certificate"}),e.jsx(L,{children:r&&r.endsWith(".pdf")?"Preview of the uploaded Transfer Certificate (PDF).":"Preview of the uploaded Transfer Certificate (image)."})]}),r&&r.endsWith(".pdf")?e.jsx("iframe",{src:`/storage/${r}`,title:"Transfer Certificate PDF",className:"w-full h-[70vh] border"}):r?e.jsx("img",{src:`/storage/${r}`,alt:"Transfer Certificate",className:"w-full max-h-[70vh] object-contain"}):null]})})]})]})]}),e.jsx(P,{open:y,title:"Delete Transfer Request?",description:"This action cannot be undone and will permanently remove the transfer request.",confirmText:"Delete",confirmVariant:"destructive",loading:C,onCancel:q,onConfirm:A})]})}export{se as default};
