import{a as S,r as y,j as e,H as M,L as P,b as B}from"./app-BbBkOpss.js";import{n as R}from"./navigation-DXenxO2h.js";import{B as u}from"./app-logo-icon-EJK9zfCM.js";import{L as m}from"./label-BDoD3lfN.js";import{C as t,a as i,b as n,c}from"./card-CeuYtmvf.js";import{A as I,T as V,b as _,c as w,d as C,v as E,w as q,x as z,B as l}from"./app-layout-DI72WroM.js";import{C as H}from"./confirm-dialog-CPg0YnBP.js";import{A as F}from"./arrow-left-Df03XlYH.js";import{S as J}from"./square-pen-s3881TYi.js";import{a as o}from"./utils-BB2gXWs2.js";import{A as G}from"./archive-XA5JSwG5.js";import{U as K}from"./user-BYQKZGaY.js";import{C as O}from"./calendar-HpFaW1Ru.js";import"./index-CAhsadr8.js";import"./index-ChOOE5JL.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./dialog-CQdogash.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]],W=o("Mail",Q);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]],Y=o("Phone",X);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],ee=o("RotateCcw",Z);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const se=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],ae=o("Shield",se);function _e(){var f,v,N,b;const{user:s,users:A=[]}=S().props,[r,x]=y.useState({open:!1,action:null,user:null}),[$,j]=y.useState(!1),L=A.filter(a=>a.roles.some(h=>h.name==="system admin")&&!a.archived_at),p=s.roles.some(a=>a.name==="system admin")&&L.length===1,k=(a,h)=>{x({open:!0,action:a,user:h})},T=()=>x({open:!1,action:null,user:null}),U=async()=>{if(!r.user)return;j(!0);const{id:a}=r.user;(r.action==="archive"||r.action==="unarchive")&&await B.put(`/admin/users/${a}/${r.action}`),j(!1),x({open:!1,action:null,user:null})},g=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"},{title:`${s.first_name} ${s.last_name}`,href:`/admin/users/${s.id}`}],D=a=>{switch(a){case"system admin":return"bg-red-100 text-red-800 border-red-200";case"minibus owner":return"bg-green-100 text-green-800 border-green-200";case"association clerk":return"bg-purple-100 text-purple-800 border-purple-200";case"association manager":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},d=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsxs(I,{breadcrumbs:g,children:[e.jsx(M,{title:`User Details - ${s.first_name} ${s.last_name}`}),e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(u,{variant:"outline",onClick:()=>R(g),className:"w-full sm:w-auto",children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Back"]})}),e.jsx(V,{children:e.jsxs("div",{className:"flex space-x-2",children:[!(p&&!s.archived_at)&&e.jsxs(_,{children:[e.jsx(w,{asChild:!0,children:e.jsx(P,{href:`/admin/users/${s.id}/edit`,children:e.jsx(u,{className:"bg-green-600 hover:bg-green-700",children:e.jsx(J,{className:"h-4 w-4 sm:mr-2"})})})}),e.jsx(C,{children:e.jsx("p",{children:"Edit user information"})})]}),!(p&&!s.archived_at)&&e.jsxs(_,{children:[e.jsx(w,{asChild:!0,children:e.jsx(u,{variant:s.archived_at?"success":"outline",className:"ml-2",onClick:()=>k(s.archived_at?"unarchive":"archive",s),children:s.archived_at?e.jsx(ee,{className:"h-4 w-4 sm:mr-2"}):e.jsx(G,{className:"h-4 w-4 sm:mr-2"})})}),e.jsx(C,{children:e.jsx("p",{children:s.archived_at?"Unarchive user":"Archive user"})})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs(t,{children:[e.jsxs(i,{className:"text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx(E,{className:"h-24 w-24",children:e.jsx(q,{children:e.jsx(z,{text:`${s.first_name} ${s.last_name}`,className:"text-2xl"})})})}),e.jsxs(n,{className:"text-xl",children:[s.first_name," ",s.last_name]})]}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(W,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm",children:s.email})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Y,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm",children:s.phone_number})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(K,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm capitalize",children:s.gender})]}),s.roles.some(a=>a.name==="minibus owner")&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(O,{className:"h-4 w-4 text-gray-500"}),e.jsxs("span",{className:"text-sm",children:["Joined on ",s.joining_date?d(s.joining_date):"N/A"]})]}),s.archived_at&&e.jsx(l,{variant:"destructive",className:"ml-2",children:"Archived"})]})]}),e.jsxs(t,{className:"mt-6",children:[e.jsx(i,{children:e.jsx(n,{children:"Location Information"})}),e.jsx(c,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"District"}),e.jsx("p",{className:"text-sm",children:s.district})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Village/Town"}),e.jsx("p",{className:"text-sm",children:s.village})]})]})})]})]}),e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(n,{className:"flex items-center",children:[e.jsx(ae,{className:"h-5 w-5 mr-2"}),"Roles & Permissions"]})}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Assigned Roles:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.roles.map(a=>e.jsx(l,{variant:"outline",className:`${D(a.name)}`,children:a.name},a.id))})]}),s.roles.length===0&&e.jsx("p",{className:"text-gray-500 text-sm",children:"No roles assigned"})]})})]}),e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(n,{children:"Account Information"})}),e.jsx(c,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Email Verified"}),e.jsx("p",{className:"text-sm mt-1",children:e.jsx(l,{variant:"outline",className:"bg-green-100 text-green-800 border-green-200",children:"Verified"})})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Last Updated"}),e.jsx("p",{className:"text-sm mt-1",children:d(s.updated_at)})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Account Status"}),e.jsx("p",{className:"text-sm mt-1",children:s.archived_at?e.jsx(l,{variant:"destructive",className:"bg-red-100 text-red-800 border-red-200",children:"Inactive"}):e.jsx(l,{variant:"outline",className:"bg-green-100 text-green-800 border-green-200",children:"Active"})})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Created on"}),e.jsx("p",{className:"text-sm mt-1",children:d(s.created_at)}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Last updated ",d(s.updated_at)]})]})]})})]}),s.roles.some(a=>a.name==="minibus owner")&&s.commitment&&e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(n,{children:"Commitment Statement"})}),e.jsx(c,{children:e.jsx("p",{className:"text-sm whitespace-pre-line",children:s.commitment})})]})]})]})]}),e.jsx(H,{open:r.open,title:r.action==="archive"?`Archive ${(f=r.user)==null?void 0:f.first_name} ${(v=r.user)==null?void 0:v.last_name}?`:r.action==="unarchive"?`Unarchive ${(N=r.user)==null?void 0:N.first_name} ${(b=r.user)==null?void 0:b.last_name}?`:"Are you sure?",description:r.action==="archive"?"This user will be archived and will not be able to access the system.":r.action==="unarchive"?"This user will be restored and regain access to the system.":"",confirmText:r.action==="archive"?"Archive":r.action==="unarchive"?"Unarchive":"Confirm",confirmVariant:r.action==="delete"?"destructive":"default",loading:$,onCancel:T,onConfirm:U})]})}export{_e as default};
