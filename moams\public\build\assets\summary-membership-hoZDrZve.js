import{a as se,r as l,R as ae,j as e,b as c,T as h}from"./app-BbBkOpss.js";import{A as ne,C as le}from"./app-layout-DI72WroM.js";import{C as k,a as A,b as M,c as T}from"./card-CeuYtmvf.js";import{B as d}from"./app-logo-icon-EJK9zfCM.js";import{n as ie}from"./navigation-DXenxO2h.js";import{C as re}from"./confirm-dialog-CPg0YnBP.js";import{D as L,a as I,b as R,c as U,d as E}from"./dialog-CQdogash.js";import{C as O}from"./chevron-left-BQ99U5mw.js";import{U as $}from"./user-BYQKZGaY.js";import{C as oe}from"./chart-column-BRNjmg3d.js";import{T as ce}from"./triangle-alert-Dh6aTEch.js";import{C as de}from"./credit-card-KPJaArOK.js";import{C as F}from"./circle-check-big-QC8tmp3b.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";const z=["Registered","Affiliation fee paid"],B=["Unregistered","Need affiliation fee","Affiliation fee not paid"];function ze({user:a,memberships:s,currentFees:p,flash:i,filters:u,unpaidCount:g}){const{auth:Y,userRoles:o}=se().props,W=Y.user;o&&o.includes("association clerk");const[q,f]=l.useState(!1);ae.useEffect(()=>{i!=null&&i.payment_success&&f(!0)},[i]),o&&o.includes("minibus owner");const r=W.id===a.id,[H,K]=l.useState((u==null?void 0:u.tab)||"all"),[me,pe]=l.useState({open:!1,membership:null}),[xe,he]=l.useState(""),[ue,ge]=l.useState("cash"),[fe,ye]=l.useState(!1),[je,m]=l.useState(null),[_,v]=l.useState({open:!1,membership:null}),[V,w]=l.useState(!1),[G,C]=l.useState(!1),[S,y]=l.useState(null),[P,j]=l.useState(null),[be,Ne]=l.useState(null),D=r?[{title:"My Membership",href:"/my-membership"}]:[{title:"Membership Management",href:"/membership-management"},{title:`${a.first_name} ${a.last_name}`,href:`/membership-management/${a.id}`}],J=t=>{K(t),r||c.get(h("membership.management.summary",a.id),{tab:t},{preserveState:!0,preserveScroll:!0})},b=(s==null?void 0:s.data)||[];b.filter(t=>B.includes(t.status));const X=()=>v({open:!1,membership:null}),Q=async()=>{w(!0),await c.delete(h("memberships.destroy",_.membership.id),{onSuccess:()=>v({open:!1,membership:null}),onFinish:()=>w(!1)})},Z=t=>{y(t.id),m(null);const x=t.type.toLowerCase()==="registration"?p.registration:p.affiliation;c.post(h("makePayment.store"),{user_id:a.id,membership_id:t.id,fee_type:t.type.toLowerCase(),amount:x,payment_method:"cash",status:"fulfilled"},{onSuccess:()=>{y(null)},onError:n=>{m(n.amount||n.membership_id||n.fee_type||"Payment failed."),y(null)}})},ee=t=>{var N;j(t.id),console.log("Initiating payment for membership:",t);const x=h("payments.ctechpay.initiate");console.log("Payment URL:",x),console.log("CSRF Token:",(N=document.querySelector('meta[name="csrf-token"]'))==null?void 0:N.getAttribute("content")),fetch(x,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({user_id:a.id,membership_id:t.id,fee_type:t.type.toLowerCase(),amount:t.type.toLowerCase()==="registration"?p.registration:p.affiliation})}).then(n=>(console.log("Payment response status:",n.status),n.json())).then(n=>{console.log("Payment response data:",n),j(null),n.payment_page_url?(console.log("Redirecting to payment page:",n.payment_page_url),window.location.href=n.payment_page_url):n.error?(console.error("Payment error:",n.error),m("Payment initiation failed: "+n.error)):(console.error("No payment URL received:",n),m("Payment initiation failed: No payment URL received."))}).catch(n=>{j(null),console.error("Payment error:",n),m("Payment initiation failed. Please try again.")})},te=[{key:"all",label:`All (${(s==null?void 0:s.total)||0})`},{key:"paid",label:"Paid"},{key:"unpaid",label:"Unpaid"}];return e.jsx(ne,{breadcrumbs:D,children:e.jsxs("div",{className:"max-w-5xl mx-auto p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-4 justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",onClick:()=>ie(D),children:[e.jsx(O,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsxs("span",{className:"text-muted-foreground text-base font-medium flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-blue-600"}),r?"My Membership":`${a.first_name} ${a.last_name}`]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&o.includes("association clerk")&&e.jsxs(d,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 hover:bg-green-200 flex items-center gap-2",onClick:()=>c.visit("/payment-analytics"),children:[e.jsx(oe,{className:"h-4 w-4"}),"Payment Analytics"]}),a.national_id_path&&o&&(o.includes("association clerk")||o.includes("system admin"))&&e.jsx(d,{variant:"outline",className:"bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",onClick:()=>C(!0),children:"View National ID"})]})]}),r&&e.jsxs(k,{className:"mb-6 border-blue-200 bg-blue-50",children:[e.jsx(A,{children:e.jsxs(M,{className:"flex items-center gap-2 text-blue-800",children:[e.jsx($,{className:"h-5 w-5"}),"Membership Commitment"]})}),e.jsx(T,{children:e.jsx("p",{className:"text-blue-700 mb-2",children:"I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all times."})})]}),r&&g>0&&e.jsxs(k,{className:"mb-6 border-orange-200 bg-orange-50",children:[e.jsx(A,{children:e.jsxs(M,{className:"flex items-center gap-2 text-orange-800",children:[e.jsx(ce,{className:"h-5 w-5"}),"Unpaid Memberships"]})}),e.jsx(T,{children:e.jsxs("p",{className:"text-orange-700 mb-4",children:["You have ",g," unpaid membership",g>1?"s":"",". Please settle your outstanding fees to maintain active membership status."]})})]}),!r&&e.jsx("div",{className:"flex gap-2 mb-8 border-b",children:te.map(t=>e.jsx("button",{className:`px-4 py-2 -mb-px border-b-2 font-medium transition-colors duration-150 ${H===t.key?"border-blue-600 text-blue-600":"border-transparent text-gray-500 hover:text-blue-600"}`,onClick:()=>J(t.key),children:t.label},t.key))}),e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Year"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Type"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Status"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Start Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"End Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Fee Obligation"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Actions"})]})}),e.jsx("tbody",{children:b.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"text-center text-gray-500 py-6",children:r?"No unpaid memberships found. All your memberships are up to date!":"No memberships found."})}):b.map(t=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-2",children:new Date(t.start_date).getFullYear()}),e.jsx("td",{className:"px-4 py-2 capitalize",children:t.type}),e.jsx("td",{className:"px-4 py-2",children:e.jsx("span",{className:`inline-block px-2 py-1 rounded text-xs font-semibold ${z.includes(t.status)?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})}),e.jsx("td",{className:"px-4 py-2",children:new Date(t.start_date).toLocaleDateString()}),e.jsx("td",{className:"px-4 py-2",children:new Date(t.end_date).toLocaleDateString()}),e.jsx("td",{className:"px-4 py-2",children:z.includes(t.status)?"Fulfilled":"Not fulfilled"}),e.jsx("td",{className:"px-4 py-2 space-x-2",children:B.includes(t.status)&&e.jsxs(d,{size:"sm",className:`flex items-center justify-center ${r?"bg-orange-600 hover:bg-orange-700":"bg-blue-400 hover:bg-blue-500"} text-white font-semibold`,onClick:()=>{r?ee(t):Z(t)},disabled:S===t.id||P===t.id,children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),S===t.id?"Processing...":r?P===t.id?"Redirecting...":"Pay Now":"Mark Paid"]})})]},t.id))})]})}),s&&s.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",s.from," to ",s.to," of ",s.total," results"]}),s.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(d,{variant:"outline",size:"sm",onClick:()=>c.get(s.prev_page_url),disabled:!s.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[s.current_page," of ",s.last_page]}),e.jsxs(d,{variant:"outline",size:"sm",onClick:()=>c.get(s.next_page_url),disabled:!s.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(le,{className:"h-4 w-4"})]})]})]})}),e.jsx(re,{open:_.open,title:"Delete Membership",description:"Are you sure you want to delete this paid membership record? This action cannot be undone.",onConfirm:Q,onCancel:X,confirmText:"Delete",cancelText:"Cancel",loading:V}),e.jsx(L,{open:G,onOpenChange:C,children:e.jsxs(I,{className:"max-w-2xl",children:[e.jsxs(R,{children:[e.jsx(U,{children:"National ID"}),e.jsx(E,{children:a.national_id_path&&a.national_id_path.endsWith(".pdf")?"Preview of the uploaded National ID (PDF).":"Preview of the uploaded National ID (image)."})]}),a.national_id_path&&a.national_id_path.endsWith(".pdf")?e.jsx("iframe",{src:`/storage/${a.national_id_path}`,title:"National ID PDF",className:"w-full h-[70vh] border"}):a.national_id_path?e.jsx("img",{src:`/storage/${a.national_id_path}`,alt:"National ID",className:"w-full max-h-[70vh] object-contain"}):null]})}),e.jsx(L,{open:q,onOpenChange:f,children:e.jsxs(I,{className:"sm:max-w-md",children:[e.jsx(R,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(F,{className:"h-8 w-8 text-green-600"})}),e.jsxs("div",{children:[e.jsx(U,{className:"text-green-700",children:"Payment Successful!"}),e.jsx(E,{className:"text-green-600",children:"Your payment has been processed successfully"})]})]})}),(i==null?void 0:i.payment_success)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[e.jsx("p",{className:"text-green-800 font-medium mb-3",children:i.payment_success.message}),e.jsxs("div",{className:"space-y-2 text-sm text-green-700",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium",children:"Amount:"}),e.jsxs("span",{className:"font-mono",children:["MWK ",Number(i.payment_success.amount).toLocaleString()]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium",children:"Time:"}),e.jsx("span",{children:new Date(i.payment_success.timestamp).toLocaleString()})]})]})]}),e.jsx("div",{className:"flex justify-end gap-2",children:e.jsxs(d,{onClick:()=>f(!1),className:"bg-green-600 hover:bg-green-700",children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Got it!"]})})]})]})})]})})}export{ze as default};
