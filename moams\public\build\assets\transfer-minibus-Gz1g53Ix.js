import{r as w,u as S,j as e,L,T as j}from"./app-BbBkOpss.js";import{A}from"./app-layout-DI72WroM.js";import{C as B}from"./card-CeuYtmvf.js";import{B as l}from"./app-logo-icon-EJK9zfCM.js";import{I as y}from"./input-error-NZKBKapl.js";import{L as g}from"./label-BDoD3lfN.js";import{M as E}from"./minibus-owner-combobox-NImMr_rD.js";import{A as I}from"./arrow-left-Df03XlYH.js";import{B as D}from"./bus-Bw3CllrX.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./search-CaYnfO0m.js";import"./loader-circle-DTMNgGt5.js";function se({minibus:r,minibusOwners:F,userRole:_}){var d,c,p,x,f,h;const[N,v]=w.useState(null),{data:t,setData:o,post:T,processing:n,errors:m}=S({new_owner_id:"",transfer_type:"internal"}),C=[{title:"Dashboard",href:"/dashboard"},{title:_==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:r.number_plate,href:`/minibuses/${r.id}`},{title:"Transfer Ownership",href:`/minibuses/${r.id}/transfer`}],O=s=>{o(i=>({...i,transfer_type:s,new_owner_id:s==="external"?"":i.new_owner_id}))},M=s=>{s.preventDefault(),T(j("minibuses.transfer.process",r.id))},k=w.useCallback(async s=>(await(await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`)).json()).filter(a=>{var u,b;return a.id!==null&&a.id!==void 0&&a.id!==""&&String(a.id)!==String((b=(u=r.owner)==null?void 0:u.user)==null?void 0:b.id)}),[(c=(d=r.owner)==null?void 0:d.user)==null?void 0:c.id]);return e.jsx(A,{breadcrumbs:C,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6",children:[e.jsxs(l,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left",children:"Transfer Minibus Ownership"})]}),e.jsx("div",{className:"w-full max-w-3xl mx-auto",children:e.jsxs(B,{className:"p-4 sm:p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("h3",{className:"text-base sm:text-lg md:text-xl font-semibold mb-3",children:[e.jsx(D,{className:"inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom"}),r.number_plate]}),e.jsxs("p",{className:"text-sm sm:text-base text-gray-600",children:["Current Owner: ",(x=(p=r.owner)==null?void 0:p.user)==null?void 0:x.first_name," ",(h=(f=r.owner)==null?void 0:f.user)==null?void 0:h.last_name]})]}),e.jsxs("form",{onSubmit:M,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"transfer_type",children:"Transfer Type"}),e.jsxs("select",{id:"transfer_type",value:t.transfer_type,onChange:s=>O(s.target.value),className:"mt-1 block w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",disabled:n,children:[e.jsx("option",{value:"internal",children:"Internal Transfer (To Another Member)"}),e.jsx("option",{value:"external",children:"External Transfer (To Non-Member)"})]}),e.jsx(y,{message:m.transfer_type})]}),t.transfer_type==="internal"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"new_owner_id",children:"New Owner"}),e.jsx(E,{value:N||null,onChange:s=>{v(s),o("new_owner_id",(s==null?void 0:s.id)||"")},fetchOwners:k,placeholder:"Select new owner..."}),e.jsx(y,{message:m.new_owner_id})]}),t.transfer_type==="external"&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:e.jsx("p",{className:"text-sm text-yellow-700",children:"Note: External transfer will mark this minibus as transferred to a non-member and archive it. Only association clerks will be able to access it until the new owner joins the association."})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(L,{href:j("minibuses.show",r.id),children:e.jsx(l,{type:"button",variant:"outline",disabled:n,children:"Cancel"})}),e.jsx(l,{type:"submit",disabled:n||!t.new_owner_id&&t.transfer_type==="internal",className:"bg-green-500 hover:bg-green-600 text-white disabled:bg-green-300",children:n?"Processing...":"Transfer Ownership"})]})]})]})})]})})}export{se as default};
