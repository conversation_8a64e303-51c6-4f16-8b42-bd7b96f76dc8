import{r as j,j as e,H as T,b as r,T as A}from"./app-BbBkOpss.js";import{C as u,a as N,b as g,c as f}from"./card-CeuYtmvf.js";import{A as F,B as i,C as B}from"./app-layout-DI72WroM.js";import{B as l}from"./app-logo-icon-EJK9zfCM.js";import{I as k}from"./input-DlpWRXnj.js";import{L as v}from"./label-BDoD3lfN.js";import{S as L,a as D,b as P,c as z,d}from"./select-B1BNOBy4.js";import{n as E}from"./navigation-DXenxO2h.js";import{S as b}from"./search-CaYnfO0m.js";import{F as V}from"./filter-zx04Nj3d.js";import{B as y}from"./bus-Bw3CllrX.js";import{E as H}from"./eye-BL-84HuG.js";import{C as I}from"./chevron-left-BQ99U5mw.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./dollar-sign-Cgj5GuZe.js";import"./triangle-alert-Dh6aTEch.js";import"./users-vqmOp4p6.js";import"./index-CHr_Yg1t.js";function ie({transferRequests:a,breadcrumbs:o,filters:t}){const[n,x]=j.useState((t==null?void 0:t.search)||""),[c,h]=j.useState((t==null?void 0:t.status)||"all"),m=(a==null?void 0:a.data)||[],w=s=>{x(s),r.get("/minibuses/transfer-requests",{search:s,status:c},{preserveState:!0,preserveScroll:!0})},S=s=>{h(s),r.get("/minibuses/transfer-requests",{search:n,status:s},{preserveState:!0,preserveScroll:!0})},C=()=>{x(""),h("all"),r.get("/minibuses/transfer-requests",{},{preserveState:!0,preserveScroll:!0})},_=s=>{switch(s){case"pending":return e.jsx(i,{variant:"outline",className:"text-amber-600 border-amber-600",children:"Pending"});case"transferred":return e.jsx(i,{variant:"default",className:"bg-green-600",children:"Transferred"});default:return e.jsx(i,{variant:"secondary",children:s})}};return e.jsxs(F,{breadcrumbs:o,children:[e.jsx(T,{title:"Minibus Transfer Requests"}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx(l,{variant:"outline",onClick:()=>E(o),children:"Back"}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View and manage minibus transfer requests."})]}),e.jsxs(u,{className:"mb-6 w-full",children:[e.jsx(N,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(f,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(v,{htmlFor:"search",children:"Search"}),e.jsxs("div",{className:"relative",children:[e.jsx(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx(k,{id:"search",placeholder:"Search by minibus, owner, reason, or type...",value:n,onChange:s=>w(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(v,{htmlFor:"status",children:"Status"}),e.jsxs(L,{value:c,onValueChange:S,children:[e.jsx(D,{children:e.jsx(P,{placeholder:"All Requests"})}),e.jsxs(z,{children:[e.jsx(d,{value:"all",children:"All Requests"}),e.jsx(d,{value:"pending",children:"Pending"}),e.jsx(d,{value:"transferred",children:"Transferred"})]})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(l,{variant:"outline",onClick:C,className:"w-full",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsxs(u,{className:"w-full border-0 shadow-none",children:[e.jsx(N,{className:"px-0",children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Transfer Requests (",(a==null?void 0:a.total)||m.length,")"]})}),e.jsx(f,{className:"p-0",children:m.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Minibus"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Owner"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Transfer Type"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Reason"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Submitted Date"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:m.map(s=>{var p;return e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 font-medium",children:((p=s.minibus)==null?void 0:p.number_plate)||"N/A"}),e.jsx("td",{className:"px-4 py-3",children:s.owner?`${s.owner.first_name} ${s.owner.last_name}`:"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx(i,{variant:"outline",className:"capitalize",children:s.transfer_type||"N/A"})}),e.jsx("td",{className:"px-4 py-3 max-w-24",children:e.jsx("div",{className:"truncate",title:s.reason||"N/A",children:s.reason||"N/A"})}),e.jsx("td",{className:"px-4 py-3",children:_(s.status)}),e.jsx("td",{className:"px-4 py-3",children:s.created_at?new Date(s.created_at).toLocaleDateString():"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>r.visit(A("minibuses.transfer-requests.show",s.id)),children:[e.jsx(H,{className:"h-4 w-4 mr-2"}),s.status==="pending"?"Review":"View Details"]})})]},s.id)})})]})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No transfer requests found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:n||c!=="all"?"Try adjusting your search or filter criteria.":"No transfer requests have been submitted yet."})]})})]}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>r.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(I,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>r.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(B,{className:"h-4 w-4"})]})]})]})})]})]})}export{ie as default};
