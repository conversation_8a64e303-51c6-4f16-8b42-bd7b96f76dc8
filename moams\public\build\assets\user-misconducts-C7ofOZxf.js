import{a as L,r as S,T as i,j as e,L as o}from"./app-BbBkOpss.js";import{A as _}from"./app-layout-DI72WroM.js";import{C as x,a as h,b as j,c as f}from"./card-CeuYtmvf.js";import{T as D,a as k,b as w,c as n,d as A,e as c}from"./table-DIpduh_T.js";import{B as d}from"./app-logo-icon-EJK9zfCM.js";import{I as P}from"./input-DlpWRXnj.js";import{L as E}from"./label-BDoD3lfN.js";import{n as R}from"./navigation-DXenxO2h.js";import{P as B}from"./pagination-Dav6jFJz.js";import{C as F}from"./chevron-left-BQ99U5mw.js";import{P as H}from"./plus-BToMwexr.js";import{U as I}from"./user-BYQKZGaY.js";import{S as U}from"./search-CaYnfO0m.js";import{F as z}from"./filter-zx04Nj3d.js";import{T as y}from"./triangle-alert-Dh6aTEch.js";import"./utils-BB2gXWs2.js";import"./index-ChOOE5JL.js";import"./index-CAhsadr8.js";import"./bus-Bw3CllrX.js";import"./dollar-sign-Cgj5GuZe.js";import"./users-vqmOp4p6.js";function me({user:r,misconducts:a}){const{userRoles:t,flash:$}=L().props,p=t&&t.includes("association clerk");t&&t.includes("association manager");const u=t&&t.includes("system admin"),C=p||u,T=p||u,[m,g]=S.useState(""),M=a.data.filter(s=>{var v;const b=m.toLowerCase();return s.name.toLowerCase().includes(b)||((v=s.description)==null?void 0:v.toLowerCase().includes(b))}),l=a.data||[],N=[{title:"Misconduct Management",href:i("misconducts.index")},{title:`${r.first_name} ${r.last_name}`,href:i("users.misconducts",r.id)}];return e.jsx(_,{breadcrumbs:N,children:e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>R(N),children:[e.jsx(F,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsxs("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:["Misconduct history for ",r.first_name," ",r.last_name]})})]}),e.jsx("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:C&&e.jsx(o,{href:i("misconducts.create"),children:e.jsxs(d,{className:"w-fit flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2",children:[e.jsx(H,{className:"h-4 w-4"}),"Report New Misconduct"]})})})]}),e.jsxs(x,{className:"mb-6",children:[e.jsx(h,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-blue-600"}),"Minibus Owner Information"]})}),e.jsx(f,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Name"}),e.jsxs("p",{className:"font-medium",children:[r.first_name," ",r.last_name]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{children:r.email})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Phone"}),e.jsx("p",{children:r.phone_number})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"District"}),e.jsx("p",{children:r.district})]})]})})]}),e.jsxs(x,{className:"mb-6",children:[e.jsx(h,{children:e.jsx(j,{className:"text-lg",children:"Search Misconducts"})}),e.jsx(f,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(E,{htmlFor:"search",children:"Search"}),e.jsxs("div",{className:"relative",children:[e.jsx(U,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),e.jsx(P,{id:"search",placeholder:"Search by misconduct type or description...",value:m,onChange:s=>g(s.target.value),className:"pl-10"})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(d,{variant:"outline",onClick:()=>{g(""),setDisplayCount(6)},className:"w-full",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Misconduct Summary"}),e.jsxs("p",{className:"text-blue-700",children:["Total misconducts: ",a.total," | Showing: ",l.length," of ",M.length]})]})})})}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(x,{className:"w-full border-0",children:[e.jsx(h,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Driver Misconducts Reported (",l.length,")"]})}),e.jsx(f,{className:"p-0",children:l.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No driver misconducts reported by this minibus owner"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:m?"Try adjusting your search criteria.":"This minibus owner has not reported any driver misconducts."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(D,{className:"w-full min-w-[600px]",children:[e.jsx(k,{children:e.jsxs(w,{children:[e.jsx(n,{children:"Misconduct Type"}),e.jsx(n,{children:"Date"}),e.jsx(n,{children:"Description"}),e.jsx(n,{children:"Actions"})]})}),e.jsx(A,{children:l.map(s=>e.jsxs(w,{children:[e.jsx(c,{className:"font-medium",children:s.name}),e.jsx(c,{children:new Date(s.offense_date).toLocaleDateString()}),e.jsx(c,{children:e.jsx("div",{className:"max-w-xs truncate",title:s.description||"N/A",children:s.description||"N/A"})}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{href:i("misconducts.show",s.id),children:e.jsx(d,{variant:"outline",size:"sm",className:"text-blue-700 border-blue-500 hover:bg-blue-50",children:"View Details"})}),T&&e.jsx(o,{href:i("misconducts.edit",s.id),children:e.jsx(d,{variant:"secondary",size:"sm",className:"bg-indigo-100 text-indigo-800 hover:bg-indigo-200",children:"Edit"})})]})})]},s.id))})]})})})]})}),a.total>0&&a.total>a.per_page&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsx(B,{data:a})]})})]})})})}export{me as default};
