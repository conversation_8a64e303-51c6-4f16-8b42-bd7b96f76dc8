import{r as W}from"./app-BbBkOpss.js";function xe(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=xe(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Ge(){for(var e,t,r=0,o="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=xe(e))&&(o&&(o+=" "),o+=t);return o}/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ke=(...e)=>e.filter((t,r,o)=>!!t&&t.trim()!==""&&o.indexOf(t)===r).join(" ").trim();/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Te={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=W.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:c,iconNode:l,...u},p)=>W.createElement("svg",{ref:p,...Te,width:t,height:t,stroke:e,strokeWidth:o?Number(r)*24/Number(t):r,className:ke("lucide",a),...u},[...l.map(([g,h])=>W.createElement(g,h)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ho=(e,t)=>{const r=W.forwardRef(({className:o,...a},c)=>W.createElement(Le,{ref:c,iconNode:t,className:ke(`lucide-${Ee(e)}`,o),...a}));return r.displayName=`${e}`,r},ae="-",Ne=e=>{const t=Ve(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:l=>{const u=l.split(ae);return u[0]===""&&u.length!==1&&u.shift(),ye(u,t)||je(l)},getConflictingClassGroupIds:(l,u)=>{const p=r[l]||[];return u&&o[l]?[...p,...o[l]]:p}}},ye=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?ye(e.slice(1),o):void 0;if(a)return a;if(t.validators.length===0)return;const c=e.join(ae);return(l=t.validators.find(({validator:u})=>u(c)))==null?void 0:l.classGroupId},be=/^\[(.+)\]$/,je=e=>{if(be.test(e)){const t=be.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Ve=e=>{const{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(const a in r)te(r[a],o,a,t);return o},te=(e,t,r,o)=>{e.forEach(a=>{if(typeof a=="string"){const c=a===""?t:ge(t,a);c.classGroupId=r;return}if(typeof a=="function"){if(Oe(a)){te(a(o),t,r,o);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([c,l])=>{te(l,ge(t,c),r,o)})})},ge=(e,t)=>{let r=e;return t.split(ae).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},Oe=e=>e.isThemeGetter,$e=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const a=(c,l)=>{r.set(c,l),t++,t>e&&(t=0,o=r,r=new Map)};return{get(c){let l=r.get(c);if(l!==void 0)return l;if((l=o.get(c))!==void 0)return a(c,l),l},set(c,l){r.has(c)?r.set(c,l):a(c,l)}}},se="!",ne=":",Be=ne.length,Fe=e=>{const{prefix:t,experimentalParseClassName:r}=e;let o=a=>{const c=[];let l=0,u=0,p=0,g;for(let k=0;k<a.length;k++){let y=a[k];if(l===0&&u===0){if(y===ne){c.push(a.slice(p,k)),p=k+Be;continue}if(y==="/"){g=k;continue}}y==="["?l++:y==="]"?l--:y==="("?u++:y===")"&&u--}const h=c.length===0?a:a.substring(p),A=_e(h),O=A!==h,$=g&&g>p?g-p:void 0;return{modifiers:c,hasImportantModifier:O,baseClassName:A,maybePostfixModifierPosition:$}};if(t){const a=t+ne,c=o;o=l=>l.startsWith(a)?c(l.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(r){const a=o;o=c=>r({className:c,parseClassName:a})}return o},_e=e=>e.endsWith(se)?e.substring(0,e.length-1):e.startsWith(se)?e.substring(1):e,We=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const a=[];let c=[];return o.forEach(l=>{l[0]==="["||t[l]?(a.push(...c.sort(),l),c=[]):c.push(l)}),a.push(...c.sort()),a}},Ue=e=>({cache:$e(e.cacheSize),parseClassName:Fe(e),sortModifiers:We(e),...Ne(e)}),qe=/\s+/,He=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:c}=t,l=[],u=e.trim().split(qe);let p="";for(let g=u.length-1;g>=0;g-=1){const h=u[g],{isExternal:A,modifiers:O,hasImportantModifier:$,baseClassName:k,maybePostfixModifierPosition:y}=r(h);if(A){p=h+(p.length>0?" "+p:p);continue}let G=!!y,S=o(G?k.substring(0,y):k);if(!S){if(!G){p=h+(p.length>0?" "+p:p);continue}if(S=o(k),!S){p=h+(p.length>0?" "+p:p);continue}G=!1}const U=c(O).join(":"),B=$?U+se:U,E=B+S;if(l.includes(E))continue;l.push(E);const T=a(S,G);for(let I=0;I<T.length;++I){const F=T[I];l.push(B+F)}p=h+(p.length>0?" "+p:p)}return p};function Je(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=ve(t))&&(o&&(o+=" "),o+=r);return o}const ve=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=ve(e[o]))&&(r&&(r+=" "),r+=t);return r};function Ke(e,...t){let r,o,a,c=l;function l(p){const g=t.reduce((h,A)=>A(h),e());return r=Ue(g),o=r.cache.get,a=r.cache.set,c=u,u(p)}function u(p){const g=o(p);if(g)return g;const h=He(p,r);return a(p,h),h}return function(){return c(Je.apply(null,arguments))}}const f=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},ze=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ce=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Xe=/^\d+\/\d+$/,Ze=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,De=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Qe=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Ye=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,eo=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,N=e=>Xe.test(e),m=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),oe=e=>e.endsWith("%")&&m(e.slice(0,-1)),C=e=>Ze.test(e),oo=()=>!0,ro=e=>De.test(e)&&!Qe.test(e),Ae=()=>!1,to=e=>Ye.test(e),so=e=>eo.test(e),no=e=>!s(e)&&!n(e),ao=e=>j(e,Ie,Ae),s=e=>ze.test(e),P=e=>j(e,Re,ro),re=e=>j(e,po,m),he=e=>j(e,Me,Ae),io=e=>j(e,Se,so),Z=e=>j(e,Pe,to),n=e=>Ce.test(e),_=e=>V(e,Re),lo=e=>V(e,uo),we=e=>V(e,Me),co=e=>V(e,Ie),mo=e=>V(e,Se),D=e=>V(e,Pe,!0),j=(e,t,r)=>{const o=ze.exec(e);return o?o[1]?t(o[1]):r(o[2]):!1},V=(e,t,r=!1)=>{const o=Ce.exec(e);return o?o[1]?t(o[1]):r:!1},Me=e=>e==="position"||e==="percentage",Se=e=>e==="image"||e==="url",Ie=e=>e==="length"||e==="size"||e==="bg-size",Re=e=>e==="length",po=e=>e==="number",uo=e=>e==="family-name",Pe=e=>e==="shadow",fo=()=>{const e=f("color"),t=f("font"),r=f("text"),o=f("font-weight"),a=f("tracking"),c=f("leading"),l=f("breakpoint"),u=f("container"),p=f("spacing"),g=f("radius"),h=f("shadow"),A=f("inset-shadow"),O=f("text-shadow"),$=f("drop-shadow"),k=f("blur"),y=f("perspective"),G=f("aspect"),S=f("ease"),U=f("animate"),B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],E=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...E(),n,s],I=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],d=()=>[n,s,p],v=()=>[N,"full","auto",...d()],ie=()=>[M,"none","subgrid",n,s],le=()=>["auto",{span:["full",M,n,s]},M,n,s],q=()=>[M,"auto",n,s],ce=()=>["auto","min","max","fr",n,s],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...d()],R=()=>[N,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...d()],i=()=>[e,n,s],de=()=>[...E(),we,he,{position:[n,s]}],me=()=>["no-repeat",{repeat:["","x","y","space","round"]}],pe=()=>["auto","cover","contain",co,ao,{size:[n,s]}],Y=()=>[oe,_,P],w=()=>["","none","full",g,n,s],x=()=>["",m,_,P],H=()=>["solid","dashed","dotted","double"],ue=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>[m,oe,we,he],fe=()=>["","none",k,n,s],J=()=>["none",m,n,s],K=()=>["none",m,n,s],ee=()=>[m,n,s],X=()=>[N,"full",...d()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[oo],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[no],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",m],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",N,s,n,G]}],container:["container"],columns:[{columns:[m,s,n,u]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",n,s]}],basis:[{basis:[N,"full","auto",u,...d()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[m,N,"auto","initial","none",s]}],grow:[{grow:["",m,n,s]}],shrink:[{shrink:["",m,n,s]}],order:[{order:[M,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":ie()}],"col-start-end":[{col:le()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":ie()}],"row-start-end":[{row:le()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ce()}],"auto-rows":[{"auto-rows":ce()}],gap:[{gap:d()}],"gap-x":[{"gap-x":d()}],"gap-y":[{"gap-y":d()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:d()}],px:[{px:d()}],py:[{py:d()}],ps:[{ps:d()}],pe:[{pe:d()}],pt:[{pt:d()}],pr:[{pr:d()}],pb:[{pb:d()}],pl:[{pl:d()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":d()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":d()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[u,"screen",...R()]}],"min-w":[{"min-w":[u,"screen","none",...R()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[l]},...R()]}],h:[{h:["screen","lh",...R()]}],"min-h":[{"min-h":["screen","lh","none",...R()]}],"max-h":[{"max-h":["screen","lh",...R()]}],"font-size":[{text:["base",r,_,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,n,re]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",oe,s]}],"font-family":[{font:[lo,s,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[m,"none",n,re]}],leading:[{leading:[c,...d()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:i()}],"text-color":[{text:i()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:[m,"from-font","auto",n,P]}],"text-decoration-color":[{decoration:i()}],"underline-offset":[{"underline-offset":[m,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:d()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:de()}],"bg-repeat":[{bg:me()}],"bg-size":[{bg:pe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,n,s],radial:["",n,s],conic:[M,n,s]},mo,io]}],"bg-color":[{bg:i()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:i()}],"gradient-via":[{via:i()}],"gradient-to":[{to:i()}],rounded:[{rounded:w()}],"rounded-s":[{"rounded-s":w()}],"rounded-e":[{"rounded-e":w()}],"rounded-t":[{"rounded-t":w()}],"rounded-r":[{"rounded-r":w()}],"rounded-b":[{"rounded-b":w()}],"rounded-l":[{"rounded-l":w()}],"rounded-ss":[{"rounded-ss":w()}],"rounded-se":[{"rounded-se":w()}],"rounded-ee":[{"rounded-ee":w()}],"rounded-es":[{"rounded-es":w()}],"rounded-tl":[{"rounded-tl":w()}],"rounded-tr":[{"rounded-tr":w()}],"rounded-br":[{"rounded-br":w()}],"rounded-bl":[{"rounded-bl":w()}],"border-w":[{border:x()}],"border-w-x":[{"border-x":x()}],"border-w-y":[{"border-y":x()}],"border-w-s":[{"border-s":x()}],"border-w-e":[{"border-e":x()}],"border-w-t":[{"border-t":x()}],"border-w-r":[{"border-r":x()}],"border-w-b":[{"border-b":x()}],"border-w-l":[{"border-l":x()}],"divide-x":[{"divide-x":x()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":x()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...H(),"hidden","none"]}],"divide-style":[{divide:[...H(),"hidden","none"]}],"border-color":[{border:i()}],"border-color-x":[{"border-x":i()}],"border-color-y":[{"border-y":i()}],"border-color-s":[{"border-s":i()}],"border-color-e":[{"border-e":i()}],"border-color-t":[{"border-t":i()}],"border-color-r":[{"border-r":i()}],"border-color-b":[{"border-b":i()}],"border-color-l":[{"border-l":i()}],"divide-color":[{divide:i()}],"outline-style":[{outline:[...H(),"none","hidden"]}],"outline-offset":[{"outline-offset":[m,n,s]}],"outline-w":[{outline:["",m,_,P]}],"outline-color":[{outline:i()}],shadow:[{shadow:["","none",h,D,Z]}],"shadow-color":[{shadow:i()}],"inset-shadow":[{"inset-shadow":["none",A,D,Z]}],"inset-shadow-color":[{"inset-shadow":i()}],"ring-w":[{ring:x()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:i()}],"ring-offset-w":[{"ring-offset":[m,P]}],"ring-offset-color":[{"ring-offset":i()}],"inset-ring-w":[{"inset-ring":x()}],"inset-ring-color":[{"inset-ring":i()}],"text-shadow":[{"text-shadow":["none",O,D,Z]}],"text-shadow-color":[{"text-shadow":i()}],opacity:[{opacity:[m,n,s]}],"mix-blend":[{"mix-blend":[...ue(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ue()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[m]}],"mask-image-linear-from-pos":[{"mask-linear-from":b()}],"mask-image-linear-to-pos":[{"mask-linear-to":b()}],"mask-image-linear-from-color":[{"mask-linear-from":i()}],"mask-image-linear-to-color":[{"mask-linear-to":i()}],"mask-image-t-from-pos":[{"mask-t-from":b()}],"mask-image-t-to-pos":[{"mask-t-to":b()}],"mask-image-t-from-color":[{"mask-t-from":i()}],"mask-image-t-to-color":[{"mask-t-to":i()}],"mask-image-r-from-pos":[{"mask-r-from":b()}],"mask-image-r-to-pos":[{"mask-r-to":b()}],"mask-image-r-from-color":[{"mask-r-from":i()}],"mask-image-r-to-color":[{"mask-r-to":i()}],"mask-image-b-from-pos":[{"mask-b-from":b()}],"mask-image-b-to-pos":[{"mask-b-to":b()}],"mask-image-b-from-color":[{"mask-b-from":i()}],"mask-image-b-to-color":[{"mask-b-to":i()}],"mask-image-l-from-pos":[{"mask-l-from":b()}],"mask-image-l-to-pos":[{"mask-l-to":b()}],"mask-image-l-from-color":[{"mask-l-from":i()}],"mask-image-l-to-color":[{"mask-l-to":i()}],"mask-image-x-from-pos":[{"mask-x-from":b()}],"mask-image-x-to-pos":[{"mask-x-to":b()}],"mask-image-x-from-color":[{"mask-x-from":i()}],"mask-image-x-to-color":[{"mask-x-to":i()}],"mask-image-y-from-pos":[{"mask-y-from":b()}],"mask-image-y-to-pos":[{"mask-y-to":b()}],"mask-image-y-from-color":[{"mask-y-from":i()}],"mask-image-y-to-color":[{"mask-y-to":i()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":b()}],"mask-image-radial-to-pos":[{"mask-radial-to":b()}],"mask-image-radial-from-color":[{"mask-radial-from":i()}],"mask-image-radial-to-color":[{"mask-radial-to":i()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":E()}],"mask-image-conic-pos":[{"mask-conic":[m]}],"mask-image-conic-from-pos":[{"mask-conic-from":b()}],"mask-image-conic-to-pos":[{"mask-conic-to":b()}],"mask-image-conic-from-color":[{"mask-conic-from":i()}],"mask-image-conic-to-color":[{"mask-conic-to":i()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:de()}],"mask-repeat":[{mask:me()}],"mask-size":[{mask:pe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:fe()}],brightness:[{brightness:[m,n,s]}],contrast:[{contrast:[m,n,s]}],"drop-shadow":[{"drop-shadow":["","none",$,D,Z]}],"drop-shadow-color":[{"drop-shadow":i()}],grayscale:[{grayscale:["",m,n,s]}],"hue-rotate":[{"hue-rotate":[m,n,s]}],invert:[{invert:["",m,n,s]}],saturate:[{saturate:[m,n,s]}],sepia:[{sepia:["",m,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":fe()}],"backdrop-brightness":[{"backdrop-brightness":[m,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[m,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",m,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m,n,s]}],"backdrop-invert":[{"backdrop-invert":["",m,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[m,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[m,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",m,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":d()}],"border-spacing-x":[{"border-spacing-x":d()}],"border-spacing-y":[{"border-spacing-y":d()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[m,"initial",n,s]}],ease:[{ease:["linear","initial",S,n,s]}],delay:[{delay:[m,n,s]}],animate:[{animate:["none",U,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,n,s]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:J()}],"rotate-x":[{"rotate-x":J()}],"rotate-y":[{"rotate-y":J()}],"rotate-z":[{"rotate-z":J()}],scale:[{scale:K()}],"scale-x":[{"scale-x":K()}],"scale-y":[{"scale-y":K()}],"scale-z":[{"scale-z":K()}],"scale-3d":["scale-3d"],skew:[{skew:ee()}],"skew-x":[{"skew-x":ee()}],"skew-y":[{"skew-y":ee()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:X()}],"translate-x":[{"translate-x":X()}],"translate-y":[{"translate-y":X()}],"translate-z":[{"translate-z":X()}],"translate-none":["translate-none"],accent:[{accent:i()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:i()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":d()}],"scroll-mx":[{"scroll-mx":d()}],"scroll-my":[{"scroll-my":d()}],"scroll-ms":[{"scroll-ms":d()}],"scroll-me":[{"scroll-me":d()}],"scroll-mt":[{"scroll-mt":d()}],"scroll-mr":[{"scroll-mr":d()}],"scroll-mb":[{"scroll-mb":d()}],"scroll-ml":[{"scroll-ml":d()}],"scroll-p":[{"scroll-p":d()}],"scroll-px":[{"scroll-px":d()}],"scroll-py":[{"scroll-py":d()}],"scroll-ps":[{"scroll-ps":d()}],"scroll-pe":[{"scroll-pe":d()}],"scroll-pt":[{"scroll-pt":d()}],"scroll-pr":[{"scroll-pr":d()}],"scroll-pb":[{"scroll-pb":d()}],"scroll-pl":[{"scroll-pl":d()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...i()]}],"stroke-w":[{stroke:[m,_,P,re]}],stroke:[{stroke:["none",...i()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},bo=Ke(fo);function wo(...e){return bo(Ge(e))}export{ho as a,Ge as b,wo as c};
