import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';
import { route } from 'ziggy-js';

const appName = import.meta.env.VITE_APP_NAME || 'Moams';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.jsx`, import.meta.glob('./pages/**/*.jsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);
        try {

            root.render(<App {...props} />);
        } catch (error) {
            console.error('Error rendering Inertia app:', error);
            root.render(
                <div style={{ color: 'red', padding: 20 }}>
                    <h1>App Render Error</h1>
                    <pre>{error && error.toString()}</pre>
                </div>
            );
        }
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on load...
initializeTheme();
