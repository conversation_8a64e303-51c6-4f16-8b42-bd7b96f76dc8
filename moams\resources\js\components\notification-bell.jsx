import React, { useState, useEffect } from 'react';
import { Bell, Check, Trash2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
    DropdownMenuItem,
    DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { router } from '@inertiajs/react';

export default function NotificationBell() {
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [isOpen, setIsOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    // Fetch notifications when component mounts or dropdown opens
    useEffect(() => {
        fetchNotifications();
        // Set up polling for new notifications every 30 seconds
        const interval = setInterval(fetchNotifications, 30000);
        return () => clearInterval(interval);
    }, []);

    const fetchNotifications = async () => {
        try {
            const response = await fetch('/api/notifications/unread');
            const data = await response.json();
            setNotifications(data.notifications || []);
            setUnreadCount(data.count || 0);
        } catch (error) {
            console.error('Failed to fetch notifications:', error);
        }
    };

    const markAsRead = async (notificationId) => {
        try {
            await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                },
            });
            // Update local state
            setNotifications(prev => prev.filter(n => n.id !== notificationId));
            setUnreadCount(prev => Math.max(0, prev - 1));
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    };

    const markAllAsRead = async () => {
        try {
            setLoading(true);
            await fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                },
            });
            setNotifications([]);
            setUnreadCount(0);
        } catch (error) {
            console.error('Failed to mark all notifications as read:', error);
        } finally {
            setLoading(false);
        }
    };

    const deleteNotification = async (notificationId) => {
        try {
            await fetch(`/api/notifications/${notificationId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                },
            });
            // Update local state
            setNotifications(prev => prev.filter(n => n.id !== notificationId));
            setUnreadCount(prev => Math.max(0, prev - 1));
        } catch (error) {
            console.error('Failed to delete notification:', error);
        }
    };

    const getNotificationIcon = (type) => {
        if (type.includes('Payment')) return '💳';
        if (type.includes('Transfer')) return '🚐';
        if (type.includes('Clearance')) return '📋';
        if (type.includes('Fee')) return '💰';
        if (type.includes('Membership')) return '👤';
        return '🔔';
    };

    const getNotificationTitle = (notification) => {
        const data = notification.data;
        if (data.title) return data.title;

        // Fallback based on type
        const type = notification.type;
        if (type.includes('PaymentSuccess')) return 'Payment Successful';
        if (type.includes('PaymentFailure')) return 'Payment Failed';
        if (type.includes('TransferRequest')) return 'Transfer Request';
        if (type.includes('ClearanceRequest')) return 'Clearance Request';
        if (type.includes('FeeChange')) return 'Fee Update';
        if (type.includes('MembershipExpiration')) return 'Membership Reminder';

        return 'Notification';
    };

    const getNotificationMessage = (notification) => {
        const data = notification.data;
        if (data.message) return data.message;

        // Fallback message construction
        if (data.user_name && data.amount) {
            return `${data.user_name} - MK ${data.amount}`;
        }
        if (data.minibus_number_plate) {
            return `Minibus: ${data.minibus_number_plate}`;
        }
        if (data.driver_name) {
            return `Driver: ${data.driver_name}`;
        }

        return 'New notification received';
    };

    const handleNotificationClick = (notification) => {
        // Mark as read when clicked
        markAsRead(notification.id);

        // Navigate based on notification type
        const data = notification.data;
        const type = notification.type;

        if (type.includes('TransferRequest') && data.transfer_request_id) {
            router.visit(`/minibuses/transfer-requests/${data.transfer_request_id}`);
        } else if (type.includes('ClearanceRequest') && data.clearance_request_id) {
            router.visit(`/drivers/clearance-requests/${data.clearance_request_id}`);
        } else if (type.includes('Payment') && data.payment_id) {
            router.visit('/payments');
        } else {
            // Default to dashboard
            router.visit('/dashboard');
        }

        setIsOpen(false);
    };

    return (
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative h-9 w-9">
                    <Bell className="h-5 w-5" />
                    {unreadCount > 0 && (
                        <Badge
                            variant="destructive"
                            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                        >
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
                <div className="flex items-center justify-between p-3 border-b">
                    <h3 className="font-semibold">Notifications</h3>
                    {unreadCount > 0 && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={markAllAsRead}
                            disabled={loading}
                            className="text-xs"
                        >
                            <Check className="h-3 w-3 mr-1" />
                            Mark all read
                        </Button>
                    )}
                </div>

                <div className="max-h-96 overflow-y-auto">
                    {notifications.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">No new notifications</p>
                        </div>
                    ) : (
                        notifications.map((notification) => (
                            <div
                                key={notification.id}
                                className="p-3 border-b hover:bg-gray-50 cursor-pointer group"
                                onClick={() => handleNotificationClick(notification)}
                            >
                                <div className="flex items-start gap-3">
                                    <span className="text-lg flex-shrink-0">
                                        {getNotificationIcon(notification.type)}
                                    </span>
                                    <div className="flex-1 min-w-0">
                                        <p className="font-medium text-sm truncate">
                                            {getNotificationTitle(notification)}
                                        </p>
                                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                            {getNotificationMessage(notification)}
                                        </p>
                                        <p className="text-xs text-gray-400 mt-1">
                                            {notification.created_at}
                                        </p>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            deleteNotification(notification.id);
                                        }}
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>
                        ))
                    )}
                </div>

                {notifications.length > 0 && (
                    <>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="w-full text-xs"
                                onClick={() => {
                                    router.visit('/notifications');
                                    setIsOpen(false);
                                }}
                            >
                                View all notifications
                            </Button>
                        </div>
                    </>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
