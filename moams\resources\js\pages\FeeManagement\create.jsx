import React from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import InputError from '@/components/input-error';
import { ArrowLeft, DollarSign } from 'lucide-react';

export default function CreateFeeSetting() {
    const { data, setData, post, processing, errors } = useForm({
        fee_type: '',
        amount: '',
        description: '',
        effective_from: '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('fee-settings.store'));
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Fee Management', href: '/fee-settings' },
        { title: 'Set New Fee', href: '/fee-settings/create' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Set New Fee" />

            <div className="container mx-auto px-4 py-8">
                <div className="max-w-2xl mx-auto">
                    {/* Header */}
                    <div className="flex items-center space-x-4 mb-6">
                        <Link href={route('fee-settings.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Fee Management
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            Set new fee amount for the association
                        </span>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                Set New Fee
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Fee Type */}
                                <div className="space-y-2">
                                    <Label htmlFor="fee_type">Fee Type *</Label>
                                    <Select
                                        value={data.fee_type}
                                        onValueChange={(value) => setData('fee_type', value)}
                                    >
                                        <SelectTrigger className={errors.fee_type ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select fee type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="registration">Registration Fee</SelectItem>
                                            <SelectItem value="affiliation">Affiliation Fee</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.fee_type} className="mt-2" />
                                </div>

                                {/* Amount */}
                                <div className="space-y-2">
                                    <Label htmlFor="amount">Amount (MK) *</Label>
                                    <Input
                                        id="amount"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.amount}
                                        onChange={(e) => setData('amount', e.target.value)}
                                        placeholder="Enter amount in Malawi Kwacha"
                                        className={errors.amount ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.amount} className="mt-2" />
                                </div>

                                {/* Effective From */}
                                <div className="space-y-2">
                                    <Label htmlFor="effective_from">Effective From *</Label>
                                    <Input
                                        id="effective_from"
                                        type="date"
                                        value={data.effective_from}
                                        onChange={(e) => setData('effective_from', e.target.value)}
                                        min={new Date().toISOString().split('T')[0]}
                                        className={errors.effective_from ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.effective_from} className="mt-2" />
                                    <p className="text-sm text-gray-600">
                                        This fee will become active from the selected date
                                    </p>
                                </div>

                                {/* Description */}
                                <div className="space-y-2">
                                    <Label htmlFor="description">Description (Optional)</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Enter a description for this fee change (e.g., 'Annual fee increase due to economic changes')"
                                        rows={3}
                                        className={errors.description ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.description} className="mt-2" />
                                </div>

                                {/* Submit Buttons */}
                                <div className="flex justify-end space-x-4">
                                    <Link href={route('fee-settings.index')}>
                                        <Button type="button" variant="outline">
                                            Cancel
                                        </Button>
                                    </Link>
                                    <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                        {processing ? 'Setting Fee...' : 'Set Fee'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Information Card */}
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle>Important Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3 text-sm text-gray-600">
                                <div className="flex items-start gap-2">
                                    <span className="text-blue-600 font-semibold">•</span>
                                    <span>Setting a new fee will automatically deactivate the previous fee from the effective date.</span>
                                </div>
                                <div className="flex items-start gap-2">
                                    <span className="text-blue-600 font-semibold">•</span>
                                    <span>Fees cannot be set for past dates to maintain data integrity.</span>
                                </div>
                                <div className="flex items-start gap-2">
                                    <span className="text-blue-600 font-semibold">•</span>
                                    <span>All fee changes are logged with your user account for audit purposes.</span>
                                </div>
                                <div className="flex items-start gap-2">
                                    <span className="text-blue-600 font-semibold">•</span>
                                    <span>Members will be charged the active fee amount when payments are recorded.</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 