import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';

import { Button } from '@/components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Link, usePage, router } from '@inertiajs/react';
import { ChevronLeft, ChevronDown, Search, Filter, User as UserIcon, Plus, ChevronRight } from 'lucide-react';
import Pagination from '@/components/ui/pagination';

export default function MembershipIndex({ minibusOwners }) {
    const { userRoles, flash } = usePage().props;
    const isClerk = userRoles && userRoles.includes('association clerk');
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');

    // Handle both old array format and new paginated format
    const ownersData = minibusOwners?.data || minibusOwners || [];
    const paginationData = minibusOwners?.data ? minibusOwners : null;

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        router.get('/membership-management', {
            search: value,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        router.get('/membership-management', {
            search: searchTerm,
            status: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };
    const breadcrumbs = [
        { title: 'Membership Management', href: '/membership-management' },
    ];
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => window.history.back()}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    Browse all minibus owners and view their membership records.
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {isClerk && (
                                <Link href="/membership-management/create">
                                    <Button className="w-fit flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2">
                                        <Plus className="h-4 w-4" />
                                        Add New Member
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                    {/* Search and Filter section */}
                    <Card className="mb-6 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search & Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="search">Search Members</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="search"
                                            placeholder="Search by name, email..."
                                            value={searchTerm}
                                            onChange={e => handleSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <Label htmlFor="status-filter" className="text-sm mb-1 block">Status</Label>
                                    <select
                                        id="status-filter"
                                        value={statusFilter}
                                        onChange={(e) => handleStatusFilter(e.target.value)}
                                        className="w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white"
                                    >
                                        <option value="all">All Members</option>
                                        <option value="active">Active</option>
                                        <option value="archived">Archived</option>
                                    </select>
                                </div>
                                <div className="flex items-end">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setStatusFilter('all');
                                            router.get('/membership-management');
                                        }}
                                        className="w-full"
                                    >
                                        <Filter className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    {/* Members Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0 shadow-none">
                            <CardHeader className="px-0">
                                <CardTitle className="flex items-center gap-2">
                                    <UserIcon className="h-5 w-5" />
                                    Minibus Owners ({paginationData?.total || ownersData.length})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {ownersData.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <UserIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No minibus owners found</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm || statusFilter !== 'all' ?
                                                "Try adjusting your search or filter criteria." :
                                                "No minibus owners have been registered yet."
                                            }
                                        </p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto mb-8">
                                        <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                            <thead>
                                                <tr className="bg-gray-100 text-gray-700">
                                                    <th className="px-4 py-3 text-left font-medium">Name</th>
                                                    <th className="px-4 py-3 text-left font-medium">Email</th>
                                                    <th className="px-4 py-3 text-left font-medium">Phone</th>
                                                    <th className="px-4 py-3 text-left font-medium">Joined</th>
                                                    <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {ownersData.map(owner => (
                                                    <tr key={owner.id} className="border-b hover:bg-gray-50">
                                                        <td className="px-4 py-3 font-medium">
                                                            {owner.first_name} {owner.last_name}
                                                        </td>
                                                        <td className="px-4 py-3">{owner.email}</td>
                                                        <td className="px-4 py-3">{owner.phone_number}</td>
                                                        <td className="px-4 py-3">
                                                            {owner.joining_date ? new Date(owner.joining_date).toLocaleDateString() : 'N/A'}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <div className="flex items-center gap-2">
                                                                <Link href={`/membership-management/${owner.id}`}>
                                                                    <Button variant="outline" size="sm" className="border-blue-500 text-blue-700 hover:bg-blue-50 hover:border-blue-700">
                                                                        View Memberships
                                                                    </Button>
                                                                </Link>
                                                                {isClerk && (
                                                                    <Link href={`/membership-management/${owner.id}/edit`}>
                                                                        <Button variant="secondary" size="sm" className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border border-indigo-300">
                                                                            Edit Member
                                                                        </Button>
                                                                    </Link>
                                                                )}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                    {/* Pagination */}
                    {paginationData && paginationData.total > 0 && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {paginationData.from} to {paginationData.to} of {paginationData.total} results
                                </div>
                                {paginationData.last_page > 1 && (
                                    <div className="flex items-center justify-center sm:justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(paginationData.prev_page_url)}
                                            disabled={!paginationData.prev_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span className="hidden sm:inline">Previous</span>
                                            <span className="sm:hidden">Prev</span>
                                        </Button>

                                        <span className="text-sm text-gray-600 px-2">
                                            {paginationData.current_page} of {paginationData.last_page}
                                        </span>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(paginationData.next_page_url)}
                                            disabled={!paginationData.next_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <span className="hidden sm:inline">Next</span>
                                            <span className="sm:hidden">Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 