import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import InputError from '../../components/input-error';
import { ChevronLeft, ChevronDown, ChevronRight, CreditCard, User as UserIcon, AlertTriangle, CheckCircle, X } from 'lucide-react';
import { router, usePage, Link } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import ConfirmDialog from '../../components/ui/confirm-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { route } from 'ziggy-js';

const PAID_STATUSES = ['Registered', 'Affiliation fee paid'];
const UNPAID_STATUSES = ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'];

export default function MembershipSummary({ user, memberships, currentFees, flash, filters, unpaidCount }) {
    const { auth, userRoles } = usePage().props;
    const currentUser = auth.user;
    const isClerk = userRoles && userRoles.includes('association clerk');

    // Payment success dialog state
    const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

    // Show payment success notification
    React.useEffect(() => {
        if (flash?.payment_success) {
            setShowPaymentSuccess(true);
        }
    }, [flash]);
    const isMinibusOwner = userRoles && userRoles.includes('minibus owner');
    const isViewingOwnMembership = currentUser.id === user.id;

    const [activeTab, setActiveTab] = useState(filters?.tab || 'all');
    const [paymentModal, setPaymentModal] = useState({ open: false, membership: null });
    const [amount, setAmount] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('cash');
    const [submitting, setSubmitting] = useState(false);
    const [formError, setFormError] = useState(null);
    const [confirmDialog, setConfirmDialog] = useState({ open: false, membership: null });
    const [loading, setLoading] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    // Add a loading state for clerk payment
    const [clerkPayingId, setClerkPayingId] = useState(null);
    // Track individual membership payment states
    const [ctechpayLoadingId, setCtechpayLoadingId] = useState(null);
    const [ctechpayError, setCtechpayError] = useState(null);

    // Set breadcrumbs based on context
    const breadcrumbs = isViewingOwnMembership
        ? [{ title: 'My Membership', href: '/my-membership' }]
        : [
            { title: 'Membership Management', href: '/membership-management' },
            { title: `${user.first_name} ${user.last_name}`, href: `/membership-management/${user.id}` },
        ];

    // Handle tab changes with pagination
    const handleTabChange = (tab) => {
        setActiveTab(tab);
        if (!isViewingOwnMembership) {
            router.get(route('membership.management.summary', user.id), {
                tab: tab,
            }, {
                preserveState: true,
                preserveScroll: true,
            });
        }
    };

    // Get memberships data (now paginated)
    const membershipsData = memberships?.data || [];

    // For warning display, we need to get all unpaid memberships (not just current page)
    // This will need to be calculated on the backend and passed as a separate prop
    const unpaidMemberships = membershipsData.filter(m => UNPAID_STATUSES.includes(m.status));

    // Remove fetch/XHR for current fees
    // const [currentFees, setCurrentFees] = useState({ registration: 0, affiliation: 0 });
    // React.useEffect(() => { ... }, []);

    const handlePaymentClick = (membership) => {
        setPaymentModal({ open: true, membership });
        // Auto-fill amount based on fee type
        const feeAmount = membership.type.toLowerCase() === 'registration' ? currentFees.registration : currentFees.affiliation;
        setAmount(feeAmount.toString());
        setPaymentMethod('cash');
        setFormError(null);
    };

    const handlePaymentSubmit = (e) => {
        e.preventDefault();
        setSubmitting(true);
        setFormError(null);
        router.post(route('makePayment.store'), {
            user_id: user.id,
            membership_id: paymentModal.membership.id,
            fee_type: paymentModal.membership.type.toLowerCase(),
            amount,
            payment_method: paymentMethod,
            status: 'fulfilled',
        }, {
            onSuccess: () => {
                setPaymentModal({ open: false, membership: null });
                setSubmitting(false);
                setAmount('');
                setPaymentMethod('cash');
                setFormError(null);
            },
            onError: (err) => {
                setFormError(err.amount || err.membership_id || err.fee_type || 'Payment failed.');
                setSubmitting(false);
            }
        });
    };

    const handleDelete = (membership) => {
        setConfirmDialog({ open: true, membership });
    };
    const handleCancel = () => setConfirmDialog({ open: false, membership: null });
    const handleConfirm = async () => {
        setLoading(true);
        await router.delete(route('memberships.destroy', confirmDialog.membership.id), {
            onSuccess: () => setConfirmDialog({ open: false, membership: null }),
            onFinish: () => setLoading(false),
        });
    };



    // New function for clerks to mark as paid automatically
    const handleClerkMarkPaid = (membership) => {
        setClerkPayingId(membership.id);
        setFormError(null);
        // Get amount from currentFees
        const feeAmount = membership.type.toLowerCase() === 'registration' ? currentFees.registration : currentFees.affiliation;
        // Set payment method (default to cash)
        const method = 'cash';

        router.post(route('makePayment.store'), {
            user_id: user.id,
            membership_id: membership.id,
            fee_type: membership.type.toLowerCase(),
            amount: feeAmount,
            payment_method: method,
            status: 'fulfilled',
        }, {
            onSuccess: () => {
                setClerkPayingId(null);
            },
            onError: (err) => {
                setFormError(err.amount || err.membership_id || err.fee_type || 'Payment failed.');
                setClerkPayingId(null);
            }
        });
    };

    // Remove modal and handle Ctechpay payment directly
    const handleCtechpayPayment = (membership) => {
        setCtechpayLoadingId(membership.id);

        // Use fetch instead of Inertia router to get the payment URL directly
        console.log('Initiating payment for membership:', membership);

        const paymentUrl = route('payments.ctechpay.initiate');
        console.log('Payment URL:', paymentUrl);
        console.log('CSRF Token:', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));

        fetch(paymentUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: JSON.stringify({
                user_id: user.id,
                membership_id: membership.id,
                fee_type: membership.type.toLowerCase(),
                amount: membership.type.toLowerCase() === 'registration' ? currentFees.registration : currentFees.affiliation,
            }),
        })
            .then(response => {
                console.log('Payment response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Payment response data:', data);
                setCtechpayLoadingId(null);
                if (data.payment_page_url) {
                    console.log('Redirecting to payment page:', data.payment_page_url);
                    // Redirect to Ctechpay payment page
                    window.location.href = data.payment_page_url;
                } else if (data.error) {
                    console.error('Payment error:', data.error);
                    // Set error message for flash display
                    setFormError('Payment initiation failed: ' + data.error);
                } else {
                    console.error('No payment URL received:', data);
                    // Set error message for flash display
                    setFormError('Payment initiation failed: No payment URL received.');
                }
            })
            .catch(error => {
                setCtechpayLoadingId(null);
                console.error('Payment error:', error);
                // Set error message for flash display
                setFormError('Payment initiation failed. Please try again.');
            });
    };

    const tabOptions = [
        { key: 'all', label: `All (${memberships?.total || 0})` },
        { key: 'paid', label: `Paid` },
        { key: 'unpaid', label: `Unpaid` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="max-w-5xl mx-auto p-6">
                {/* Remove local flash error message display; use only global flash */}
                {/* Header section with back button and action buttons */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-4 justify-between">
                    <div className="flex items-center gap-2">
                        <button
                            className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200"
                            onClick={() => navigateToLogicalParent(breadcrumbs)}
                        >
                            <ChevronLeft className="h-4 w-4 mr-1" /> Back
                        </button>
                        <span className="text-muted-foreground text-base font-medium flex items-center gap-2">
                            <UserIcon className="h-5 w-5 text-blue-600" />
                            {isViewingOwnMembership ? 'My Membership' : `${user.first_name} ${user.last_name}`}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        {/* National ID Button */}
                        {user.national_id_path && userRoles && (userRoles.includes('association clerk') || userRoles.includes('system admin')) && (
                            <Button
                                variant="outline"
                                className="bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200"
                                onClick={() => setModalOpen(true)}
                            >
                                View National ID
                            </Button>
                        )}
                    </div>
                </div>

                {/* Commitment Statement Card for minibus owners */}
                {isViewingOwnMembership && (
                    <Card className="mb-6 border-blue-200 bg-blue-50">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-blue-800">
                                <UserIcon className="h-5 w-5" />
                                Membership Commitment
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-blue-700 mb-2">
                                I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all times.
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Warning section for minibus owners with unpaid memberships */}
                {isViewingOwnMembership && unpaidCount > 0 && (
                    <Card className="mb-6 border-orange-200 bg-orange-50">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-orange-800">
                                <AlertTriangle className="h-5 w-5" />
                                Unpaid Memberships
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-orange-700 mb-4">
                                You have {unpaidCount} unpaid membership{unpaidCount > 1 ? 's' : ''}.
                                Please settle your outstanding fees to maintain active membership status.
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Tabs - only show for clerks (not for minibus owners viewing their own) */}
                {!isViewingOwnMembership && (
                    <div className="flex gap-2 mb-8 border-b">
                        {tabOptions.map(tab => (
                            <button
                                key={tab.key}
                                className={`px-4 py-2 -mb-px border-b-2 font-medium transition-colors duration-150 ${activeTab === tab.key ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-blue-600'}`}
                                onClick={() => handleTabChange(tab.key)}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </div>
                )}

                {/* Memberships table */}
                <div className="overflow-x-auto mb-8">
                    <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                        <thead>
                            <tr className="bg-gray-100 text-gray-700">
                                <th className="px-4 py-2 text-left">Year</th>
                                <th className="px-4 py-2 text-left">Type</th>
                                <th className="px-4 py-2 text-left">Status</th>
                                <th className="px-4 py-2 text-left">Start Date</th>
                                <th className="px-4 py-2 text-left">End Date</th>
                                <th className="px-4 py-2 text-left">Fee Obligation</th>
                                <th className="px-4 py-2 text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {membershipsData.length === 0 ? (
                                <tr>
                                    <td colSpan={7} className="text-center text-gray-500 py-6">
                                        {isViewingOwnMembership
                                            ? 'No unpaid memberships found. All your memberships are up to date!'
                                            : 'No memberships found.'}
                                    </td>
                                </tr>
                            ) : (
                                membershipsData.map(m => (
                                    <tr key={m.id} className="border-b hover:bg-gray-50">
                                        <td className="px-4 py-2">{new Date(m.start_date).getFullYear()}</td>
                                        <td className="px-4 py-2 capitalize">{m.type}</td>
                                        <td className="px-4 py-2">
                                            <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${PAID_STATUSES.includes(m.status) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{m.status}</span>
                                        </td>
                                        <td className="px-4 py-2">{new Date(m.start_date).toLocaleDateString()}</td>
                                        <td className="px-4 py-2">{new Date(m.end_date).toLocaleDateString()}</td>
                                        <td className="px-4 py-2">{PAID_STATUSES.includes(m.status) ? 'Fulfilled' : 'Not fulfilled'}</td>
                                        <td className="px-4 py-2 space-x-2">
                                            {UNPAID_STATUSES.includes(m.status) && (
                                                <Button
                                                    size="sm"
                                                    className={`flex items-center justify-center ${isViewingOwnMembership ? 'bg-orange-600 hover:bg-orange-700' : 'bg-blue-400 hover:bg-blue-500'} text-white font-semibold`}
                                                    onClick={() => {
                                                        if (isViewingOwnMembership) {
                                                            handleCtechpayPayment(m);
                                                        } else {
                                                            handleClerkMarkPaid(m);
                                                        }
                                                    }}
                                                    disabled={clerkPayingId === m.id || ctechpayLoadingId === m.id}
                                                >
                                                    <CreditCard className="h-4 w-4 mr-2" />
                                                    {clerkPayingId === m.id ? 'Processing...' : (isViewingOwnMembership ? (ctechpayLoadingId === m.id ? 'Redirecting...' : 'Pay Now') : 'Mark Paid')}
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {memberships && memberships.total > 0 && (
                    <div className="mt-6 w-full">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                            <div className="text-sm text-gray-700 text-center sm:text-left">
                                Showing {memberships.from} to {memberships.to} of {memberships.total} results
                            </div>
                            {memberships.last_page > 1 && (
                                <div className="flex items-center justify-center sm:justify-end space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(memberships.prev_page_url)}
                                        disabled={!memberships.prev_page_url}
                                        className="flex items-center gap-1"
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        <span className="hidden sm:inline">Previous</span>
                                        <span className="sm:hidden">Prev</span>
                                    </Button>

                                    <span className="text-sm text-gray-600 px-2">
                                        {memberships.current_page} of {memberships.last_page}
                                    </span>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(memberships.next_page_url)}
                                        disabled={!memberships.next_page_url}
                                        className="flex items-center gap-1"
                                    >
                                        <span className="hidden sm:inline">Next</span>
                                        <span className="sm:hidden">Next</span>
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* ConfirmDialog for deleting membership */}
                <ConfirmDialog
                    open={confirmDialog.open}
                    title="Delete Membership"
                    description="Are you sure you want to delete this paid membership record? This action cannot be undone."
                    onConfirm={handleConfirm}
                    onCancel={handleCancel}
                    confirmText="Delete"
                    cancelText="Cancel"
                    loading={loading}
                />

                {/* Modal for viewing National ID */}
                <Dialog open={modalOpen} onOpenChange={setModalOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>National ID</DialogTitle>
                            <DialogDescription>
                                {user.national_id_path && user.national_id_path.endsWith('.pdf')
                                    ? 'Preview of the uploaded National ID (PDF).'
                                    : 'Preview of the uploaded National ID (image).'}
                            </DialogDescription>
                        </DialogHeader>
                        {user.national_id_path && user.national_id_path.endsWith('.pdf') ? (
                            <iframe
                                src={`/storage/${user.national_id_path}`}
                                title="National ID PDF"
                                className="w-full h-[70vh] border"
                            />
                        ) : user.national_id_path ? (
                            <img
                                src={`/storage/${user.national_id_path}`}
                                alt="National ID"
                                className="w-full max-h-[70vh] object-contain"
                            />
                        ) : null}
                    </DialogContent>
                </Dialog>

                {/* Payment Success Dialog */}
                <Dialog open={showPaymentSuccess} onOpenChange={setShowPaymentSuccess}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex-shrink-0">
                                    <CheckCircle className="h-8 w-8 text-green-600" />
                                </div>
                                <div>
                                    <DialogTitle className="text-green-700">Payment Successful!</DialogTitle>
                                    <DialogDescription className="text-green-600">
                                        Your payment has been processed successfully
                                    </DialogDescription>
                                </div>
                            </div>
                        </DialogHeader>

                        {flash?.payment_success && (
                            <div className="space-y-4">
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <p className="text-green-800 font-medium mb-3">
                                        {flash.payment_success.message}
                                    </p>

                                    <div className="space-y-2 text-sm text-green-700">
                                        <div className="flex justify-between">
                                            <span className="font-medium">Amount:</span>
                                            <span className="font-mono">MWK {Number(flash.payment_success.amount).toLocaleString()}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Time:</span>
                                            <span>{new Date(flash.payment_success.timestamp).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button
                                        onClick={() => setShowPaymentSuccess(false)}
                                        className="bg-green-600 hover:bg-green-700"
                                    >
                                        <CheckCircle className="h-4 w-4 mr-2" />
                                        Got it!
                                    </Button>
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
} 