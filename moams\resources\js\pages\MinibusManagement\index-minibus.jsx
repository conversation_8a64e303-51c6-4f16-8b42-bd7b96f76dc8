import { Head, <PERSON>, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { Plus, Eye, Search, Bus, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { route } from 'ziggy-js';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import Pagination from '@/components/ui/pagination';

export default function MinibusManagement({ minibuses, transferRequests, userRole, auth }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');

    const [confirmDialog, setConfirmDialog] = useState({ open: false, minibus: null });
    const [loading, setLoading] = useState(false);

    // Handle both old array format and new paginated format
    const minibusesData = minibuses?.data || minibuses || [];
    const paginationData = minibuses?.data ? minibuses : null;

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
    ];

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        router.get('/minibuses', {
            search: value,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        router.get('/minibuses', {
            search: searchTerm,
            status: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };



    const handleDelete = (minibus) => {
        setConfirmDialog({ open: true, minibus });
    };
    const handleCancel = () => setConfirmDialog({ open: false, minibus: null });
    const handleConfirm = async () => {
        setLoading(true);
        await router.delete(route('minibuses.destroy', confirmDialog.minibus.id));
        setLoading(false);
        setConfirmDialog({ open: false, minibus: null });
    };
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    {userRole === 'minibus owner'
                                        ? 'View and manage your registered minibuses.'
                                        : 'View and manage all registered minibuses.'
                                    }
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {userRole === 'association clerk' && (
                                <Button
                                    variant="outline"
                                    onClick={() => router.visit(route('minibuses.transfer-requests.index'))}
                                    className="w-fit flex items-center gap-2"
                                >
                                    <span className="hidden sm:inline">Pending Transfer Requests</span>
                                    <span className="sm:hidden">Pending</span>
                                    {transferRequests?.filter(req => req.status === 'pending').length > 0 && (
                                        <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 font-bold">
                                            {transferRequests.filter(req => req.status === 'pending').length}
                                        </Badge>
                                    )}
                                </Button>
                            )}
                            {(userRole === 'association clerk' || userRole === 'minibus owner') && (
                                <Link href={route('minibuses.create')}>
                                    <Button className="w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2">
                                        <Plus className="h-4 w-4" />
                                        Add New Minibus
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>




                    {/* Search & Filters section */}
                    <Card className="mb-6 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search & Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="search">Search Minibuses</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="search"
                                            placeholder="Search by plate, route..."
                                            value={searchTerm}
                                            onChange={(e) => handleSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                {userRole === 'association clerk' && (
                                    <div>
                                        <Label htmlFor="status-filter" className="text-sm mb-1 block">Status</Label>
                                        <select
                                            id="status-filter"
                                            value={statusFilter}
                                            onChange={(e) => handleStatusFilter(e.target.value)}
                                            className="w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white"
                                        >
                                            <option value="all">All Minibuses</option>
                                            <option value="active">Active</option>
                                            <option value="archived">Archived</option>
                                        </select>
                                    </div>
                                )}
                                <div className="flex items-end">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setStatusFilter('all');
                                            router.get('/minibuses');
                                        }}
                                        className="w-full"
                                    >
                                        <Filter className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Minibus Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0 shadow-none">
                            <CardHeader className="px-0">
                                <CardTitle className="flex items-center gap-2">
                                    <Bus className="h-5 w-5" />
                                    {userRole === 'minibus owner' ? 'My Minibuses' : 'Minibuses'} ({paginationData?.total || minibusesData.length})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {minibusesData.length > 0 ? (
                                    <div className="overflow-x-auto mb-8">
                                        <TooltipProvider>
                                            <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                                <thead>
                                                    <tr className="bg-gray-100 text-gray-700">
                                                        <th className="px-4 py-3 text-left font-medium">Number Plate</th>
                                                        <th className="px-4 py-3 text-left font-medium">Make</th>
                                                        <th className="px-4 py-3 text-left font-medium">Model</th>
                                                        <th className="px-4 py-3 text-left font-medium">Main Colour</th>
                                                        <th className="px-4 py-3 text-left font-medium">Status</th>
                                                        <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {minibusesData.map((minibus) => (
                                                        <tr key={minibus.id} className="border-b hover:bg-gray-50 text-sm">
                                                            <td className="font-medium px-4 py-3">
                                                                <div className="truncate max-w-[100px] sm:max-w-none">
                                                                    {minibus.number_plate}
                                                                </div>
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="truncate max-w-[80px] sm:max-w-none">
                                                                    {minibus.make || '-'}
                                                                </div>
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="truncate max-w-[80px] sm:max-w-none">
                                                                    {minibus.model || '-'}
                                                                </div>
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="truncate max-w-[80px] sm:max-w-none">
                                                                    {minibus.main_colour || '-'}
                                                                </div>
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                {minibus.archived ? (
                                                                    <Badge variant="destructive" className="text-sm">Archived</Badge>
                                                                ) : (
                                                                    <Badge className="bg-green-100 text-green-800 text-sm">Active</Badge>
                                                                )}
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="flex items-center gap-1 sm:gap-2">
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Link href={route('minibuses.show', minibus.id)}>
                                                                                <Button variant="outline" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                                                                    <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                </Button>
                                                                            </Link>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>View Details</TooltipContent>
                                                                    </Tooltip>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </TooltipProvider>
                                    </div>
                                ) : (
                                    <div className="text-center py-8 px-6">
                                        <Bus className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No minibuses found</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm || filterTransferType ?
                                                "Try adjusting your search or filters" :
                                                "No minibuses have been registered yet"}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                    {/* Pagination */}
                    {paginationData && paginationData.total > 0 && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {paginationData.from} to {paginationData.to} of {paginationData.total} results
                                </div>
                                {paginationData.last_page > 1 && (
                                    <div className="flex items-center justify-center sm:justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(paginationData.prev_page_url)}
                                            disabled={!paginationData.prev_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span className="hidden sm:inline">Previous</span>
                                            <span className="sm:hidden">Prev</span>
                                        </Button>

                                        <span className="text-sm text-gray-600 px-2">
                                            {paginationData.current_page} of {paginationData.last_page}
                                        </span>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(paginationData.next_page_url)}
                                            disabled={!paginationData.next_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <span className="hidden sm:inline">Next</span>
                                            <span className="sm:hidden">Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <ConfirmDialog
                open={confirmDialog.open}
                title={confirmDialog.minibus ? `Delete minibus ${confirmDialog.minibus.number_plate}?` : 'Delete minibus?'}
                description="This action cannot be undone and will permanently remove the minibus."
                confirmText="Delete"
                confirmVariant="destructive"
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout >
    );
}
