import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Trash2 } from 'lucide-react';
import { route } from 'ziggy-js';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

export default function ShowTransferRequest({ transferRequest, breadcrumbs }) {
    if (!transferRequest) {
        return <div>Transfer request not found.</div>;
    }
    const { minibus, owner, transfer_type, status, reason, created_at, id, ownership_transfer_certificate } = transferRequest;

    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);

    const handleDelete = () => {
        setConfirmDialogOpen(true);
    };
    const handleCancel = () => setConfirmDialogOpen(false);
    const handleConfirm = async () => {
        setLoading(true);
        await router.delete(route('minibuses.transfer-requests.destroy', id), {
            onSuccess: () => router.visit('/minibuses/transfer-requests')
        });
        setLoading(false);
        setConfirmDialogOpen(false);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="container mx-auto px-2 sm:px-4 py-6 sm:py-8">
                {/* Page title above grid */}
                <h1 className="text-2xl font-bold mb-1">Minibus Transfer Request Details</h1>
                {/* Back button and subtitle in a flex row */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 flex-wrap gap-2">
                    <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                    <p className="text-muted-foreground flex-1 min-w-0 mt-2 sm:mt-0 sm:ml-4">View and manage this minibus transfer request.</p>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Minibus & Transfer Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Minibus & Transfer Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Minibus</label>
                                        <p className="text-lg font-mono">{minibus ? minibus.number_plate : 'N/A'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Transfer Type</label>
                                        <div className="flex items-center gap-2 mt-1">
                                            {transfer_type === 'internal' ? (
                                                <Badge className="bg-green-100 text-green-800 border-green-200 border border-green-600">{transfer_type}</Badge>
                                            ) : (
                                                <Badge className="bg-red-100 text-red-800 border-red-200 border border-red-600">{transfer_type}</Badge>
                                            )}
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Status</label>
                                        <div className="flex items-center gap-2 mt-1">
                                            {status === 'pending' ? (
                                                <Badge variant="outline" className="text-amber-600 border-amber-600">Pending</Badge>
                                            ) : status === 'transferred' ? (
                                                <Badge variant="default" className="bg-green-600">Transferred</Badge>
                                            ) : (
                                                <Badge variant="secondary">{status}</Badge>
                                            )}
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Request Date</label>
                                        <p className="text-lg">{created_at ? new Date(created_at).toLocaleString() : 'N/A'}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        {/* Owner Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Owner Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Owner Name</label>
                                        <p className="text-lg font-medium">{owner ? `${owner.first_name} ${owner.last_name}` : 'N/A'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Phone Number</label>
                                        <p className="text-lg">{owner && owner.phone_number ? owner.phone_number : 'N/A'}</p>
                                    </div>
                                </div>
                                {owner && owner.email && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Email</label>
                                        <p className="text-lg">{owner.email}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                        {/* Reason Card */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Reason for Transfer</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="break-all whitespace-pre-line max-w-full text-base">{reason}</div>
                            </CardContent>
                        </Card>
                    </div>
                    {/* Sidebar: Quick Actions */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {minibus && (
                                    <Button
                                        className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white"
                                        onClick={() => router.visit(route('minibuses.transfer', minibus.id))}
                                    >
                                        Process Request
                                    </Button>
                                )}
                                {ownership_transfer_certificate && (
                                    <Button
                                        variant="outline"
                                        className="w-full justify-start bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200"
                                        onClick={() => setModalOpen(true)}
                                    >
                                        View Transfer Certificate
                                    </Button>
                                )}
                                <Button
                                    variant="destructive"
                                    className="w-full justify-start"
                                    onClick={handleDelete}
                                >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete Request
                                </Button>
                            </CardContent>
                        </Card>
                        {/* Modal for viewing Transfer Certificate */}
                        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
                            <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                    <DialogTitle>Ownership Transfer Certificate</DialogTitle>
                                    <DialogDescription>
                                        {ownership_transfer_certificate && ownership_transfer_certificate.endsWith('.pdf')
                                            ? 'Preview of the uploaded Transfer Certificate (PDF).'
                                            : 'Preview of the uploaded Transfer Certificate (image).'}
                                    </DialogDescription>
                                </DialogHeader>
                                {ownership_transfer_certificate && ownership_transfer_certificate.endsWith('.pdf') ? (
                                    <iframe
                                        src={`/storage/${ownership_transfer_certificate}`}
                                        title="Transfer Certificate PDF"
                                        className="w-full h-[70vh] border"
                                    />
                                ) : ownership_transfer_certificate ? (
                                    <img
                                        src={`/storage/${ownership_transfer_certificate}`}
                                        alt="Transfer Certificate"
                                        className="w-full max-h-[70vh] object-contain"
                                    />
                                ) : null}
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
            </div>
            <ConfirmDialog
                open={confirmDialogOpen}
                title="Delete Transfer Request?"
                description="This action cannot be undone and will permanently remove the transfer request."
                confirmText="Delete"
                confirmVariant="destructive"
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout>
    );
} 