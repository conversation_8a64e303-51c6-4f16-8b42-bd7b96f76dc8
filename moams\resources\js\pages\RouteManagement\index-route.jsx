import { Head, Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { Plus, Search, Route, Filter, ChevronDown, ChevronLeft, ChevronRight, Power, PowerOff, AlertTriangle, FileText } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { route } from 'ziggy-js';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import Pagination from '@/components/ui/pagination';

export default function RouteManagement({ routes = [], auth = {} }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [createModalOpen, setCreateModalOpen] = useState(false);
    const [confirmDialog, setConfirmDialog] = useState({ open: false, route: null, action: null });
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({ name: '', status: 'active' });
    const [formErrors, setFormErrors] = useState({});

    // Handle both old array format and new paginated format
    const routesData = routes?.data || routes || [];
    const paginationData = routes?.data ? routes : null;

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Route Management', href: '/routes' },
        { title: 'Lagos-Ibadan Route', href: '/routes/1' },
        { title: 'Edit Route Details', href: '/routes/1/edit' },
        { title: 'Current Page', href: '/routes' },
    ];

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        router.get('/routes', {
            search: value,
            status: statusFilter,
            sort: sortBy,
            direction: sortDirection,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        router.get('/routes', {
            search: searchTerm,
            status: value,
            sort: sortBy,
            direction: sortDirection,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSort = (field) => {
        const newDirection = sortBy === field && sortDirection === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortDirection(newDirection);
        router.get('/routes', {
            search: searchTerm,
            status: statusFilter,
            sort: field,
            direction: newDirection,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleToggleStatus = (route) => {
        const newStatus = route.status === 'active' ? 'inactive' : 'active';
        const action = newStatus === 'active' ? 'activate' : 'deactivate';

        setConfirmDialog({
            open: true,
            route,
            action,
            title: `${action.charAt(0).toUpperCase() + action.slice(1)} Route`,
            description: action === 'deactivate' && route.minibuses_count > 0
                ? `Warning: This route is assigned to ${route.minibuses_count} active minibus(es). Deactivating it may affect operations.`
                : `Are you sure you want to ${action} this route?`
        });
    };

    const handleConfirmAction = async () => {
        setLoading(true);
        const { route: routeItem, action } = confirmDialog;

        try {
            switch (action) {
                case 'activate':
                    await router.post(route('routes.activate', routeItem.id));
                    break;
                case 'deactivate':
                    await router.post(route('routes.deactivate', routeItem.id));
                    break;
            }
            setConfirmDialog({ open: false, route: null, action: null });
        } catch (error) {
            console.error(`Error ${action}ing route:`, error);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateRoute = () => {
        setFormData({ name: '', status: 'active' });
        setFormErrors({});
        setCreateModalOpen(true);
    };

    const handleSubmitCreate = async () => {
        setLoading(true);
        setFormErrors({});

        try {
            await router.post(route('routes.store'), formData, {
                onError: (errors) => {
                    setFormErrors(errors);
                },
                onSuccess: () => {
                    setCreateModalOpen(false);
                    setFormData({ name: '', status: 'active' });
                }
            });
        } catch (error) {
            console.error('Error creating route:', error);
        } finally {
            setLoading(false);
        }
    };

    try {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Route Management" />

                <div className="w-full max-w-full overflow-hidden">
                    <div className="container mx-auto px-4 py-8">

                        {/* Header section */}
                        <div className="flex flex-col gap-4 mb-6">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                                <button
                                    className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                    onClick={() => navigateToLogicalParent(breadcrumbs)}
                                >
                                    <ChevronLeft className="h-4 w-4 mr-1" /> Back
                                </button>
                                <div className="flex-1 sm:flex-none">
                                    <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                        Manage minibus routes and monitor usage patterns.
                                    </span>
                                </div>
                            </div>

                            {/* Action buttons row - Create Route and Export on same line */}
                            <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                                {/* Only show Add Route button for Association Clerks */}
                                {auth.user && auth.user.roles && auth.user.roles.includes('association clerk') && (
                                    <Button
                                        onClick={() => setCreateModalOpen(true)}
                                        className="w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                                    >
                                        <Plus className="h-4 w-4" />
                                        Add New Route
                                    </Button>
                                )}
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        // PDF Export functionality
                                        window.open('/routes/export?format=pdf', '_blank');
                                    }}
                                    className="w-fit border-green-500 text-green-700 hover:bg-green-50 px-4 py-2"
                                >
                                    <FileText className="h-4 w-4 mr-2" />
                                    <span className="hidden sm:inline">Export Routes Analysis PDF</span>
                                    <span className="sm:hidden">Export PDF</span>
                                </Button>
                            </div>
                        </div>

                        {/* Search and Filter section */}
                        <Card className="mb-6 w-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Filter className="h-5 w-5" />
                                    Search & Filter
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="search">Search Routes</Label>
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="search"
                                                placeholder="Search by route name..."
                                                value={searchTerm}
                                                onChange={(e) => handleSearch(e.target.value)}
                                                className="pl-10"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <Label htmlFor="status-filter">Filter by Status</Label>
                                        <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All statuses" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Statuses</SelectItem>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="sort-by">Sort By</Label>
                                        <Select value={`${sortBy}-${sortDirection}`} onValueChange={(value) => {
                                            const [column, direction] = value.split('-');
                                            handleSort(column);
                                        }}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                                                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                                                <SelectItem value="minibuses_count-desc">Most Popular</SelectItem>
                                                <SelectItem value="minibuses_count-asc">Least Popular</SelectItem>
                                                <SelectItem value="status-asc">Status (Active First)</SelectItem>
                                                <SelectItem value="status-desc">Status (Inactive First)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Routes Table Container - Fixed width that fits screen */}
                        <div
                            className="w-full border-2 border-blue-200 rounded-lg"
                            style={{
                                maxWidth: 'calc(100vw - 2rem)',
                                overflow: 'hidden'
                            }}
                        >
                            <Card className="w-full border-0">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Route className="h-5 w-5" />
                                        Routes ({paginationData?.total || routesData.length})
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-0">
                                    {routesData.length === 0 ? (
                                        <div className="text-center py-8 px-6">
                                            <Route className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                            <h3 className="text-lg font-medium mb-2">No routes found</h3>
                                            <p className="text-muted-foreground mb-4">
                                                {searchTerm || statusFilter !== 'all'
                                                    ? 'Try adjusting your search or filter criteria.'
                                                    : 'No routes available.'
                                                }
                                            </p>
                                        </div>
                                    ) : (
                                        <div className="overflow-x-auto">
                                            <Table className="w-full min-w-[600px]">
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead
                                                            className="cursor-pointer hover:bg-muted/50"
                                                            onClick={() => handleSort('name')}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                Route Name
                                                                {sortBy === 'name' && (
                                                                    <ChevronDown className={`h-4 w-4 transition-transform ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                                                                )}
                                                            </div>
                                                        </TableHead>
                                                        <TableHead
                                                            className="cursor-pointer hover:bg-muted/50"
                                                            onClick={() => handleSort('status')}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                Status
                                                                {sortBy === 'status' && (
                                                                    <ChevronDown className={`h-4 w-4 transition-transform ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                                                                )}
                                                            </div>
                                                        </TableHead>
                                                        <TableHead
                                                            className="cursor-pointer hover:bg-muted/50"
                                                            onClick={() => handleSort('minibuses_count')}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                Usage Count
                                                                {sortBy === 'minibuses_count' && (
                                                                    <ChevronDown className={`h-4 w-4 transition-transform ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                                                                )}
                                                            </div>
                                                        </TableHead>
                                                        <TableHead>Actions</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {routesData.map((routeItem) => (
                                                        <TableRow key={routeItem.id}>
                                                            <TableCell className="font-medium">
                                                                {routeItem.name}
                                                            </TableCell>
                                                            <TableCell>
                                                                <Badge
                                                                    variant={routeItem.status === 'active' ? 'default' : 'secondary'}
                                                                    className={routeItem.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                                                                >
                                                                    {routeItem.status === 'active' ? 'Active' : 'Inactive'}
                                                                </Badge>
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-2">
                                                                    <span className="font-medium">{routeItem.minibuses_count || 0}</span>
                                                                    <span className="text-muted-foreground text-sm">
                                                                        minibus{(routeItem.minibuses_count || 0) !== 1 ? 'es' : ''}
                                                                    </span>
                                                                    {(routeItem.minibuses_count || 0) > 0 && (
                                                                        <TooltipProvider>
                                                                            <Tooltip>
                                                                                <TooltipTrigger>
                                                                                    <AlertTriangle className="h-4 w-4 text-amber-500" />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    This route is assigned to active minibuses
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        </TooltipProvider>
                                                                    )}
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                <TooltipProvider>
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => handleToggleStatus(routeItem)}
                                                                                style={routeItem.status === 'active'
                                                                                    ? {
                                                                                        borderColor: '#ea580c',
                                                                                        color: '#ea580c',
                                                                                        backgroundColor: 'transparent'
                                                                                    }
                                                                                    : {
                                                                                        borderColor: '#16a34a',
                                                                                        color: '#16a34a',
                                                                                        backgroundColor: 'transparent'
                                                                                    }
                                                                                }
                                                                                onMouseEnter={(e) => {
                                                                                    if (routeItem.status === 'active') {
                                                                                        e.target.style.backgroundColor = '#fff7ed';
                                                                                    } else {
                                                                                        e.target.style.backgroundColor = '#f0fdf4';
                                                                                    }
                                                                                }}
                                                                                onMouseLeave={(e) => {
                                                                                    e.target.style.backgroundColor = 'transparent';
                                                                                }}
                                                                            >
                                                                                {routeItem.status === 'active' ?
                                                                                    <PowerOff className="h-4 w-4" /> :
                                                                                    <Power className="h-4 w-4" />
                                                                                }
                                                                            </Button>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            {routeItem.status === 'active' ? 'Deactivate' : 'Activate'} route
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                </TooltipProvider>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Pagination */}
                        {paginationData && paginationData.total > 0 && (
                            <div className="mt-6 w-full">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                    <div className="text-sm text-gray-700 text-center sm:text-left">
                                        Showing {paginationData.from} to {paginationData.to} of {paginationData.total} results
                                    </div>
                                    {paginationData.last_page > 1 && (
                                        <div className="flex items-center justify-center sm:justify-end space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => router.get(paginationData.prev_page_url)}
                                                disabled={!paginationData.prev_page_url}
                                                className="flex items-center gap-1"
                                            >
                                                <ChevronLeft className="h-4 w-4" />
                                                <span className="hidden sm:inline">Previous</span>
                                                <span className="sm:hidden">Prev</span>
                                            </Button>

                                            <span className="text-sm text-gray-600 px-2">
                                                Page {paginationData.current_page} of {paginationData.last_page}
                                            </span>

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => router.get(paginationData.next_page_url)}
                                                disabled={!paginationData.next_page_url}
                                                className="flex items-center gap-1"
                                            >
                                                <span className="hidden sm:inline">Next</span>
                                                <span className="sm:hidden">Next</span>
                                                <ChevronRight className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}


                        {/* Create Route Modal */}
                        <Dialog open={createModalOpen} onOpenChange={setCreateModalOpen}>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Add New Route</DialogTitle>
                                    <DialogDescription>
                                        Create a new minibus route. Route names must follow the pattern "Location - Location" (e.g., "Ntcheu - Chingeni").
                                    </DialogDescription>
                                </DialogHeader>
                                <form onSubmit={(e) => {
                                    e.preventDefault();
                                    handleSubmitCreate();
                                }}>
                                    <div className="space-y-4">
                                        <div>
                                            <Label htmlFor="route-name">Route Name</Label>
                                            <Input
                                                id="route-name"
                                                placeholder="e.g., Ntcheu - Chingeni"
                                                value={formData.name}
                                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                                className={formErrors.name ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                Format: "Location - Location" with exactly one space before and after the dash
                                            </p>
                                            {formErrors.name && (
                                                <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="route-status">Status</Label>
                                            <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="active">Active</SelectItem>
                                                    <SelectItem value="inactive">Inactive</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                    <DialogFooter className="mt-6">
                                        <Button type="button" variant="outline" onClick={() => setCreateModalOpen(false)}>
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700 text-white">
                                            {loading ? 'Creating...' : 'Create Route'}
                                        </Button>
                                    </DialogFooter>
                                </form>
                            </DialogContent>
                        </Dialog>

                        {/* Confirmation Dialog */}
                        <Dialog open={confirmDialog.open} onOpenChange={(open) => { if (!open) setConfirmDialog({ open: false, route: null, action: null }); }}>
                            <DialogContent showCloseButton={false}>
                                <DialogHeader>
                                    <DialogTitle>{confirmDialog.title}</DialogTitle>
                                    <DialogDescription>{confirmDialog.description}</DialogDescription>
                                </DialogHeader>
                                <DialogFooter>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setConfirmDialog({ open: false, route: null, action: null })}
                                        disabled={loading}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={handleConfirmAction}
                                        disabled={loading}
                                    >
                                        {loading ? 'Loading...' : (confirmDialog.action === 'activate' ? 'Activate' : 'Deactivate')}
                                    </Button>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
            </AppLayout>
        );
    } catch (error) {
        console.error('RouteManagement render error:', error);
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Route Management" />
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Routes</h1>
                        <p className="text-gray-600 mb-4">Error: {error.message}</p>
                        <Button onClick={() => window.location.reload()}>Reload Page</Button>
                    </div>
                </div>
            </AppLayout>
        );
    }
}
