import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';

export default function ErrorPage({ status = 500, title = 'Something went wrong', description = 'An unexpected error occurred. Please try again later or contact support if the problem persists.' }) {
    return (
        <AppLayout>
            <Head title={title} />
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                <Card className="max-w-lg w-full text-center">
                    <CardHeader>
                        <CardTitle className="text-4xl font-bold text-red-600 mb-2">{status}</CardTitle>
                        <h2 className="text-2xl font-semibold mb-2">{title}</h2>
                    </CardHeader>
                    <CardContent>
                        <p className="text-gray-600 mb-6">{description}</p>
                        <Link href="/dashboard">
                            <Button className="bg-blue-600 hover:bg-blue-700">Back to Dashboard</Button>
                        </Link>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
} 