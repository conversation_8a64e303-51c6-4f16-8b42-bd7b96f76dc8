import { Head, Link, useForm } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ChevronLeft, AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { usePage } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function CreateMisconduct({ drivers = [] }) {
    const { userRoles } = usePage().props;
    const [selectedDriver, setSelectedDriver] = useState(null);

    // Form for creating misconduct
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        description: '',
        offense_date: '',
        severity: 'medium',
        evidence_file: null,
        offender_type: 'driver',
        offender_id: '',
    });

    const misconductTypes = [
        'Reckless Driving',
        'Speeding',
        'Overloading',
        'Route Deviation',
        'Poor Customer Service',
        'Vehicle Maintenance Issues',
        'Unauthorized Stops',
        'Inappropriate Behavior',
        'Operating Without Valid License',
        'Operating Outside Designated Hours',
        'Unauthorized Fare Increase',
        'Other'
    ];

    const breadcrumbs = [
        { title: 'Misconduct Management', href: route('misconducts.index') },
        { title: 'Report Misconduct', href: route('misconducts.create') },
    ];

    const handleDriverChange = (driver) => {
        setSelectedDriver(driver);
        setData('offender_id', driver?.id || '');
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/misconducts', {
            forceFormData: true,
            onSuccess: () => {
                reset();
                setSelectedDriver(null);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Report Driver Misconduct" />

            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    Report a misconduct incident for one of your drivers
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Main Form Card */}
                    <Card className="max-w-4xl mx-auto">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-red-600">
                                <AlertTriangle className="h-6 w-6" />
                                Report Driver Misconduct
                            </CardTitle>
                            <p className="text-gray-600 mt-2">
                                Please provide detailed information about the misconduct incident. This will affect the driver's trust score and may result in disciplinary action.
                            </p>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Driver Selection */}
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div className="lg:col-span-2">
                                        <Label htmlFor="driver-select" className="text-base font-medium">
                                            Select Driver *
                                        </Label>
                                        <Select
                                            value={selectedDriver?.id?.toString() || ''}
                                            onValueChange={(value) => {
                                                const driver = drivers.find(d => d.id.toString() === value);
                                                handleDriverChange(driver);
                                            }}
                                        >
                                            <SelectTrigger className={`mt-2 ${errors.offender_id ? 'border-red-500' : ''}`}>
                                                <SelectValue placeholder="Choose a driver..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {drivers.map((driver) => (
                                                    <SelectItem key={driver.id} value={driver.id.toString()}>
                                                        <div className="flex flex-col">
                                                            <span className="font-medium">
                                                                {driver.first_name} {driver.last_name}
                                                            </span>
                                                            <span className="text-sm text-gray-500">
                                                                {driver.phone_number}
                                                                {driver.minibus && ` • ${driver.minibus.plate_number}`}
                                                            </span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.offender_id && (
                                            <p className="text-sm text-red-500 mt-1">{errors.offender_id}</p>
                                        )}
                                    </div>

                                    {/* Misconduct Type */}
                                    <div>
                                        <Label htmlFor="misconduct-type" className="text-base font-medium">
                                            Misconduct Type *
                                        </Label>
                                        <Select value={data.name} onValueChange={(value) => setData('name', value)}>
                                            <SelectTrigger className={`mt-2 ${errors.name ? 'border-red-500' : ''}`}>
                                                <SelectValue placeholder="Select misconduct type..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {misconductTypes.map((type) => (
                                                    <SelectItem key={type} value={type}>
                                                        {type}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.name && (
                                            <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                                        )}
                                    </div>

                                    {/* Offense Date */}
                                    <div>
                                        <Label htmlFor="offense-date" className="text-base font-medium">
                                            Offense Date *
                                        </Label>
                                        <Input
                                            id="offense-date"
                                            type="date"
                                            value={data.offense_date}
                                            onChange={(e) => setData('offense_date', e.target.value)}
                                            className={`mt-2 ${errors.offense_date ? 'border-red-500' : ''}`}
                                            max={new Date().toISOString().split('T')[0]}
                                        />
                                        {errors.offense_date && (
                                            <p className="text-sm text-red-500 mt-1">{errors.offense_date}</p>
                                        )}
                                    </div>

                                    {/* Severity */}
                                    <div>
                                        <Label htmlFor="severity" className="text-base font-medium">
                                            Severity Level *
                                        </Label>
                                        <Select value={data.severity} onValueChange={(value) => setData('severity', value)}>
                                            <SelectTrigger className={`mt-2 ${errors.severity ? 'border-red-500' : ''}`}>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="low">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">Low</span>
                                                        <span className="text-sm text-gray-500">Minor infraction</span>
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="medium">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">Medium</span>
                                                        <span className="text-sm text-gray-500">Moderate concern</span>
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="high">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">High</span>
                                                        <span className="text-sm text-gray-500">Serious violation</span>
                                                    </div>
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.severity && (
                                            <p className="text-sm text-red-500 mt-1">{errors.severity}</p>
                                        )}
                                    </div>

                                    {/* Evidence File */}
                                    <div>
                                        <Label htmlFor="evidence-file" className="text-base font-medium">
                                            Evidence File (Optional)
                                        </Label>
                                        <div className="mt-2">
                                            <Input
                                                id="evidence-file"
                                                type="file"
                                                onChange={(e) => setData('evidence_file', e.target.files[0])}
                                                className={errors.evidence_file ? 'border-red-500' : ''}
                                                accept=".pdf,.jpg,.jpeg,.png"
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                Accepted formats: Images (JPG, JPEG, PNG) and PDF files only (Max: 5MB)
                                            </p>
                                        </div>
                                        {errors.evidence_file && (
                                            <p className="text-sm text-red-500 mt-1">{errors.evidence_file}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Description */}
                                <div>
                                    <Label htmlFor="description" className="text-base font-medium">
                                        Description
                                    </Label>
                                    <Textarea
                                        id="description"
                                        placeholder="Provide detailed information about the misconduct incident, including what happened, when, where, and any other relevant details..."
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        className={`mt-2 min-h-[120px] ${errors.description ? 'border-red-500' : ''}`}
                                        rows={5}
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-red-500 mt-1">{errors.description}</p>
                                    )}
                                </div>

                                {/* Form Actions */}
                                <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => window.history.back()}
                                        className="sm:w-auto"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="bg-red-600 hover:bg-red-700 text-white sm:w-auto"
                                    >
                                        {processing ? 'Reporting...' : 'Report Misconduct'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
