import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';

export default function CtechpayResult({ success, message, payment, ctechpay_status, ctechpay_response, verification_needed, order_reference, error_type }) {
    const getIcon = () => {
        if (success === true) return <CheckCircle className="h-12 w-12 text-green-600 mb-2" />;
        if (success === false) return <AlertTriangle className="h-12 w-12 text-red-600 mb-2" />;
        return <Clock className="h-12 w-12 text-yellow-600 mb-2" />; // verification needed
    };

    const getTitle = () => {
        if (success === true) return 'Payment Successful';
        if (success === false) return 'Payment Failed';
        return 'Payment Verification Needed';
    };

    const getTitleColor = () => {
        if (success === true) return 'text-green-700';
        if (success === false) return 'text-red-700';
        return 'text-yellow-700';
    };

    return (
        <AppLayout>
            <Head title="Payment Result" />
            <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12">
                <Card className="max-w-md w-full shadow-lg">
                    <CardHeader className="flex flex-col items-center">
                        {getIcon()}
                        <CardTitle className={getTitleColor()}>
                            {getTitle()}
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="text-center text-gray-700">{message}</div>

                        {verification_needed && (
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <div className="text-sm text-yellow-800">
                                    <div className="font-semibold mb-2">What to do next:</div>
                                    <ul className="list-disc list-inside space-y-1">
                                        <li>Save your order reference: <code className="bg-yellow-100 px-1 rounded">{order_reference}</code></li>
                                        <li>Contact support to verify your payment</li>
                                        <li>Check your bank statement for the transaction</li>
                                    </ul>
                                </div>
                            </div>
                        )}

                        {payment && (
                            <div className="text-sm text-gray-600">
                                <div><b>Amount:</b> {payment.amount}</div>
                                <div><b>Status:</b> {payment.status}</div>
                                <div><b>Order Ref:</b> {payment.order_reference || order_reference}</div>
                            </div>
                        )}

                        {ctechpay_status && (
                            <div className="text-xs text-gray-400">Ctechpay Status: {ctechpay_status}</div>
                        )}

                        {error_type === 'connection_timeout' && (
                            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                                Technical note: Connection timeout to payment gateway
                            </div>
                        )}

                        <div className="flex justify-center gap-3 mt-6">
                            {success === false && (
                                <Button
                                    onClick={() => {
                                        // Go back to membership page to retry payment
                                        window.location.href = '/my-membership?t=' + Date.now();
                                    }}
                                    className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 font-medium"
                                >
                                    Retry Payment
                                </Button>
                            )}
                            {success === true && (
                                <Button
                                    onClick={() => {
                                        // Force a complete page reload to bypass any caching
                                        window.location.href = '/my-membership?t=' + Date.now();
                                    }}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 font-medium"
                                >
                                    Back to My Membership
                                </Button>
                            )}
                            <Button
                                variant="outline"
                                onClick={() => router.visit('/dashboard')}
                                className="border-gray-400 text-gray-600 hover:bg-gray-50 px-6 py-2 font-medium"
                            >
                                Go to Dashboard
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
} 