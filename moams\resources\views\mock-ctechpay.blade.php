<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Ctechpay Payment Gateway</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .payment-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .order-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 120px;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        .btn-danger:hover {
            background-color: #dc2626;
        }
        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }
        .btn-warning:hover {
            background-color: #d97706;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="header">
            <div class="logo">🏦 Ctechpay Payment Gateway</div>
            <p>Mock Payment Interface for Testing</p>
        </div>

        <div class="order-info">
            <h3>Payment Details</h3>
            <p><strong>Order Reference:</strong> {{ $orderReference }}</p>
            <p><strong>Amount:</strong> UGX 25,000</p>
            <p><strong>Description:</strong> MOAMS Membership Fee</p>
        </div>

        <p>Choose how you want to simulate the payment result:</p>

        <div class="buttons">
            <a href="{{ route('payments.ctechpay.return', ['ref' => $orderReference]) }}" 
               class="btn btn-success">
                ✅ Pay Successfully
            </a>

            <a href="{{ route('payments.ctechpay.return', ['ref' => $orderReference, 'status' => 'failed', 'error' => 'Payment declined by bank']) }}" 
               class="btn btn-danger">
                ❌ Payment Failed
            </a>

            <a href="{{ route('payments.ctechpay.return', ['ref' => $orderReference, 'status' => 'cancelled', 'cancelled' => 'true']) }}" 
               class="btn btn-warning">
                🔙 Return to Basket
            </a>

            <a href="{{ route('payments.ctechpay.return', ['ref' => $orderReference, 'success' => 'false', 'error' => 'Insufficient funds']) }}" 
               class="btn btn-secondary">
                💳 Insufficient Funds
            </a>
        </div>

        <div class="note">
            <strong>Note:</strong> This is a mock payment gateway for testing purposes. 
            In a real environment, this would be the actual Ctechpay interface where users enter their payment details.
            <br><br>
            <strong>Testing:</strong>
            <ul>
                <li><strong>Pay Successfully</strong> - Simulates successful payment</li>
                <li><strong>Payment Failed</strong> - Simulates payment failure with error</li>
                <li><strong>Return to Basket</strong> - Simulates user cancelling payment</li>
                <li><strong>Insufficient Funds</strong> - Simulates insufficient funds error</li>
            </ul>
        </div>
    </div>
</body>
</html>
