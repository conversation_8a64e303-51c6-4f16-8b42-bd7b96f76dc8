<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\UserRoleController;
use App\Http\Controllers\RegisteredUserController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\FeeSettingController;
use App\Jobs\TestQueueJob;
use Illuminate\Http\Request;
use App\Http\Controllers\RouteController;

Route::get('/', function () {
    return Inertia::render('auth/login');
})->name('home');

// Mock Ctechpay routes (outside middleware for testing)
Route::get('/ctechpay/redirect/{orderReference}', function ($orderReference) {
    return view('mock-ctechpay', ['orderReference' => $orderReference]);
})->name('ctechpay.redirect');

// Payment return route (must be outside auth middleware - users return from external gateway)
Route::get('/payments/ctechpay/return', [PaymentController::class, 'ctechpayReturn'])->name('payments.ctechpay.return');

Route::post('makePayment', [PaymentController::class, 'store'])->name('makePayment.store');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('/payments/ctechpay/initiate', [PaymentController::class, 'initiateCtechpay'])->name('payments.ctechpay.initiate');
    Route::post('/payments/{payment}/verify-manually', [PaymentController::class, 'verifyManually'])->name('payments.verify-manually');



    // Test route to verify changes are applied
    Route::get('/test-changes', function () {
        return response()->json([
            'message' => 'Changes are applied!',
            'timestamp' => now(),
            'version' => '2.0'
        ]);
    })->name('test.changes');

    // Test route to simulate payment return
    Route::get('/test-return/{orderRef}', [PaymentController::class, 'ctechpayReturn'])->name('test.return');





    // Test route to check roles
    Route::get('/test-roles', function () {
        $allRoles = \Spatie\Permission\Models\Role::all()->pluck('name')->toArray();
        $currentUser = auth()->user();
        $userRoles = $currentUser ? $currentUser->roles->pluck('name')->toArray() : [];

        return response()->json([
            'all_roles_in_system' => $allRoles,
            'current_user_email' => $currentUser?->email,
            'current_user_roles' => $userRoles,
            'has_clerk_role' => $currentUser?->hasRole('association clerk'),
            'has_manager_role' => $currentUser?->hasRole('association manager'),
            'has_any_clerk_role' => $currentUser?->hasAnyRole(['association clerk', 'association manager']),
        ]);
    })->name('test.roles');

    // Debug route for misconduct create
    Route::get('/debug-misconduct-create', function () {
        $user = auth()->user();
        if (!$user) {
            return response()->json(['error' => 'Not authenticated']);
        }

        $userRoles = $user->roles->pluck('name')->toArray();
        $hasMinibusOwnerRole = $user->hasRole('minibus owner');
        $driversCount = $user->drivers()->count();
        $drivers = $user->drivers()->active()->with('minibus')->get(['id', 'first_name', 'last_name', 'phone_number', 'minibus_id']);

        return response()->json([
            'user_email' => $user->email,
            'user_roles' => $userRoles,
            'has_minibus_owner_role' => $hasMinibusOwnerRole,
            'drivers_count' => $driversCount,
            'drivers' => $drivers,
        ]);
    })->name('debug.misconduct.create');

    // Test route to simulate payment failure


    // Fallback route for notification links - always redirects to login for proper authentication flow
    Route::get('/notification-redirect/{type}/{id}', function ($type, $id) {
        // Always store the intended URL and redirect to login, regardless of current auth state
        // This ensures proper authentication flow and role-based dashboard redirection

        $intendedUrl = null;
        switch ($type) {
            case 'driver-clearance':
                $intendedUrl = route('drivers.clearance-requests.show', $id);
                break;
            case 'transfer-request':
                $intendedUrl = route('minibuses.transfer-requests.show', $id);
                break;
            case 'member-details':
                $intendedUrl = route('membership.management.summary', $id);
                break;
            default:
                // Invalid type, redirect to login without intended URL
                return redirect()->route('login')->with('message', 'Please log in to access this page.');
        }

        if ($intendedUrl) {
            session([
                'url.intended' => $intendedUrl,
                'intended.requires_clerk_role' => true,
                'intended.type' => $type,
                'intended.id' => $id
            ]);
        }

        // Always redirect to login - let the login system handle role-based redirection
        return redirect()->route('login')->with('message', 'Please log in to access this page.');
    })->name('notification.redirect');
    Route::get('dashboard', function () {
        $user = auth()->user();
        $userRoles = $user ? $user->roles()->pluck('name')->toArray() : [];

        // Fetch real statistics from the database
        $minibuses = $drivers = $members = $memberships = $misconducts = $clearanceRequests = $transferRequests = $unpaidMemberships = $users = [];
        $stats = [
            'minibuses' => ['all' => 0, 'active' => 0, 'inactive' => 0],
            'drivers' => ['all' => 0, 'active' => 0, 'inactive' => 0],
            'members' => 0,
            'memberships' => ['unpaid' => 0],
        ];

        if (in_array('system admin', $userRoles)) {
            $users = \App\Models\User::with('roles')->get();
            $stats['users'] = [
                'all' => $users->count(),
            ];
        }
        if (in_array('association manager', $userRoles) || in_array('association clerk', $userRoles)) {
            $minibusesAll = \App\Models\Minibus::all();
            $driversAll = \App\Models\Driver::all();
            $membersAll = \App\Models\User::role('minibus owner')->get();
            $membershipsAll = \App\Models\Membership::all();
            $misconductsAll = \App\Models\Misconduct::all();
            $clearanceRequestsAll = in_array('association clerk', $userRoles) ? \App\Models\DriverClearanceRequest::where('status', 'pending')->get() : [];
            $transferRequestsAll = in_array('association clerk', $userRoles) ? \App\Models\MinibusTransferRequest::where('status', 'pending')->get() : [];
            $unpaidMembershipsAll = \App\Models\Membership::whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])->get();

            $stats['minibuses'] = [
                'all' => $minibusesAll->count(),
                'active' => $minibusesAll->where('archived', false)->count(),
                'inactive' => $minibusesAll->where('archived', true)->count(),
            ];
            $stats['drivers'] = [
                'all' => $driversAll->count(),
                'active' => $driversAll->where('archived', false)->count(),
                'inactive' => $driversAll->where('archived', true)->count(),
            ];
            $stats['members'] = $membersAll->count();
            $stats['memberships'] = [
                'unpaid' => $unpaidMembershipsAll->count(),
            ];
            $stats['misconducts'] = [
                'all' => $misconductsAll->count(),
            ];
            $stats['clearanceRequests'] = [
                'pending' => is_array($clearanceRequestsAll) ? count($clearanceRequestsAll) : $clearanceRequestsAll->count(),
            ];
            $stats['transferRequests'] = [
                'pending' => is_array($transferRequestsAll) ? count($transferRequestsAll) : $transferRequestsAll->count(),
            ];

            $minibuses = $minibusesAll;
            $drivers = $driversAll;
            $members = $membersAll;
            $memberships = $membershipsAll;
            $misconducts = $misconductsAll;
            $clearanceRequests = $clearanceRequestsAll;
            $transferRequests = $transferRequestsAll;
            $unpaidMemberships = $unpaidMembershipsAll;
        }
        if (in_array('minibus owner', $userRoles)) {
            $minibusesAll = \App\Models\Minibus::where('owner_id', $user->id)->get();
            $driversAll = \App\Models\Driver::where('owner_id', $user->id)->get();
            $unpaidMembershipsAll = $user->memberships()->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])->get();
            $stats['minibuses'] = [
                'all' => $minibusesAll->count(),
                'active' => $minibusesAll->where('archived', false)->count(),
                'inactive' => $minibusesAll->where('archived', true)->count(),
            ];
            $stats['drivers'] = [
                'all' => $driversAll->count(),
                'active' => $driversAll->where('archived', false)->count(),
                'inactive' => $driversAll->where('archived', true)->count(),
            ];
            $stats['memberships'] = [
                'unpaid' => $unpaidMembershipsAll->count(),
            ];
            $minibuses = $minibusesAll;
            $drivers = $driversAll;
            $unpaidMemberships = $unpaidMembershipsAll;
        }

        // Always pass personal details
        $personal = [
            'first_name' => $user->first_name ?? '',
            'last_name' => $user->last_name ?? '',
            'gender' => $user->gender ?? '',
            'district' => $user->district ?? '',
            'email' => $user->email ?? '',
            'phone_number' => $user->phone_number ?? '',
        ];

        $currentRegistrationFee = \App\Models\FeeSetting::getCurrentFeeAmount('registration');
        $currentAffiliationFee = \App\Models\FeeSetting::getCurrentFeeAmount('affiliation');

        return Inertia::render('dashboard', [
            'userRoles' => $userRoles,
            'auth' => ['user' => $personal],
            'user' => $user?->only([
                'id',
                'first_name',
                'last_name',
                'email',
                'phone_number',
                'gender',
                'district',
                'village',
                'created_at',
                'updated_at'
            ]),
            'minibuses' => $minibuses,
            'drivers' => $drivers,
            'members' => $members,
            'memberships' => $memberships,
            'misconducts' => $misconducts,
            'clearanceRequests' => $clearanceRequests,
            'transferRequests' => $transferRequests,
            'unpaidMemberships' => $unpaidMemberships,
            'users' => $users,
            'stats' => $stats,
            'currentFees' => [
                'registration' => $currentRegistrationFee,
                'affiliation' => $currentAffiliationFee,
            ],
        ]);
    })->name('dashboard');

    // Fee Management Routes (Association Managers only)
    Route::middleware(['auth', 'role:association manager'])->group(function () {
        Route::get('fee-settings/history/{feeType}', [FeeSettingController::class, 'getFeeHistory'])->name('fee-settings.history');
        Route::resource('fee-settings', FeeSettingController::class);
    });


});

Route::middleware(['auth', 'verified', 'role:system admin'])->group(function () {
    // User Management Routes
    Route::get('/admin/users', [RegisteredUserController::class, 'index'])->name('admin.users');
    Route::get('/admin/users/{user}', [RegisteredUserController::class, 'show'])->name('admin.users.show');
    Route::get('/admin/users/{user}/edit', [RegisteredUserController::class, 'edit'])->name('admin.users.edit');
    Route::put('/admin/users/{user}', [RegisteredUserController::class, 'update'])->name('admin.users.update');
    Route::patch('/admin/users/{user}', [RegisteredUserController::class, 'update'])->name('admin.users.patch');
    Route::delete('/admin/users/{user}', [RegisteredUserController::class, 'destroy'])->name('admin.users.destroy');
    Route::get('/admin/create-user', [RegisteredUserController::class, 'create'])->name('admin.createUser');
    Route::post('/admin/create-user', [RegisteredUserController::class, 'storeUser'])->name('admin.storeUser');
    Route::get('/admin/users/{user}/unpaid-memberships', [PaymentController::class, 'unpaidMemberships'])->name('admin.users.unpaidMemberships');
    Route::get('/admin/users/{user}/membership-summary', [PaymentController::class, 'membershipSummary'])->name('admin.users.membershipSummary');
    Route::put('/memberships/{membership}/mark-paid', [\App\Http\Controllers\PaymentController::class, 'markPaid'])->name('memberships.markPaid');

    // Role Management Routes (keeping existing)
    Route::get('/users', [UserRoleController::class, 'index'])->name('users');
    Route::get('/roles', [UserRoleController::class, 'roles']);
    Route::post('/users/{user}/roles', [UserRoleController::class, 'update']);
});

// API routes for async searches
Route::get('/api/minibus-owners', [UserController::class, 'searchMinibusOwners']);
Route::get('/api/drivers', [UserController::class, 'searchDrivers']);
Route::get('/api/vehicle-routes', [RouteController::class, 'apiIndex']);
Route::get('/api/minibuses', [\App\Http\Controllers\MinibusController::class, 'apiIndex']);

// Notification API routes
Route::middleware(['auth'])->group(function () {
    Route::get('/api/notifications/unread', [\App\Http\Controllers\NotificationController::class, 'getUnread']);
    Route::get('/api/notifications/all', [\App\Http\Controllers\NotificationController::class, 'getAll']);
    Route::post('/api/notifications/{notification}/read', [\App\Http\Controllers\NotificationController::class, 'markAsRead']);
    Route::post('/api/notifications/mark-all-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsRead']);
    Route::delete('/api/notifications/{notification}', [\App\Http\Controllers\NotificationController::class, 'delete']);
});

// Fallback route for Inertia 404s
Route::fallback(function (Request $request) {
    if ($request->hasHeader('X-Inertia')) {
        return Inertia::render('error', [
            'status' => 404,
            'title' => 'Page Not Found',
            'description' => 'The page you are looking for does not exist.'
        ])->toResponse($request)->setStatusCode(404);
    }
    abort(404);
});

Route::get('/membership-management/view-id', function (Request $request) {
    $user = auth()->user();

    // Only allow association clerks and system admins to view files
    if (!$user || !$user->hasAnyRole(['association clerk', 'system admin'])) {
        abort(403, 'Unauthorized access to files.');
    }

    $file = $request->query('file');
    $type = $request->query('type', 'image');
    $fileUrl = '/storage/' . ltrim($file, '/');
    return Inertia::render('MembershipManagement/ImageViewer', [
        'fileUrl' => $fileUrl,
        'fileType' => $type,
    ]);
})->middleware('auth');

// Payment Analytics Routes (only for association clerks and system admins)
Route::middleware(['role:system admin|association clerk'])->group(function () {
    Route::get('/payment-analytics', [\App\Http\Controllers\PaymentAnalyticsController::class, 'index'])->name('payment-analytics.index');
    Route::get('/payment-analytics/report', [\App\Http\Controllers\PaymentAnalyticsController::class, 'generateReport'])->name('payment-analytics.report');
});

Route::middleware(['auth', 'role:association clerk|association manager'])->group(function () {
    Route::get('/routes', [RouteController::class, 'index'])->name('routes.index');
    Route::post('/routes', [RouteController::class, 'store'])->name('routes.store');
    Route::post('/routes/{route}/update', [RouteController::class, 'update'])->name('routes.update');
    Route::post('/routes/{route}/delete', [RouteController::class, 'destroy'])->name('routes.destroy');
    Route::post('/routes/{route}/deactivate', [RouteController::class, 'deactivate'])->name('routes.deactivate');
    Route::post('/routes/{route}/activate', [RouteController::class, 'activate'])->name('routes.activate');
    Route::get('/routes/export', [RouteController::class, 'export'])->name('routes.export');
});



// Test route to access mock Ctechpay without order reference
Route::get('/ctechpay/test', function () {
    return view('mock-ctechpay', ['orderReference' => 'test-order-' . time()]);
});

// Debug route to test URL generation
Route::get('/debug-payment-url/{orderRef}', function ($orderRef) {
    $mockPaymentUrl = url('/ctechpay/redirect/' . $orderRef);
    return response()->json([
        'order_ref' => $orderRef,
        'generated_url' => $mockPaymentUrl,
        'url_parts' => parse_url($mockPaymentUrl),
        'route_exists' => \Route::has('ctechpay.redirect'),
        'all_routes' => collect(\Route::getRoutes())->filter(function ($route) {
            return str_contains($route->uri(), 'ctechpay');
        })->map(function ($route) {
            return [
                'uri' => $route->uri(),
                'methods' => $route->methods(),
                'name' => $route->getName()
            ];
        })->values()
    ]);
});

// Test route for payment initiation (bypasses CSRF for testing)
Route::post('/test-payment-initiate', [PaymentController::class, 'initiateCtechpay'])->withoutMiddleware(['web']);




Route::get('/payments/ctechpay/cancel', function () {
    return inertia('paymentManagement/ctechpay-result', [
        'success' => false,
        'message' => 'Payment was cancelled by the user.'
    ]);
})->name('payments.ctechpay.cancel');

require __DIR__ . '/userManag.php';
require __DIR__ . '/driverManag.php';
require __DIR__ . '/minibusManag.php';
require __DIR__ . '/misconductManag.php';
require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
require __DIR__ . '/membershipManag.php';