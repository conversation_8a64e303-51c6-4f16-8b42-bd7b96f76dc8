<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($title); ?></title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #1e40af;
        }
        
        .report-header h1 {
            color: #1e40af;
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        
        .report-header .company-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-header .company-details {
            font-size: 12px;
            color: #6b7280;
        }
        
        .report-meta {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: table;
            width: 100%;
        }
        
        .meta-item {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 0 10px;
        }
        
        .meta-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .meta-value {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: #1e40af;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .summary-item {
            display: table-cell;
            width: 25%;
            text-align: center;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
        }
        
        .summary-item:first-child {
            border-radius: 8px 0 0 8px;
        }
        
        .summary-item:last-child {
            border-radius: 0 8px 8px 0;
        }
        
        .summary-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
            margin-top: 5px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background: #1e40af;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 13px;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .data-table tr:hover {
            background: #e5e7eb;
        }
        
        .amount {
            text-align: right;
            font-weight: bold;
            color: #059669;
        }
        
        .count {
            text-align: center;
            font-weight: bold;
        }
        
        .payment-methods {
            margin-bottom: 30px;
        }
        
        .method-item {
            display: table;
            width: 100%;
            margin-bottom: 10px;
            background: #f8fafc;
            border-radius: 6px;
            padding: 10px;
        }
        
        .method-name {
            display: table-cell;
            width: 40%;
            font-weight: bold;
            color: #374151;
        }
        
        .method-stats {
            display: table-cell;
            width: 60%;
            text-align: right;
        }
        
        .top-members {
            margin-bottom: 30px;
        }
        
        .member-item {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
        }
        
        .member-name {
            display: table-cell;
            width: 50%;
            font-weight: bold;
        }
        
        .member-stats {
            display: table-cell;
            width: 50%;
            text-align: right;
            color: #059669;
            font-weight: bold;
        }
        
        .report-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Report Header -->
        <div class="report-header">
            <h1><?php echo e($title); ?></h1>
            <div class="company-name"><?php echo e($company_info['name']); ?></div>
            <div class="company-details">
                <?php echo e($company_info['address']); ?><br>
                Phone: <?php echo e($company_info['phone']); ?> | Email: <?php echo e($company_info['email']); ?>

            </div>
        </div>
        
        <!-- Report Metadata -->
        <div class="report-meta">
            <div class="meta-item">
                <div class="meta-label">Report Period</div>
                <div class="meta-value"><?php echo e(ucfirst($period)); ?></div>
            </div>
            <div class="meta-item">
                <div class="meta-label">Generated By</div>
                <div class="meta-value"><?php echo e($generated_by); ?></div>
            </div>
            <div class="meta-item">
                <div class="meta-label">Generated On</div>
                <div class="meta-value"><?php echo e($generated_at); ?></div>
            </div>
        </div>
        
        <!-- Summary Section -->
        <div class="summary-section">
            <h2 class="section-title">Financial Summary</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">Total Revenue</div>
                    <div class="summary-value">MWK <?php echo e(number_format($summary['total_revenue'], 2)); ?></div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Total Payments</div>
                    <div class="summary-value"><?php echo e(number_format($summary['total_payments'])); ?></div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Registration Fees</div>
                    <div class="summary-value">MWK <?php echo e(number_format($summary['registration_revenue'], 2)); ?></div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Affiliation Fees</div>
                    <div class="summary-value">MWK <?php echo e(number_format($summary['affiliation_revenue'], 2)); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Detailed Data -->
        <div class="data-section">
            <h2 class="section-title">Detailed <?php echo e(ucfirst($period)); ?> Data</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <?php if($period === 'daily'): ?>
                            <th>Date</th>
                        <?php elseif($period === 'weekly'): ?>
                            <th>Week Period</th>
                        <?php elseif($period === 'monthly'): ?>
                            <th>Month</th>
                        <?php else: ?>
                            <th>Year</th>
                        <?php endif; ?>
                        <th>Total Payments</th>
                        <th>Total Amount</th>
                        <th>Registration Fees</th>
                        <th>Affiliation Fees</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <?php if($period === 'daily'): ?>
                                    <?php echo e(\Carbon\Carbon::parse($row->date)->format('M j, Y')); ?>

                                <?php elseif($period === 'weekly'): ?>
                                    <?php echo e(\Carbon\Carbon::parse($row->week_start)->format('M j')); ?> - <?php echo e(\Carbon\Carbon::parse($row->week_end)->format('M j, Y')); ?>

                                <?php elseif($period === 'monthly'): ?>
                                    <?php echo e(\Carbon\Carbon::create(null, $row->month, 1)->format('F')); ?>

                                <?php else: ?>
                                    <?php echo e($row->year); ?>

                                <?php endif; ?>
                            </td>
                            <td class="count"><?php echo e(number_format($row->total_payments)); ?></td>
                            <td class="amount">MWK <?php echo e(number_format($row->total_amount, 2)); ?></td>
                            <td class="amount">MWK <?php echo e(number_format($row->registration_amount, 2)); ?></td>
                            <td class="amount">MWK <?php echo e(number_format($row->affiliation_amount, 2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
        <!-- Payment Methods Breakdown -->
        <?php if($payment_methods->isNotEmpty()): ?>
            <div class="payment-methods">
                <h2 class="section-title">Payment Methods Breakdown</h2>
                <?php $__currentLoopData = $payment_methods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="method-item">
                        <div class="method-name"><?php echo e(ucfirst(str_replace('_', ' ', $method->payment_method))); ?></div>
                        <div class="method-stats">
                            <?php echo e(number_format($method->count)); ?> payments | MWK <?php echo e(number_format($method->total_amount, 2)); ?>

                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
        
        <!-- Top Paying Members -->
        <?php if($top_members->isNotEmpty()): ?>
            <div class="top-members">
                <h2 class="section-title">Top Paying Members</h2>
                <?php $__currentLoopData = $top_members; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="member-item">
                        <div class="member-name">
                            <?php echo e($member->user->first_name); ?> <?php echo e($member->user->last_name); ?>

                            <br><small style="color: #6b7280;"><?php echo e($member->user->email); ?></small>
                        </div>
                        <div class="member-stats">
                            <?php echo e(number_format($member->payment_count)); ?> payments | MWK <?php echo e(number_format($member->total_paid, 2)); ?>

                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
        
        <!-- Report Footer -->
        <div class="report-footer">
            <p><strong>Confidential Financial Report</strong></p>
            <p>This report contains confidential financial information and should be handled according to organizational policies.</p>
            <p>Generated by MOAMS Financial Management System</p>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\MoamProject\moams\resources\views/reports/financial-report.blade.php ENDPATH**/ ?>