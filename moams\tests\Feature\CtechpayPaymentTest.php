<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Membership;
use App\Models\Payment;
use App\Models\FeeSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class CtechpayPaymentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test admin user for fee settings
        $adminUser = User::factory()->create();

        // Create test fee settings
        FeeSetting::create([
            'fee_type' => 'registration',
            'amount' => 100.00,
            'is_active' => true,
            'effective_from' => now()->subDays(30),
            'created_by' => $adminUser->id,
        ]);

        FeeSetting::create([
            'fee_type' => 'affiliation',
            'amount' => 200.00,
            'is_active' => true,
            'effective_from' => now()->subDays(30),
            'created_by' => $adminUser->id,
        ]);
    }

    public function test_ctechpay_payment_initiation()
    {
        // Create test user and membership
        $user = User::factory()->create();
        $membership = Membership::create([
            'user_id' => $user->id,
            'type' => 'Registration',
            'status' => 'Unregistered',
            'start_date' => now(),
            'end_date' => now()->addYear(),
        ]);

        // Mock Ctechpay API response
        Http::fake([
            'https://api-sandbox.ctechpay.com/*' => Http::response([
                'order_reference' => 'test-order-123',
                'payment_page_URL' => 'https://paypage.sandbox.com/?code=test123'
            ], 200)
        ]);

        // Initiate payment
        $response = $this->postJson(route('payments.ctechpay.initiate'), [
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'fee_type' => 'registration',
            'amount' => 100,
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure(['payment_page_url']);

        // Verify payment record was created
        $this->assertDatabaseHas('payments', [
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'amount' => 100,
            'fee_type' => 'registration',
            'status' => 'pending',
            'payment_method' => 'ctechpay',
            'order_reference' => 'test-order-123',
        ]);
    }

    public function test_ctechpay_payment_return_success()
    {
        // Create test payment
        $user = User::factory()->create();
        $membership = Membership::create([
            'user_id' => $user->id,
            'type' => 'Registration',
            'status' => 'Unregistered',
            'start_date' => now(),
            'end_date' => now()->addYear(),
        ]);

        $payment = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'amount' => 100,
            'fee_type' => 'registration',
            'status' => 'pending',
            'payment_method' => 'ctechpay',
            'order_reference' => 'test-order-123',
        ]);

        // Mock Ctechpay status check response
        Http::fake([
            'https://api-sandbox.ctechpay.com/get_order_status/' => Http::response([
                '_id' => 'test-order-123',
                'orderReference' => 'test-order-123',
                'status' => 'PURCHASED',
                'currencyCode' => 'MWK',
                'amount' => 100,
                'formattedAmount' => 'MWK 100',
                'cardHolderName' => 'TEST USER'
            ], 200)
        ]);

        // Test the controller method directly instead of making HTTP request
        $controller = new \App\Http\Controllers\PaymentController();
        $request = new \Illuminate\Http\Request(['ref' => 'test-order-123']);

        $response = $controller->ctechpayReturn($request);

        // Verify payment was updated
        $payment->refresh();
        $this->assertEquals('fulfilled', $payment->status);
        $this->assertNotNull($payment->paid_at);
        $this->assertEquals('MWK', $payment->currency_code);
        $this->assertEquals('MWK 100', $payment->formatted_amount);
        $this->assertEquals('TEST USER', $payment->card_holder_name);

        // Verify membership status was updated
        $membership->refresh();
        $this->assertEquals('Registered', $membership->status);
    }

    public function test_ctechpay_payment_return_failed()
    {
        // Create test payment
        $user = User::factory()->create();
        $membership = Membership::create([
            'user_id' => $user->id,
            'type' => 'Registration',
            'status' => 'Unregistered',
            'start_date' => now(),
            'end_date' => now()->addYear(),
        ]);

        $payment = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'amount' => 100,
            'fee_type' => 'registration',
            'status' => 'pending',
            'payment_method' => 'ctechpay',
            'order_reference' => 'test-order-123',
        ]);

        // Mock Ctechpay status check response for failed payment
        Http::fake([
            'https://api-sandbox.ctechpay.com/get_order_status/' => Http::response([
                '_id' => 'test-order-123',
                'orderReference' => 'test-order-123',
                'status' => 'FAILED',
                'currencyCode' => 'MWK',
                'amount' => 100,
                'formattedAmount' => 'MWK 100',
            ], 200)
        ]);

        // Test the controller method directly instead of making HTTP request
        $controller = new \App\Http\Controllers\PaymentController();
        $request = new \Illuminate\Http\Request(['ref' => 'test-order-123']);

        $controller->ctechpayReturn($request);

        // Verify payment status remains pending
        $payment->refresh();
        $this->assertEquals('pending', $payment->status);
        $this->assertNull($payment->paid_at);

        // Verify membership status unchanged
        $membership->refresh();
        $this->assertEquals('Unregistered', $membership->status);
    }
}
